/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_VN = {
    // Set Filter
    selectAll: '(<PERSON><PERSON><PERSON>)',
    selectAllSearchResults: '(<PERSON><PERSON><PERSON>ác <PERSON>ết Quả <PERSON>ì<PERSON>)',
    addCurrentSelectionToFilter: 'Thêm lựa chọn hiện tại vào bộ lọc',
    searchOoo: 'Tìm kiếm...',
    blanks: '(Trống)',
    noMatches: 'Không có kết quả',

    // Number Filter & Text Filter
    filterOoo: 'Lọc...',
    equals: 'Bằng',
    notEqual: 'Không bằng',
    blank: 'Trống',
    notBlank: 'Không trống',
    empty: 'Chọn một',

    // Number Filter
    lessThan: 'Nhỏ hơn',
    greaterThan: 'Lớn hơn',
    lessThanOrEqual: 'Nhỏ hơn hoặc bằng',
    greaterThanOrEqual: 'Lớn hơn hoặc bằng',
    inRange: 'Trong khoảng',
    inRangeStart: 'Từ',
    inRangeEnd: 'Đến',

    // Text Filter
    contains: 'Chứa',
    notContains: 'Không chứa',
    startsWith: 'Bắt đầu với',
    endsWith: 'Kết thúc với',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Trước',
    after: 'Sau',

    // Filter Conditions
    andCondition: 'VÀ',
    orCondition: 'HOẶC',

    // Filter Buttons
    applyFilter: 'Áp dụng',
    resetFilter: 'Đặt lại',
    clearFilter: 'Xóa',
    cancelFilter: 'Hủy',

    // Filter Titles
    textFilter: 'Bộ lọc văn bản',
    numberFilter: 'Bộ lọc số',
    dateFilter: 'Bộ lọc ngày tháng',
    setFilter: 'Bộ lọc tập hợp',

    // Group Column Filter
    groupFilterSelect: 'Chọn trường:',

    // New Filter Tool Panel
    filterSummaryInactive: 'là (Tất cả)',
    filterSummaryContains: 'chứa',
    filterSummaryNotContains: 'không chứa',
    filterSummaryTextEquals: 'bằng',
    filterSummaryTextNotEqual: 'không bằng',
    filterSummaryStartsWith: 'bắt đầu với',
    filterSummaryEndsWith: 'kết thúc với',
    filterSummaryBlank: 'trống',
    filterSummaryNotBlank: 'không trống',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'giữa',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'là (Tất cả)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'là (${variable})',
    filterSummaryListLong: 'là (${variable}) và ${variable} nữa',
    addFilterCard: 'Thêm Bộ Lọc',
    agTextColumnFilterDisplayName: 'Bộ Lọc Đơn Giản',
    agNumberColumnFilterDisplayName: 'Bộ Lọc Đơn Giản',
    agDateColumnFilterDisplayName: 'Bộ Lọc Đơn Giản',
    agSetColumnFilterDisplayName: 'Bộ Lọc Lựa Chọn',
    agMultiColumnFilterDisplayName: 'Bộ Lọc Kết Hợp',
    addFilterPlaceholder: 'Tìm kiếm cột...',

    // Advanced Filter
    advancedFilterContains: 'chứa',
    advancedFilterNotContains: 'không chứa',
    advancedFilterTextEquals: 'bằng',
    advancedFilterTextNotEqual: 'không bằng',
    advancedFilterStartsWith: 'bắt đầu với',
    advancedFilterEndsWith: 'kết thúc với',
    advancedFilterBlank: 'trống',
    advancedFilterNotBlank: 'không trống',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'đúng',
    advancedFilterFalse: 'sai',
    advancedFilterAnd: 'VÀ',
    advancedFilterOr: 'HOẶC',
    advancedFilterApply: 'Áp dụng',
    advancedFilterBuilder: 'Trình tạo',
    advancedFilterValidationMissingColumn: 'Thiếu cột',
    advancedFilterValidationMissingOption: 'Thiếu tùy chọn',
    advancedFilterValidationMissingValue: 'Thiếu giá trị',
    advancedFilterValidationInvalidColumn: 'Không tìm thấy cột',
    advancedFilterValidationInvalidOption: 'Không tìm thấy tùy chọn',
    advancedFilterValidationMissingQuote: 'Giá trị thiếu dấu ngoặc kép',
    advancedFilterValidationNotANumber: 'Giá trị không phải là một số',
    advancedFilterValidationInvalidDate: 'Giá trị không phải là ngày hợp lệ',
    advancedFilterValidationMissingCondition: 'Thiếu điều kiện',
    advancedFilterValidationJoinOperatorMismatch: 'Các toán tử kết nối trong một điều kiện phải giống nhau',
    advancedFilterValidationInvalidJoinOperator: 'Không tìm thấy toán tử kết nối',
    advancedFilterValidationMissingEndBracket: 'Thiếu dấu ngoặc kết thúc',
    advancedFilterValidationExtraEndBracket: 'Quá nhiều dấu ngoặc kết thúc',
    advancedFilterValidationMessage: 'Biểu thức có lỗi. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Biểu thức có lỗi. ${variable} ở cuối biểu thức.',
    advancedFilterBuilderTitle: 'Bộ lọc Nâng cao',
    advancedFilterBuilderApply: 'Áp dụng',
    advancedFilterBuilderCancel: 'Hủy',
    advancedFilterBuilderAddButtonTooltip: 'Thêm Bộ lọc hoặc Nhóm',
    advancedFilterBuilderRemoveButtonTooltip: 'Xóa bỏ',
    advancedFilterBuilderMoveUpButtonTooltip: 'Di chuyển lên',
    advancedFilterBuilderMoveDownButtonTooltip: 'Di chuyển xuống',
    advancedFilterBuilderAddJoin: 'Thêm Nhóm',
    advancedFilterBuilderAddCondition: 'Thêm Bộ lọc',
    advancedFilterBuilderSelectColumn: 'Chọn một cột',
    advancedFilterBuilderSelectOption: 'Chọn một tùy chọn',
    advancedFilterBuilderEnterValue: 'Nhập một giá trị...',
    advancedFilterBuilderValidationAlreadyApplied: 'Bộ lọc hiện tại đã được áp dụng.',
    advancedFilterBuilderValidationIncomplete: 'Không phải tất cả điều kiện đều đã hoàn thành.',
    advancedFilterBuilderValidationSelectColumn: 'Phải chọn một cột.',
    advancedFilterBuilderValidationSelectOption: 'Phải chọn một tùy chọn.',
    advancedFilterBuilderValidationEnterValue: 'Phải nhập một giá trị.',

    // Editor Validation Errors
    minDateValidation: 'Ngày phải sau ${variable}',
    maxDateValidation: 'Ngày phải trước ${variable}',
    maxLengthValidation: 'Phải có ${variable} ký tự hoặc ít hơn.',
    minValueValidation: 'Phải lớn hơn hoặc bằng ${variable}',
    maxValueValidation: 'Phải nhỏ hơn hoặc bằng ${variable}',
    invalidSelectionValidation: 'Lựa chọn không hợp lệ.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Cột',
    filters: 'Bộ lọc',

    // columns tool panel
    pivotMode: 'Chế độ Pivot',
    groups: 'Nhóm hàng',
    rowGroupColumnsEmptyMessage: 'Kéo vào đây để thiết lập các nhóm hàng',
    values: 'Giá trị',
    valueColumnsEmptyMessage: 'Kéo vào đây để tổng hợp',
    pivots: 'Nhãn cột',
    pivotColumnsEmptyMessage: 'Kéo vào đây để thiết lập nhãn cột',

    // Header of the Default Group Column
    group: 'Nhóm',

    // Row Drag
    rowDragRow: 'hàng',
    rowDragRows: 'các hàng',

    // Other
    loadingOoo: 'Đang tải...',
    loadingError: 'LỖI',
    noRowsToShow: 'Không có hàng để hiển thị',
    enabled: 'Đã bật',

    // Menu
    pinColumn: 'Ghim Cột',
    pinLeft: 'Ghim Trái',
    pinRight: 'Ghim Phải',
    noPin: 'Không Ghim',
    valueAggregation: 'Tổng Hợp Giá Trị',
    noAggregation: 'Không',
    autosizeThisColumn: 'Tự Điều Chỉnh Kích Thước Cột Này',
    autosizeAllColumns: 'Tự Điều Chỉnh Kích Thước Tất Cả Các Cột',
    groupBy: 'Nhóm Theo',
    ungroupBy: 'Bỏ Nhóm Theo',
    ungroupAll: 'Bỏ Nhóm Tất Cả',
    addToValues: 'Thêm ${variable} vào giá trị',
    removeFromValues: 'Xóa ${variable} khỏi giá trị',
    addToLabels: 'Thêm ${variable} vào nhãn',
    removeFromLabels: 'Xóa ${variable} khỏi nhãn',
    resetColumns: 'Đặt Lại Cột',
    expandAll: 'Mở Rộng Tất Cả Các Nhóm Hàng',
    collapseAll: 'Đóng Tất Cả Các Nhóm Hàng',
    copy: 'Sao Chép',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Sao Chép Kèm Tiêu Đề',
    copyWithGroupHeaders: 'Sao Chép Kèm Tiêu Đề Nhóm',
    cut: 'Cắt',
    paste: 'Dán',
    ctrlV: 'Ctrl+V',
    export: 'Xuất',
    csvExport: 'Xuất CSV',
    excelExport: 'Xuất Excel',
    columnFilter: 'Bộ Lọc Cột',
    columnChooser: 'Chọn Cột',
    chooseColumns: 'Chọn Cột',
    sortAscending: 'Sắp Xếp Tăng Dần',
    sortDescending: 'Sắp Xếp Giảm Dần',
    sortUnSort: 'Xóa Sắp Xếp',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Tổng',
    first: 'Đầu tiên',
    last: 'Cuối cùng',
    min: 'Nhỏ nhất',
    max: 'Lớn nhất',
    none: 'Không có',
    count: 'Đếm',
    avg: 'Trung bình',
    filteredRows: 'Đã lọc',
    selectedRows: 'Đã chọn',
    totalRows: 'Tổng số hàng',
    totalAndFilteredRows: 'Hàng',
    more: 'Thêm',
    to: 'đến',
    of: 'trong',
    page: 'Trang',
    pageLastRowUnknown: '?',
    nextPage: 'Trang kế',
    lastPage: 'Trang cuối',
    firstPage: 'Trang đầu',
    previousPage: 'Trang trước',
    pageSizeSelectorLabel: 'Kích cỡ trang:',
    footerTotal: 'Tổng',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Cuộn ${variable} vào tầm nhìn',

    // Pivoting
    pivotColumnGroupTotals: 'Tổng cộng',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Biểu đồ Pivot & Chế độ Pivot',
    pivotChart: 'Biểu đồ Pivot',
    chartRange: 'Phạm vi Biểu đồ',
    columnChart: 'Cột',
    groupedColumn: 'Nhóm',
    stackedColumn: 'Xếp chồng',
    normalizedColumn: 'Xếp chồng 100%',
    barChart: 'Thanh',
    groupedBar: 'Nhóm',
    stackedBar: 'Xếp chồng',
    normalizedBar: 'Xếp chồng 100%',
    pieChart: 'Biểu đồ Tròn',
    pie: 'Tròn',
    donut: 'Vòng',
    lineChart: 'Đường',
    stackedLine: 'Xếp chồng',
    normalizedLine: 'Xếp chồng 100%',
    xyChart: 'X Y (Phân tán)',
    scatter: 'Phân tán',
    bubble: 'Bong bóng',
    areaChart: 'Khu vực',
    area: 'Khu vực',
    stackedArea: 'Xếp chồng',
    normalizedArea: 'Xếp chồng 100%',
    histogramChart: 'Biểu đồ Tần số',
    polarChart: 'Biểu đồ Cực',
    radarLine: 'Đường Radar',
    radarArea: 'Khu vực Radar',
    nightingale: 'Nightingale',
    radialColumn: 'Cột Radial',
    radialBar: 'Thanh Radial',
    statisticalChart: 'Thống kê',
    boxPlot: 'Biểu đồ Hộp',
    rangeBar: 'Thanh Phạm vi',
    rangeArea: 'Khu vực Phạm vi',
    hierarchicalChart: 'Phân cấp',
    treemap: 'Bản đồ Cây',
    sunburst: 'Sunburst',
    specializedChart: 'Chuyên biệt',
    waterfall: 'Thác nước',
    heatmap: 'Bản đồ Nhiệt',
    combinationChart: 'Kết hợp',
    columnLineCombo: 'Cột & Đường',
    AreaColumnCombo: 'Khu vực & Cột',

    // Charts
    pivotChartTitle: 'Biểu đồ Pivot',
    rangeChartTitle: 'Biểu đồ Phạm vi',
    settings: 'Biểu đồ',
    data: 'Thiết lập',
    format: 'Tùy chỉnh',
    categories: 'Danh mục',
    defaultCategory: '(Không có)',
    series: 'Chuỗi',
    switchCategorySeries: 'Chuyển đổi Danh mục / Chuỗi',
    categoryValues: 'Giá trị Danh mục',
    seriesLabels: 'Nhãn Chuỗi',
    aggregate: 'Tổng hợp',
    xyValues: 'Giá trị X Y',
    paired: 'Chế độ Ghép đôi',
    axis: 'Trục',
    xAxis: 'Trục Ngang',
    yAxis: 'Trục Dọc',
    polarAxis: 'Trục Cực',
    radiusAxis: 'Trục Bán kính',
    navigator: 'Định hướng',
    zoom: 'Thu phóng',
    animation: 'Hoạt ảnh',
    crosshair: 'Đường cắt chéo',
    color: 'Màu sắc',
    thickness: 'Độ dày',
    preferredLength: 'Chiều dài Ưa thích',
    xType: 'Loại X',
    axisType: 'Loại Trục',
    automatic: 'Tự động',
    category: 'Danh mục',
    number: 'Số',
    time: 'Thời gian',
    timeFormat: 'Định dạng Thời gian',
    autoRotate: 'Tự động Xoay',
    labelRotation: 'Xoay nhãn',
    circle: 'Hình tròn',
    polygon: 'Hình đa giác',
    square: 'Hình vuông',
    cross: 'Hình chữ thập',
    diamond: 'Hình kim cương',
    plus: 'Dấu cộng',
    triangle: 'Hình tam giác',
    heart: 'Hình trái tim',
    orientation: 'Hướng',
    fixed: 'Cố định',
    parallel: 'Song song',
    perpendicular: 'Vuông góc',
    radiusAxisPosition: 'Vị trí',
    ticks: 'Vạch',
    gridLines: 'Đường lưới',
    width: 'Chiều rộng',
    height: 'Chiều cao',
    length: 'Chiều dài',
    padding: 'Đệm',
    spacing: 'Khoảng cách',
    chartStyle: 'Phong cách Biểu đồ',
    title: 'Tiêu đề',
    chartTitles: 'Tiêu đề Biểu đồ',
    chartTitle: 'Tiêu đề Biểu đồ',
    chartSubtitle: 'Phụ đề',
    horizontalAxisTitle: 'Tiêu đề Trục Ngang',
    verticalAxisTitle: 'Tiêu đề Trục Dọc',
    polarAxisTitle: 'Tiêu đề Trục Cực',
    titlePlaceholder: 'Tiêu đề Biểu đồ',
    background: 'Nền',
    font: 'Phông chữ',
    weight: 'Trọng lượng',
    top: 'Trên',
    right: 'Phải',
    bottom: 'Dưới',
    left: 'Trái',
    labels: 'Nhãn',
    calloutLabels: 'Nhãn Gọi ra',
    sectorLabels: 'Nhãn Khu vực',
    positionRatio: 'Tỷ lệ Vị trí',
    size: 'Kích thước',
    shape: 'Hình dạng',
    minSize: 'Kích thước Tối thiểu',
    maxSize: 'Kích thước Tối đa',
    legend: 'Chú giải',
    position: 'Vị trí',
    markerSize: 'Kích thước Đánh dấu',
    markerStroke: 'Đường viền Đánh dấu',
    markerPadding: 'Đệm Đánh dấu',
    itemSpacing: 'Khoảng cách Mục',
    itemPaddingX: 'Đệm Mục X',
    itemPaddingY: 'Đệm Mục Y',
    layoutHorizontalSpacing: 'Khoảng cách Ngang',
    layoutVerticalSpacing: 'Khoảng cách Dọc',
    strokeWidth: 'Độ rộng Đường',
    offset: 'Độ lệch',
    offsets: 'Độ lệch',
    tooltips: 'Chú thích',
    callout: 'Gọi ra',
    markers: 'Đánh dấu',
    shadow: 'Bóng',
    blur: 'Mờ',
    xOffset: 'Độ lệch X',
    yOffset: 'Độ lệch Y',
    lineWidth: 'Độ rộng Đường',
    lineDash: 'Đường Dash',
    lineDashOffset: 'Độ lệch Dash',
    scrollingZoom: 'Cuộn',
    scrollingStep: 'Bước Cuộn',
    selectingZoom: 'Chọn',
    durationMillis: 'Thời lượng (ms)',
    crosshairLabel: 'Nhãn',
    crosshairSnap: 'Bắt điểm',
    normal: 'Bình thường',
    bold: 'Đậm',
    italic: 'Nghiêng',
    boldItalic: 'Đậm Nghiêng',
    predefined: 'Định nghĩa trước',
    fillOpacity: 'Độ mờ Đổ',
    strokeColor: 'Màu Đường',
    strokeOpacity: 'Độ mờ Đường',
    miniChart: 'Biểu đồ Nhỏ',
    histogramBinCount: 'Số lượng Bin',
    connectorLine: 'Đường Kết nối',
    seriesItems: 'Mục Chuỗi',
    seriesItemType: 'Loại Mục',
    seriesItemPositive: 'Tích cực',
    seriesItemNegative: 'Tiêu cực',
    seriesItemLabels: 'Nhãn Mục',
    columnGroup: 'Cột',
    barGroup: 'Thanh',
    pieGroup: 'Biểu đồ Tròn',
    lineGroup: 'Đường',
    scatterGroup: 'X Y (Phân tán)',
    areaGroup: 'Khu vực',
    polarGroup: 'Cực',
    statisticalGroup: 'Thống kê',
    hierarchicalGroup: 'Phân cấp',
    specializedGroup: 'Chuyên biệt',
    combinationGroup: 'Kết hợp',
    groupedColumnTooltip: 'Nhóm',
    stackedColumnTooltip: 'Xếp chồng',
    normalizedColumnTooltip: 'Xếp chồng 100%',
    groupedBarTooltip: 'Nhóm',
    stackedBarTooltip: 'Xếp chồng',
    normalizedBarTooltip: 'Xếp chồng 100%',
    pieTooltip: 'Biểu đồ Tròn',
    donutTooltip: 'Biểu đồ Vòng',
    lineTooltip: 'Đường',
    stackedLineTooltip: 'Xếp chồng',
    normalizedLineTooltip: 'Xếp chồng 100%',
    groupedAreaTooltip: 'Khu vực',
    stackedAreaTooltip: 'Xếp chồng',
    normalizedAreaTooltip: 'Xếp chồng 100%',
    scatterTooltip: 'Phân tán',
    bubbleTooltip: 'Bong bóng',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Cột Xuyên tâm',
    radialBarTooltip: 'Thanh Xuyên tâm',
    radarLineTooltip: 'Đường Radar',
    radarAreaTooltip: 'Khu vực Radar',
    nightingaleTooltip: 'Biểu đồ Nightingale',
    rangeBarTooltip: 'Thanh Phạm vi',
    rangeAreaTooltip: 'Khu vực Phạm vi',
    boxPlotTooltip: 'Biểu đồ Hộp',
    treemapTooltip: 'Biểu đồ Cây',
    sunburstTooltip: 'Biểu đồ Sunburst',
    waterfallTooltip: 'Biểu đồ Thác nước',
    heatmapTooltip: 'Biểu đồ Nhiệt',
    columnLineComboTooltip: 'Cột & Đường',
    areaColumnComboTooltip: 'Khu vực & Cột',
    customComboTooltip: 'Kết hợp Tùy chỉnh',
    innerRadius: 'Bán kính Trong',
    startAngle: 'Góc Bắt đầu',
    endAngle: 'Góc Kết thúc',
    reverseDirection: 'Đảo ngược Hướng',
    groupPadding: 'Đệm Nhóm',
    seriesPadding: 'Đệm Chuỗi',
    tile: 'Ô',
    whisker: 'Râu',
    cap: 'Nắp',
    capLengthRatio: 'Tỷ lệ Chiều dài',
    labelPlacement: 'Vị trí',
    inside: 'Bên trong',
    outside: 'Bên ngoài',
    noDataToChart: 'Không có dữ liệu để tạo biểu đồ.',
    pivotChartRequiresPivotMode: 'Biểu đồ Pivot yêu cầu kích hoạt Chế độ Pivot.',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Liên kết với Lưới',
    chartUnlinkToolbarTooltip: 'Hủy liên kết từ Lưới',
    chartDownloadToolbarTooltip: 'Tải xuống Biểu đồ',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Chỉnh sửa Biểu đồ',
    chartAdvancedSettings: 'Cài đặt Nâng cao',
    chartLink: 'Liên kết với Lưới',
    chartUnlink: 'Hủy liên kết từ Lưới',
    chartDownload: 'Tải xuống Biểu đồ',
    histogramFrequency: 'Tần số',
    seriesChartType: 'Loại Biểu đồ Chuỗi',
    seriesType: 'Loại Chuỗi',
    secondaryAxis: 'Trục Phụ',
    seriesAdd: 'Thêm một chuỗi',
    categoryAdd: 'Thêm một danh mục',
    bar: 'Thanh',
    column: 'Cột',
    histogram: 'Histogram',
    advancedSettings: 'Cài đặt Nâng cao',
    direction: 'Hướng',
    horizontal: 'Ngang',
    vertical: 'Dọc',
    seriesGroupType: 'Loại Nhóm',
    groupedSeriesGroupType: 'Nhóm',
    stackedSeriesGroupType: 'Xếp chồng',
    normalizedSeriesGroupType: 'Xếp chồng 100%',
    legendEnabled: 'Bật',
    invalidColor: 'Giá trị màu sắc không hợp lệ',
    groupedColumnFull: 'Cột Nhóm',
    stackedColumnFull: 'Cột Xếp chồng',
    normalizedColumnFull: 'Cột Xếp chồng 100%',
    groupedBarFull: 'Thanh Nhóm',
    stackedBarFull: 'Thanh Xếp chồng',
    normalizedBarFull: 'Thanh Xếp chồng 100%',
    stackedAreaFull: 'Khu vực Xếp chồng',
    normalizedAreaFull: 'Khu vực Xếp chồng 100%',
    customCombo: 'Kết hợp Tùy chỉnh',
    funnel: 'Phễu',
    coneFunnel: 'Phễu hình nón',
    pyramid: 'Kim tự tháp',
    funnelGroup: 'Phễu',
    funnelTooltip: 'Phễu',
    coneFunnelTooltip: 'Phễu hình nón',
    pyramidTooltip: 'Kim tự tháp',
    dropOff: 'Rơi/Giảm',
    stageLabels: 'Nhãn giai đoạn',
    reverse: 'Đảo ngược',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Mức ${variable}. Nhấn ENTER để chỉnh sửa.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Mức ${variable}. ${variable} Nhấn ENTER để chỉnh sửa.',
    ariaAdvancedFilterBuilderList: 'Danh sách Bộ lọc Nâng cao',
    ariaAdvancedFilterBuilderFilterItem: 'Điều kiện Lọc',
    ariaAdvancedFilterBuilderGroupItem: 'Nhóm Lọc',
    ariaAdvancedFilterBuilderColumn: 'Cột',
    ariaAdvancedFilterBuilderOption: 'Tùy chọn',
    ariaAdvancedFilterBuilderValueP: 'Giá trị',
    ariaAdvancedFilterBuilderJoinOperator: 'Toán tử Kết nối',
    ariaAdvancedFilterInput: 'Đầu vào Bộ lọc Nâng cao',
    ariaChecked: 'đã đánh dấu',
    ariaColumn: 'Cột',
    ariaColumnGroup: 'Nhóm cột',
    ariaColumnFiltered: 'Cột đã lọc',
    ariaColumnSelectAll: 'Chuyển đổi hiển thị tất cả các cột',
    ariaDateFilterInput: 'Đầu vào Bộ lọc Ngày',
    ariaDefaultListName: 'Danh sách',
    ariaFilterColumnsInput: 'Đầu vào Lọc Cột',
    ariaFilterFromValue: 'Lọc từ giá trị',
    ariaFilterInput: 'Đầu vào Bộ lọc',
    ariaFilterList: 'Danh sách Lọc',
    ariaFilterToValue: 'Lọc đến giá trị',
    ariaFilterValue: 'Giá trị Lọc',
    ariaFilterMenuOpen: 'Mở Menu Bộ lọc',
    ariaFilteringOperator: 'Toán tử Lọc',
    ariaHidden: 'ẩn',
    ariaIndeterminate: 'không xác định',
    ariaInputEditor: 'Trình chỉnh sửa đầu vào',
    ariaMenuColumn: 'Nhấn ALT xuống để mở menu cột',
    ariaFilterColumn: 'Nhấn CTRL ENTER để mở bộ lọc',
    ariaRowDeselect: 'Nhấn SPACE để bỏ chọn hàng này',
    ariaHeaderSelection: 'Cột với lựa chọn tiêu đề',
    ariaSelectAllCells: 'Nhấn phím Space để chọn tất cả các ô',
    ariaRowSelectAll: 'Nhấn SPACE để chuyển đổi chọn tất cả các hàng',
    ariaRowToggleSelection: 'Nhấn SPACE để chuyển đổi chọn hàng',
    ariaRowSelect: 'Nhấn SPACE để chọn hàng này',
    ariaRowSelectionDisabled: 'Chọn hàng bị vô hiệu hoá cho hàng này',
    ariaSearch: 'Tìm kiếm',
    ariaSortableColumn: 'Nhấn ENTER để sắp xếp',
    ariaToggleVisibility: 'Nhấn SPACE để chuyển đổi hiển thị',
    ariaToggleCellValue: 'Nhấn SPACE để chuyển đổi giá trị ô',
    ariaUnchecked: 'chưa đánh dấu',
    ariaVisible: 'hiển thị',
    ariaSearchFilterValues: 'Tìm kiếm giá trị lọc',
    ariaPageSizeSelectorLabel: 'Kích thước Trang',
    ariaChartMenuClose: 'Đóng Menu Chỉnh sửa Biểu đồ',
    ariaChartSelected: 'Đã chọn',
    ariaSkeletonCellLoadingFailed: 'Hàng không tải được',
    ariaSkeletonCellLoading: 'Dữ liệu hàng đang tải',
    ariaDeferSkeletonCellLoading: 'Ô đang tải',

    // ARIA for Batch Edit
    ariaPendingChange: 'Thay đổi đang chờ xử lý',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Nhóm Hàng',
    ariaValuesDropZonePanelLabel: 'Giá Trị',
    ariaPivotDropZonePanelLabel: 'Nhãn Cột',
    ariaDropZoneColumnComponentDescription: 'Nhấn DELETE để xóa',
    ariaDropZoneColumnValueItemDescription: 'Nhấn ENTER để thay đổi loại tổng hợp',
    ariaDropZoneColumnGroupItemDescription: 'Nhấn ENTER để sắp xếp',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' của ',
    ariaDropZoneColumnComponentSortAscending: 'tăng dần',
    ariaDropZoneColumnComponentSortDescending: 'giảm dần',
    ariaLabelDialog: 'Hộp thoại',
    ariaLabelColumnMenu: 'Menu Cột',
    ariaLabelColumnFilter: 'Bộ Lọc Cột',
    ariaLabelSelectField: 'Chọn Trường',

    // Cell Editor
    ariaLabelCellEditor: 'Trình chỉnh sửa ô',
    ariaValidationErrorPrefix: 'Xác nhận lỗi trình chỉnh sửa ô',
    ariaLabelLoadingContextMenu: 'Đang tải Menu Ngữ cảnh',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Trường chọn nâng cao',
    ariaLabelRichSelectToggleSelection: 'Nhấn SPACE để chuyển đổi lựa chọn',
    ariaLabelRichSelectDeselectAllItems: 'Nhấn DELETE để bỏ chọn tất cả các mục',
    ariaLabelRichSelectDeleteSelection: 'Nhấn DELETE để bỏ chọn mục',
    ariaLabelTooltip: 'Chú giải công cụ',
    ariaLabelContextMenu: 'Trình đơn ngữ cảnh',
    ariaLabelSubMenu: 'Trình đơn phụ',
    ariaLabelAggregationFunction: 'Hàm tổng hợp',
    ariaLabelAdvancedFilterAutocomplete: 'Tự động hoàn thành bộ lọc nâng cao',
    ariaLabelAdvancedFilterBuilderAddField: 'Bộ lọc nâng cao - Thêm trường',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Bộ lọc nâng cao - Chọn cột',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Bộ lọc nâng cao - Chọn tùy chọn',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Bộ lọc nâng cao - Chọn toán tử nối',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Danh sách Cột',
    ariaFilterPanelList: 'Danh sách Bộ lọc',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Thêm Trường Bộ Lọc',
    ariaLabelFilterCardDelete: 'Xóa Bộ Lọc',
    ariaLabelFilterCardHasEdits: 'Có Chỉnh Sửa',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'Đúng',
    false: 'Sai',
    invalidDate: 'Ngày không hợp lệ',
    invalidNumber: 'Số không hợp lệ',
    january: 'Tháng Một',
    february: 'Tháng Hai',
    march: 'Tháng Ba',
    april: 'Tháng Tư',
    may: 'Tháng Năm',
    june: 'Tháng Sáu',
    july: 'Tháng Bảy',
    august: 'Tháng Tám',
    september: 'Tháng Chín',
    october: 'Tháng Mười',
    november: 'Tháng Mười Một',
    december: 'Tháng Mười Hai',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS SA/CH',
};
