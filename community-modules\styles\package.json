{"name": "@ag-grid-community/styles", "version": "34.0.1", "description": "AG Grid Styles and Themes", "main": "_index.scss", "files": ["*.scss", "*.css"], "scripts": {"build:styles": "npm run build:sass -- --no-error-css && node post-build.js", "build:sass": "sass --silence-deprecation=mixed-decls --no-source-map --load-path src/internal src:.", "watch": "npm-run-all build --parallel watch:sass watch:post-build", "watch:post-build": "chokidar '*.css' --ignore '*.min.css' --command 'node post-build.js'", "watch:sass": "npm run build:sass -- --watch", "clean": "rm -f *.css", "testx": "npm run lint:scss && npm run build && npm run lint:css && npm run test:sass", "test:sass": "sass test/test.scss", "lint:css": "stylelint --formatter unix --config stylelint-config-css.js '*.css'", "lint:scss": "stylelint --formatter unix --config stylelint-config-scss.js 'src/**/*.scss' '*.scss'", "update-icon-fonts": "cd icon-fonts && npm i && npm run build"}, "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-grid.git"}, "keywords": ["ag", "ag-grid", "style", "theme"], "private": true, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ag-grid/ag-grid/issues"}, "homepage": "http://www.ag-grid.com/", "devDependencies": {"@vusion/webfonts-generator": "0.8.0", "chokidar-cli": "3.0.0", "csso": "5.0.4", "css-tree": "2.1.0", "npm-run-all": "4.1.5", "postcss-scss": "4.0.4", "sass": "1.79.3", "stylelint": "14.9.1", "stylelint-csstree-validator": "2.0.0", "stylelint-scss": "4.3.0", "glob": "8.0.3"}}