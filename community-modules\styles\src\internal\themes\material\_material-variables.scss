@use 'sass:color';

.ag-theme-material,
.ag-theme-material-dark,
.ag-theme-material-auto-dark {
    // Taking primary and accent from the indigo / pink theme: https://github.com/angular/material2/blob/master/src/lib/core/theming/prebuilt/indigo-pink.scss
    --ag-material-primary-color: #3f51b5;
    --ag-material-accent-color: #ff4081;

    // Colors
    --ag-foreground-color: rgba(0, 0, 0, 0.87);
    --ag-secondary-foreground-color: rgba(0, 0, 0, 0.54);
    --ag-disabled-foreground-color: rgba(0, 0, 0, 0.38);
    --ag-background-color: #fff;
    --ag-header-background-color: #fff;
    --ag-tooltip-background-color: #fff;
    --ag-subheader-background-color: #eee;
    --ag-subheader-toolbar-background-color: rgba(238, 238, 238, 0.5);
    --ag-header-cell-hover-background-color: #f2f2f2;
    --ag-chip-background-color: #e2e2e2;
    --ag-range-selection-background-color: rgba(122, 134, 203, 0.1);
    --ag-range-selection-background-color-2: rgba(122, 134, 203, 0.19);
    --ag-range-selection-background-color-3: rgba(122, 134, 203, 0.27);
    --ag-range-selection-background-color-4: rgba(122, 134, 203, 0.34);
    --ag-row-numbers-selected-color: color-mix(in srgb, transparent, var(--ag-material-accent-color) 50%);
    --ag-range-selection-highlight-color: #fce4ec;
    --ag-row-hover-color: #fafafa;
    --ag-column-hover-color: #fafafa;
    --ag-control-panel-background-color: #fafafa;
    --ag-selected-row-background-color: rgba(33, 150, 243, 0.3);
    --ag-checkbox-unchecked-color: #333;
    --ag-value-change-value-highlight-background-color: #00acc1;
    --ag-side-button-selected-background-color: transparent;
    --ag-advanced-filter-join-pill-color: #f08e8d;
    --ag-advanced-filter-column-pill-color: #a6e194;
    --ag-advanced-filter-option-pill-color: #f3c08b;
    --ag-advanced-filter-value-pill-color: #85c0e4;
    --ag-find-match-color: var(--ag-foreground-color);
    --ag-find-match-background-color: #ffff00;
    --ag-find-active-match-color: var(--ag-foreground-color);
    --ag-find-active-match-background-color: #ffa500;
    --ag-filter-panel-apply-button-color: var(--ag-material-primary-color);
    --ag-filter-panel-apply-button-background-color: transparent;

    // derived colours (no color blending)
    --ag-range-selection-border-color: var(--ag-material-primary-color);
    --ag-checkbox-checked-color: var(--ag-material-accent-color);

    // Borders
    --ag-borders: none;
    --ag-borders-critical: solid 1px;
    --ag-border-color: #e2e2e2;

    // Sizing
    --ag-grid-size: 8px;
    --ag-icon-size: 18px;
    --ag-header-height: calc(var(--ag-grid-size) * 7);
    --ag-row-height: calc(var(--ag-grid-size) * 6);
    --ag-cell-horizontal-padding: calc(var(--ag-grid-size) * 3);
    --ag-list-item-height: calc(var(--ag-grid-size) * 4);
    --ag-row-group-indent-size: calc(var(--ag-grid-size) * 3 + var(--ag-icon-size));
    --ag-filter-tool-panel-sub-level-row-height: calc(var(--ag-grid-size) * 4);
    --ag-checkbox-border-radius: 2px;
    --ag-toggle-button-switch-border-width: 2px;
    --ag-toggle-button-height: var(--ag-icon-size);
    --ag-widget-container-horizontal-padding: calc(var(--ag-grid-size) * 1.5);
    --ag-widget-container-vertical-padding: calc(var(--ag-grid-size) * 2);
    --ag-widget-vertical-spacing: calc(var(--ag-grid-size) * 1.75);

    // Fonts
    --ag-font-family: Roboto, -apple-system, BlinkMacSystemFont, 'Segoe UI', Oxygen-Sans, Ubuntu, Cantarell,
        'Helvetica Neue', sans-serif;
    --ag-font-size: 13px;
    --ag-icon-font-family: agGridMaterial;

    // Misc
    --ag-selected-tab-underline-color: var(--ag-material-primary-color);
    --ag-selected-tab-underline-width: 2px;

    --ag-input-focus-border-color: var(--ag-material-primary-color);

    --ag-input-focus-box-shadow: 0 0 0 5px rgba(32, 33, 36, 0.122);
    --ag-input-error-focus-box-shadow: 0 0 0 5px
        color-mix(in srgb, var(--ag-background-color), var(--ag-invalid-color) 0.5%);

    --ag-card-shadow: 0 3px 1px -2px rgba(0, 0, 0, 0.2), 0 2px 2px 0 rgba(0, 0, 0, 0.14),
        0 1px 5px 0 rgba(0, 0, 0, 0.12);
    --ag-card-radius: 2px;
    --ag-invalid-color: #e02525;
}

@mixin -dark-vars {
    --ag-material-primary-color: #3f51b5;
    --ag-material-accent-color: #bb86fcff;

    --ag-range-selection-border-color: var(--ag-material-accent-color);
    --ag-find-match-color: var(--ag-background-color);
    --ag-find-active-match-color: var(--ag-background-color);

    --ag-background-color: #121212ff;
    --ag-foreground-color: #ffffffff;
    --ag-data-color: #f5f5f5ff;
    --ag-header-cell-hover-background-color: #000000ff;
    --ag-advanced-filter-join-pill-color: #7a3a37ff;
    --ag-advanced-filter-column-pill-color: #355f2dff;
    --ag-advanced-filter-option-pill-color: #5a3168ff;
    --ag-advanced-filter-value-pill-color: #374c86ff;
    --ag-input-disabled-border-color: #3a434eff;
    --ag-input-disabled-background-color: #68686e12;
    --ag-selected-row-background-color: #bb86fc33;
    --ag-row-hover-color: #bb86fc33;
    --ag-column-hover-color: #f5f5f50d;
    --ag-range-selection-background-color: #bb86fc1a;
    --ag-range-selection-background-color-2: #bb86fc30;
    --ag-range-selection-background-color-3: #bb86fc45;
    --ag-range-selection-background-color-4: #bb86fc57;
    --ag-border-color: #383838ff;
    --ag-secondary-border-color: #383838ff;
    --ag-header-background-color: #121212ff;
    --ag-tooltip-background-color: #212b38ff;
    --ag-odd-row-background-color: #121212ff;
    --ag-control-panel-background-color: #2c2c2cff;
    --ag-subheader-background-color: #ffffff0d;
    --ag-subheader-toolbar-background-color: #2c2c2cff;
    --ag-invalid-color: #e02525ff;
    --ag-checkbox-unchecked-color: #797e87ff;
    --ag-checkbox-background-color: #121212ff;
    --ag-secondary-foreground-color: #f5f5f5ff;
    --ag-input-border-color: #383838ff;
    --ag-input-border-color-invalid: #e02525ff;
    --ag-disabled-foreground-color: #f5f5f580;
    --ag-chip-background-color: #22262812;
    --ag-side-button-selected-background-color: #2c2c2cff;
    --ag-selected-tab-underline-color: #3f51b5ff;
    --ag-modal-overlay-background-color: #121212a8;
    --ag-value-change-delta-up-color: #43a047a8;
    --ag-value-change-delta-down-color: #e53935ff;
    --ag-menu-background-color: #2c2c2cff;
    --ag-row-loading-skeleton-effect-color: #{color.change(#cacbcc, $alpha: 0.4)};

    --ag-cell-batch-edit-text-color: #f3d0b3;

    color-scheme: dark;
}

.ag-theme-material-dark {
    @include -dark-vars();
}

@media (prefers-color-scheme: dark) {
    .ag-theme-material-auto-dark {
        @include -dark-vars();
    }
}
