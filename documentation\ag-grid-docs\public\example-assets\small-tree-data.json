[{"employeeId": 101, "employeeName": "<PERSON>", "jobTitle": "CEO", "employmentType": "Permanent", "children": [{"employeeId": 102, "employeeName": "<PERSON>", "jobTitle": "Exec. Vice President", "employmentType": "Permanent", "children": [{"employeeId": 103, "employeeName": "<PERSON>", "jobTitle": "Director of Operations", "employmentType": "Permanent", "children": [{"employeeId": 104, "employeeName": "<PERSON>", "jobTitle": "Fleet Coordinator", "employmentType": "Permanent", "children": [{"employeeId": 105, "employeeName": "<PERSON>", "jobTitle": "Parts Technician", "employmentType": "Contract"}, {"employeeId": 106, "employeeName": "<PERSON>", "jobTitle": "Service Technician", "employmentType": "Contract"}]}, {"employeeId": 107, "employeeName": "<PERSON>", "jobTitle": "Inventory Control", "employmentType": "Permanent"}]}, {"employeeId": 108, "employeeName": "<PERSON>", "jobTitle": "VP Sales", "employmentType": "Permanent", "children": [{"employeeId": 109, "employeeName": "<PERSON>", "jobTitle": "Sales Manager", "employmentType": "Permanent"}, {"employeeId": 110, "employeeName": "<PERSON>", "jobTitle": "Sales Executive", "employmentType": "Contract"}, {"employeeId": 111, "employeeName": "<PERSON><PERSON>", "jobTitle": "Sales Executive", "employmentType": "Contract"}, {"employeeId": 112, "employeeName": "<PERSON>", "jobTitle": "Sales Executive", "employmentType": "Permanent"}]}]}, {"employeeId": 113, "employeeName": "<PERSON>", "jobTitle": "Exec. Vice President", "employmentType": "Permanent", "children": [{"employeeId": 114, "employeeName": "<PERSON>", "jobTitle": "Director of Operations", "employmentType": "Permanent", "children": [{"employeeId": 115, "employeeName": "<PERSON>", "jobTitle": "Fleet Coordinator", "employmentType": "Permanent", "children": [{"employeeId": 116, "employeeName": "<PERSON>", "jobTitle": "Parts Technician", "employmentType": "Contract"}, {"employeeId": 117, "employeeName": "<PERSON>", "jobTitle": "Service Technician", "employmentType": "Contract"}]}, {"employeeId": 118, "employeeName": "<PERSON>", "jobTitle": "Inventory Control", "employmentType": "Permanent"}]}, {"employeeId": 119, "employeeName": "<PERSON>", "jobTitle": "VP Sales", "employmentType": "Permanent", "children": [{"employeeId": 120, "employeeName": "<PERSON>", "jobTitle": "Sales Manager", "employmentType": "Permanent"}, {"employeeId": 121, "employeeName": "<PERSON>", "jobTitle": "Sales Executive", "employmentType": "Contract"}, {"employeeId": 122, "employeeName": "<PERSON>", "jobTitle": "Sales Executive", "employmentType": "Contract"}, {"employeeId": 123, "employeeName": "<PERSON>", "jobTitle": "Sales Executive", "employmentType": "Permanent"}]}]}]}]