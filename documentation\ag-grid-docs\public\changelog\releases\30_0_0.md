#### 9th Jun 2023 - Grid v30.0.0 (Charts v8.0.0)

#### Feature Highlights:

-   Cell Editing
    -   AG-8234 - Add cell data types with automatic type inference (See [Cell Data Types](https://www.ag-grid.com/archive/30.0.0/react-data-grid/cell-data-types/))
    -   AG-4037 - Add built-in date cell editor (See [Date Editor)](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/provided-cell-editors/#date-cell-editor)
    -   AG-7413 - Add built-in numeric cell editor (See [Number Editor](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/provided-cell-editors/#number-cell-editor))
    -   AG-8718 - Add built-in checkbox cell editor (See [Checkbox Editor](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/provided-cell-editors/#checkbox-cell-editor))
-   Cell Rendering
    -   AG-2396 - Allow the formatted cell value to be used with copy / fill handle / copy range down (See [Value Formatter](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/value-formatters/#formatting-for-export))
    -   AG-1900 - Allow Value Parser to run on paste / fill handle / copy range down (See [Value Parser](https://www.ag-grid.com/archive/30.0.0/react-data-grid/value-parsers/#use-value-parser-for-import))
-   Export
    -   AG-3427 - Allow exporting the formatted cell value instead of the underlying cell value (See [Value Formatter](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/value-formatters/#formatting-for-export))
    -   AG-5719 - Allow exporting row groups to Excel to preserve their collapsed / expanded state (See [Excel Export](https://www.ag-grid.com/archive/30.0.0/react-data-grid/excel-export-api/#reference-ExcelExportParams-rowGroupExpandState))
-   API Improvements
    -   AG-8464 - Allow updating an integrated chart via the Grid API (see [Event Driven Chart Updates](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/integrated-charts-events/#event-driven-chart-updates))
    -   AG-8076 - Add API for bulk row selection (See [Bulk Row Selection API](https://www.ag-grid.com/archive/30.0.0/react-data-grid/row-selection/#reference-selection-setNodesSelected))
    -   AG-8095 - Add events to fire at the start and end of a cut operation (See [Clipboard Events](https://www.ag-grid.com/archive/30.0.0/react-data-grid/clipboard/#clipboard-events))
    -   AG-8096 - Add events to fire at the start and end of cell range delete operations (See [Delete Range](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/range-selection/#delete-range))
-   Miscellaneous
    -   AG-687 - Add support for group footer rows when using server-side row model (See [SSRM - Row Grouping](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/server-side-model-grouping/))
    -   AG-5525 - Allow AG Grid module registration on a per-grid basis (See [Providing Modules to Individual Grids](https://www.ag-grid.com/archive/30.0.0/react-data-grid/modules/#providing-modules-to-individual-grids))
    -   AG-8674 - Default to use ES Modules in main field of package.json for modules

#### Breaking Changes:

##### Angular Upgrade

The minimum Angular dependency supported by AG Grid v30 is now Angular v12. Previous versions of Angular aren't supported by AG Grid v30. There is no v30 for `@ag-grid-community/angular-legacy` or `ag-grid-angular-legacy` as these are only required for Angular versions <= v11. See [Angular Compatibility](https://www.ag-grid.com/archive/30.0.0/angular-data-grid/angular-compatibility/) for more information.

##### Typescript Upgrade

Minimum version of Typescript is now v4.3 for v30 of AG Grid. You may have type errors if a `TData` generic is provided to `GridOptions` but the `columnDefs` is set from an untyped external property. To resolve provide explicit type your `columnDefs`.

##### Module Registration

Modules registered individually to a grid are now only available to that instance of the grid. Previously individually registered modules were registered globally leading to a leaking of features across different grids. See [Registering AG Grid Modules](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/modules/#registering-ag-grid-modules) for more information.

##### Build & Deploy

In previous releases the `main` field of `package.json` defaulted to ES5 CommonJS artefacts. From v30 onwards the default will be ES6 ES Modules. For most
modern build tools such as Webpack, Angular ClI, React Scripts or Vue 2/3 CLI your build should work as it did before - and should result in a smaller overall
bundle size. If however you're using a more complicated build that relied on CommonJS being the default then you may need to make changes to accommodate this change.

Note that we still build and distribute both ES5/ES6 versions of both CommonJS and ES Modules.

Jest users please refer to the [Testing](https://www.ag-grid.com/javascript-data-grid/testing/) section of the documentation for further configuration instructions.

##### ColDef

Certain properties defined on the `defaultColDef`, including `valueFormatter` and `valueParser`, will now be overridden by cell data types (which are enabled by default). See [Cell Data Types](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/cell-data-types/) for more information.

The following properties were previously deprecated and have now been removed:

Cell Rendering

-   `cellRendererFramework` - removed, use `cellRenderer` instead.
-   Return type of `cellRendererSelector` property `frameworkComponent` removed - use `component` property instead.

Cell Editing

The `CellKeyPress` and `FullWidthCellKeyPress` events are no longer fired by the Grid. These events have been replaced by `CellKeyDown` and `FullWidthCellKeyDown`. All events that had source= `spacePressed` or `deleteKeyPressed` now have the source set to `spaceKey` or `deleteKey.`

`charPress` - removed from `ICellEditorParams` and `StartEditingCellParams`. You can now detect a key press by using: `if (eventKey.length === 1)`

`cellEditorFramework` - removed, use `cellEditor` instead.

Return type of `cellEditorSelector` property `frameworkComponent` removed - use `component` property instead.

`'agPopupTextCellEditor'` removed - use `{cellEditor: "agTextCellEditor"; cellEditorPopup: true}` instead.

`'agPopupSelectCellEditor'` removed - use `{cellEditor: "agSelectCellEditor"; cellEditorPopup: true}` instead.

Components

-   `tooltipComponentFramework` - removed, use `tooltipComponent` instead.
-   `headerComponentFramework` - removed, use `headerComponent` instead.
-   `headerGroupComponentFramework` - removed, use `headerGroupComponent` instead.

Header Height

-   `colDef.spanHeaderHeight` removed and made default, use `colDef.suppressSpanHeaderHeight` to suppress.

##### ColumnGroup

The following properties were previously deprecated and have now been removed.

`colGroupDef.stickyLabel` removed and made default, use `colGroupDef.suppressStickyLabel` to suppress.

`getOriginalColumnGroup` - removed, use `getProvidedColumnGroup` instead.

##### Column Api

The following methods were previously deprecated and have now been removed.

-   `getOriginalColumnGroup` - removed, use `getProvidedColumnGroup` instead.

##### GridOptions

The following properties were previously deprecated and have now been removed.

Clipboard

-   `clipboardDeliminator` - removed, use `clipboardDelimiter` instead.

Components

-   `frameworkComponents` - removed, use `components` instead.
-   `loadingCellRendererFramework` - removed, use `loadingCellRenderer` instead.
-   Return type of `loadingCellRendererSelector` property `frameworkComponent` removed - use `component` instead.
-   `detailCellRendererFramework` - removed, use `detailCellRenderer` instead.
-   `loadingOverlayComponentFramework` - removed, use `loadingOverlayComponent` instead.
-   `noRowsOverlayComponentFramework` - removed, use `noRowsOverlayComponent` instead.
-   `fullWidthCellRendererFramework` - removed, use `fullWidthCellRenderer` instead.
-   `groupRowRendererFramework` - removed, use `groupRowRenderer` instead.

Editing

-   `stopEditingWhenGridLosesFocus` - removed, use `stopEditingWhenCellsLoseFocus` instead.

Full Width

-   `isFullWidthCell` - removed, use `isFullWidthRow` instead.

Filtering

-   Set Filter parameter `defaultToNothingSelected` will be ignored when `excelMode` is set
-   All browsers now use HTML number inputs for the Number Filter by default. The old behaviour (in browsers other than Chrome or Edge) can be replicated by setting `filterParams.allowedCharPattern = '\\d\\-\\.'`
-   `serverSideFilterAllLevels` - deprecated and the behaviour made default. Set `serverSideOnlyRefreshFilteredGroups` property to only refresh filtered groups as before.
-   `excludeHiddenColumnsFromQuickFilter` - deprecated and the behavior made default. Hidden columns are now excluded from the Quick Filter by default. To include them, set the grid option `includeHiddenColumnsInQuickFilter = true`

Grouping

-   `groupMultiAutoColumn` - removed, set `groupDisplayType='multipleColumns'` instead.
-   `groupUseEntireRow` - removed, set `groupDisplayType='groupRows'` instead.
-   `defaultGroupOrderComparator` - removed, use `initialGroupOrderComparator` instead.
-   `groupRowAggNodes` - removed, use `getGroupRowAgg` instead.
-   `gridOptions.groupRowsSticky` removed and made default, use `gridOptions.suppressGroupRowsSticky` to suppress.
-   `suppressAggAtRootLevel` - deprecated and the behavior made default. The root level aggregation is now suppressed by default, except when using `groupIncludeTotalFooter=true`. This can be toggled using `alwaysAggregateAtRootLevel`

Immutable Data

-   `immutableData` - removed, implement `getRowId()` to enable immutable data mode.
-   `getRowNodeId` - removed, use `getRowId()` instead.

Localisation

-   `localeTextFunc` - removed, use `getLocaleText` instead.

Selection

-   `suppressCellSelection` - removed, use `suppressCellFocus` instead.

Sorting

-   `postSort` - removed, use `postSortRows` instead.

React

-   `reactUI` - removed as this is now the default.

Row Dragging

-   `enableMultiRowDragging` - removed, use `rowDragMultiRow` instead.

Tool Panel

-   `toolPanelVisibleChanged` updated, see [toolPanelVisibleChanged](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/tool-panel/#reference-accessories-toolPanelVisibleChanged). It now fires twice when switching between tool panels (once for the old panel and once for the new panel), but has new fields to indicate the change. The source property now refers to the event source rather than the panel name.

##### Quick Filter

-   When pivoting, quick filter applies to pivot and group columns only and not to primary columns anymore.

#### Grid API:

Callbacks which had the generic `TValue` type may now be slightly stricter with regards to null values. See [Cell \<TValue>](https://www.ag-grid.com/archive/30.0.0/javascript-data-grid/typescript-generics/#cell-value-tvalue).

The following methods were previously deprecated and have now been removed.

Full Width

-   `setIsFullWidthCell` - removed, use `setIsFullWidthRow` instead.

Grouping

-   `setGroupRowAggNodes` - removed, use `setGetGroupRowAgg` instead.
-   `setDefaultGroupOrderComparator` - removed, use `setInitialGroupOrderComparator` instead.

Immutable Data

-   `setGetRowNodeId` - removed, use `setGetRowId` instead.

Sorting

-   `setPostSort` - removed, use `setPostSortRows` instead.

Selection

-   `rowNode.setSelected()` property `suppressFinishActions` - removed, use `gridApi.setNodesSelected()` for bulk row selection, and the event `source` property for ignoring events instead.

#### IFilterOptionDef

-   `test` - removed, use `predicate` instead.
-   `hideFilterInput` - removed, use `numberOfInputs: 0` instead.

#### ISetFilterParams

-   `cellRendererFramework` - removed, use `cellRenderer` instead.

#### ToolPanelDef

-   `toolPanelFramework` - removed, use `toolPanel` instead.

#### StatusPanelDef

-   `statusPanelFramework` - removed, use `statusPanel` instead.

#### IGroupCellRendererParams

-   `innerRendererFramework` - removed, use `innerRenderer` instead.

#### IMultiFilterDef

-   `filterFramework` - removed, use `filter` instead.
-   `floatingFilterComponentFramework` - removed, use `floatingFilterComponent` instead.

#### Standalone Charts

##### General Option Processing

-   When `enabled` is set to false in an options object, all other configurations in that object will be ignored.

##### Chart Auto-Sizing

-   `autoSize` now does not override a specified `height` or `width`, and only applies to unset dimensions. `autoSize` set to false without a `width` and `height` specified, fixes the size of the canvas to its initial size.

##### Legend

-   The legend is now hidden by default in cartesian charts with only one series. To show the legend, use `legend.enabled`.
-   The default legend position is now `bottom`. To use a custom position, set `legend.position`.
-   `column` series legend items now follow declaration order. Use `legend.reverseOrder = true` to revert this ordering change if needed.
-   Pie series sectors no longer toggle when a legend item is double clicked.

##### Formatter Callbacks (excluding `AgCartesianSeriesMarkerFormatter`)

-   Chart `formatter` callbacks will now be deduplicated, with the intent being that we cache results for every unique permutation of parameters. The result cache is cleared on an `AgChart.update()` or `AgChart.updateDelta()` call to allow for modifications to callback behaviour to be picked up.

##### Navigator

-   Disabling the navigator now clears any zoom/scroll applied, rather than leaving it present. Re-enabling restores the previous zoom/scroll state.

##### Event Handlers

-   `AgNodeBaseClickEvent.series` removed - use `seriesId` instead.
-   `AgSeriesNodeClickParams.series` removed - use `seriesId` instead.

##### Other Breaking Option Changes

-   `AgAxisCategoryTickOptions.maxSpacing` has been immediately removed from our typings. This option never had any impact on axis tick selection, so this is considered a bugfix to the `AgAxisCategoryTickOptions` type.
-   `AgBarSeriesOptions`
-   `flipXY` has been removed without deprecation as it was redundant, and didn't work consistently - specify series type `column` or `bar` instead.
-   `AgBarSeriesOptions` has been updated to remove support for `type` of `column`, which has be split into a distinct type of `AgColumnSeriesOptions`. Aside from type name changes, their options contracts are identical, so no runtime options changes should be needed.
-   The default `seriesId` for `column` series now have a different prefix of `'ColumnSeries-'` - we recommend you supply your own `seriesId` rather than relying on this default.
-   `AgChartLegendLabelFormatterParams.id` removed - use `seriesId` instead.
-   `AgPieSeriesOptions`
-   `label` removed - use `calloutLabel` instead.
-   `callout` removed - use `calloutLine` instead.
-   `labelKey` removed - use `calloutLabelKey` or `sectorLabelKey` instead.
-   `labelName` removed - use `calloutLabelName` or `sectorLabelName` instead.
-   `AgPieSeriesTooltipRendererParams`
-   `labelKey` removed - use `calloutLabelKey` or `sectorLabelKey` instead.
-   `labelName` removed - use `calloutLabelName` or `sectorLabelName` instead.
-   `AgPieSeriesLabelFormatterParams`
-   `labelValue` removed - use `calloutLabelValue` instead.
-   `labelName` removed - use `calloutLabelName` instead.
-   `value` removed - use `item.datum` instead.

#### Deprecations:

Selection

-   `enterMovesDown` and `enterMovesDownAfterEdit` deprecated. Use `enterNavigatesVertically` and `enterNavigatesVerticallyAfterEdit` instead.
