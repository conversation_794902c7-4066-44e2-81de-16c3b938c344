/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_HR = {
    // Set Filter
    selectAll: '(Odaberi sve)',
    selectAllSearchResults: '(Odaberi sve rezultate pretraživanja)',
    addCurrentSelectionToFilter: 'Dodaj trenutni odabir u filter',
    searchOoo: 'Traži...',
    blanks: '(Praznine)',
    noMatches: 'Nema podudaranja',

    // Number Filter & Text Filter
    filterOoo: 'Filt<PERSON>raj...',
    equals: 'Jednako',
    notEqual: '<PERSON><PERSON> jednako',
    blank: 'Praz<PERSON>',
    notBlank: 'Nije prazno',
    empty: 'Odaberite jedan',

    // Number Filter
    lessThan: 'Manje od',
    greaterThan: 'Veće od',
    lessThanOrEqual: 'Manje ili jedna<PERSON>',
    greaterThanOrEqual: 'Veće ili jednako',
    inRange: 'Između',
    inRangeStart: 'Od',
    inRangeEnd: 'Do',

    // Text Filter
    contains: 'Sadrži',
    notContains: 'Ne sadrži',
    startsWith: 'Počinje s',
    endsWith: 'Završava s',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Prije',
    after: 'Poslije',

    // Filter Conditions
    andCondition: 'I',
    orCondition: 'ILI',

    // Filter Buttons
    applyFilter: 'Primijeni',
    resetFilter: 'Poništi',
    clearFilter: 'Očisti',
    cancelFilter: 'Odustani',

    // Filter Titles
    textFilter: 'Tekstualni filter',
    numberFilter: 'Numerički filter',
    dateFilter: 'Filtar datuma',
    setFilter: 'Filtar skupa',

    // Group Column Filter
    groupFilterSelect: 'Odaberite polje:',

    // New Filter Tool Panel
    filterSummaryInactive: 'je (sve)',
    filterSummaryContains: 'sadrži',
    filterSummaryNotContains: 'ne sadrži',
    filterSummaryTextEquals: 'jednako je',
    filterSummaryTextNotEqual: 'nije jednako',
    filterSummaryStartsWith: 'započinje s',
    filterSummaryEndsWith: 'završava s',
    filterSummaryBlank: 'je prazno',
    filterSummaryNotBlank: 'nije prazno',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'između',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'je (sve)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'je (${variable})',
    filterSummaryListLong: 'je (${variable}) i ${variable} više',
    addFilterCard: 'Dodaj filtriranje',
    agTextColumnFilterDisplayName: 'Jednostavan filter',
    agNumberColumnFilterDisplayName: 'Jednostavan filter',
    agDateColumnFilterDisplayName: 'Jednostavan filter',
    agSetColumnFilterDisplayName: 'Filter za odabir',
    agMultiColumnFilterDisplayName: 'Kombinirani filter',
    addFilterPlaceholder: 'Pretraži stupce...',

    // Advanced Filter
    advancedFilterContains: 'sadrži',
    advancedFilterNotContains: 'ne sadrži',
    advancedFilterTextEquals: 'jednako',
    advancedFilterTextNotEqual: 'nije jednako',
    advancedFilterStartsWith: 'započinje s',
    advancedFilterEndsWith: 'završava s',
    advancedFilterBlank: 'je prazno',
    advancedFilterNotBlank: 'nije prazno',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'je točno',
    advancedFilterFalse: 'je netočno',
    advancedFilterAnd: 'I',
    advancedFilterOr: 'ILI',
    advancedFilterApply: 'Primijeni',
    advancedFilterBuilder: 'Graditelj',
    advancedFilterValidationMissingColumn: 'Nedostaje stupac',
    advancedFilterValidationMissingOption: 'Nedostaje opcija',
    advancedFilterValidationMissingValue: 'Nedostaje vrijednost',
    advancedFilterValidationInvalidColumn: 'Stupac nije pronađen',
    advancedFilterValidationInvalidOption: 'Opcija nije pronađena',
    advancedFilterValidationMissingQuote: 'Vrijednosti nedostaje završna navodna oznaka',
    advancedFilterValidationNotANumber: 'Vrijednost nije broj',
    advancedFilterValidationInvalidDate: 'Vrijednost nije valjani datum',
    advancedFilterValidationMissingCondition: 'Nedostaje uvjet',
    advancedFilterValidationJoinOperatorMismatch: 'Spojni operatori unutar uvjeta moraju biti isti',
    advancedFilterValidationInvalidJoinOperator: 'Spojni operator nije pronađen',
    advancedFilterValidationMissingEndBracket: 'Nedostaje završna zagrada',
    advancedFilterValidationExtraEndBracket: 'Previše završnih zagrada',
    advancedFilterValidationMessage: 'Izraz ima grešku. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Izraz ima grešku. ${variable} na kraju izraza.',
    advancedFilterBuilderTitle: 'Napredni filter',
    advancedFilterBuilderApply: 'Primijeni',
    advancedFilterBuilderCancel: 'Odustani',
    advancedFilterBuilderAddButtonTooltip: 'Dodaj filter ili grupu',
    advancedFilterBuilderRemoveButtonTooltip: 'Ukloni',
    advancedFilterBuilderMoveUpButtonTooltip: 'Pomakni gore',
    advancedFilterBuilderMoveDownButtonTooltip: 'Pomakni dolje',
    advancedFilterBuilderAddJoin: 'Dodaj grupu',
    advancedFilterBuilderAddCondition: 'Dodaj filter',
    advancedFilterBuilderSelectColumn: 'Odaberi stupac',
    advancedFilterBuilderSelectOption: 'Odaberi opciju',
    advancedFilterBuilderEnterValue: 'Unesi vrijednost...',
    advancedFilterBuilderValidationAlreadyApplied: 'Trenutni filter je već primijenjen.',
    advancedFilterBuilderValidationIncomplete: 'Nisu svi uvjeti ispunjeni.',
    advancedFilterBuilderValidationSelectColumn: 'Moraš odabrati stupac.',
    advancedFilterBuilderValidationSelectOption: 'Moraš odabrati opciju.',
    advancedFilterBuilderValidationEnterValue: 'Moraš unijeti vrijednost.',

    // Editor Validation Errors
    minDateValidation: 'Datum mora biti nakon ${variable}',
    maxDateValidation: 'Datum mora biti prije ${variable}',
    maxLengthValidation: 'Mora imati ${variable} znakova ili manje.',
    minValueValidation: 'Mora biti veće ili jednako ${variable}',
    maxValueValidation: 'Mora biti manje ili jednako ${variable}',
    invalidSelectionValidation: 'Nevažeći odabir.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Stupci',
    filters: 'Filteri',

    // columns tool panel
    pivotMode: 'Način okretanja',
    groups: 'Grupiranja redaka',
    rowGroupColumnsEmptyMessage: 'Povucite ovdje za postavljanje grupiranja redaka',
    values: 'Vrijednosti',
    valueColumnsEmptyMessage: 'Povucite ovdje za izvođenje agregacije',
    pivots: 'Oznake stupaca',
    pivotColumnsEmptyMessage: 'Povucite ovdje za postavljanje oznaka stupaca',

    // Header of the Default Group Column
    group: 'Grupa',

    // Row Drag
    rowDragRow: 'redak',
    rowDragRows: 'redci',

    // Other
    loadingOoo: 'Učitavanje...',
    loadingError: 'GREŠKA',
    noRowsToShow: 'Nema redaka za prikaz',
    enabled: 'Omogućeno',

    // Menu
    pinColumn: 'Prikvači stupac',
    pinLeft: 'Prikvači lijevo',
    pinRight: 'Prikvači desno',
    noPin: 'Bez prikvačivanja',
    valueAggregation: 'Agregacija vrijednosti',
    noAggregation: 'Nema',
    autosizeThisColumn: 'Automatski prilagodi ovaj stupac',
    autosizeAllColumns: 'Automatski prilagodi sve stupce',
    groupBy: 'Grupiraj po',
    ungroupBy: 'Razgrupiraj po',
    ungroupAll: 'Razgrupiraj sve',
    addToValues: 'Dodaj ${variable} u vrijednosti',
    removeFromValues: 'Ukloni ${variable} iz vrijednosti',
    addToLabels: 'Dodaj ${variable} u oznake',
    removeFromLabels: 'Ukloni ${variable} iz oznaka',
    resetColumns: 'Resetiraj stupce',
    expandAll: 'Proširi sve grupe redova',
    collapseAll: 'Zatvori sve grupe redova',
    copy: 'Kopiraj',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopiraj s zaglavljima',
    copyWithGroupHeaders: 'Kopiraj s grupnim zaglavljima',
    cut: 'Izreži',
    paste: 'Zalijepi',
    ctrlV: 'Ctrl+V',
    export: 'Izvoz',
    csvExport: 'Izvoz u CSV',
    excelExport: 'Izvoz u Excel',
    columnFilter: 'Filter stupca',
    columnChooser: 'Izbor stupaca',
    chooseColumns: 'Odaberi stupce',
    sortAscending: 'Sortiraj uzlazno',
    sortDescending: 'Sortiraj silazno',
    sortUnSort: 'Očisti sortiranje',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Zbroj',
    first: 'Prvi',
    last: 'Zadnji',
    min: 'Min',
    max: 'Max',
    none: 'Ništa',
    count: 'Broj',
    avg: 'Prosjek',
    filteredRows: 'Filtrirano',
    selectedRows: 'Odabrano',
    totalRows: 'Ukupno redaka',
    totalAndFilteredRows: 'Redaka',
    more: 'Više',
    to: 'do',
    of: 'od',
    page: 'Stranica',
    pageLastRowUnknown: '?',
    nextPage: 'Sljedeća stranica',
    lastPage: 'Zadnja stranica',
    firstPage: 'Prva stranica',
    previousPage: 'Prethodna stranica',
    pageSizeSelectorLabel: 'Veličina stranice:',
    footerTotal: 'Ukupno',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Pomakni ${variable} u prikaz',

    // Pivoting
    pivotColumnGroupTotals: 'Ukupno',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Zakretna tabela i način zakretanja',
    pivotChart: 'Zakretna tabela',
    chartRange: 'Raspon grafikona',
    columnChart: 'Stupac',
    groupedColumn: 'Grupirano',
    stackedColumn: 'Složeno',
    normalizedColumn: '100% Složeno',
    barChart: 'Traka',
    groupedBar: 'Grupirano',
    stackedBar: 'Složeno',
    normalizedBar: '100% Složeno',
    pieChart: 'Pita',
    pie: 'Pita',
    donut: 'Krafna',
    lineChart: 'Linijski',
    stackedLine: 'Složeni',
    normalizedLine: '100% Složeni',
    xyChart: 'X Y (Raspršenost)',
    scatter: 'Raspršenost',
    bubble: 'Mjehurić',
    areaChart: 'Područje',
    area: 'Područje',
    stackedArea: 'Složeno',
    normalizedArea: '100% Složeno',
    histogramChart: 'Histogram',
    polarChart: 'Polarni',
    radarLine: 'Radarska linija',
    radarArea: 'Radarsko područje',
    nightingale: 'Nightingale',
    radialColumn: 'Radijalni stupac',
    radialBar: 'Radijalna traka',
    statisticalChart: 'Statistički',
    boxPlot: 'Kutija graf',
    rangeBar: 'Raspon traka',
    rangeArea: 'Raspon područje',
    hierarchicalChart: 'Hijerarhijski',
    treemap: 'Karta stabla',
    sunburst: 'Sunčev prasak',
    specializedChart: 'Specijalizirani',
    waterfall: 'Vodopad',
    heatmap: 'Toplinska karta',
    combinationChart: 'Kombinacija',
    columnLineCombo: 'Stupac i linija',
    AreaColumnCombo: 'Područje i stupac',

    // Charts
    pivotChartTitle: 'Pivot Graf',
    rangeChartTitle: 'Graf Raspona',
    settings: 'Graf',
    data: 'Postavke',
    format: 'Prilagodba',
    categories: 'Kategorije',
    defaultCategory: '(Nijedna)',
    series: 'Serije',
    switchCategorySeries: 'Promijeni Kategoriju / Serije',
    categoryValues: 'Vrijednosti Kategorije',
    seriesLabels: 'Oznake Serija',
    aggregate: 'Zbir',
    xyValues: 'X Y Vrijednosti',
    paired: 'Upareni Način',
    axis: 'Os',
    xAxis: 'Horizontalna Os',
    yAxis: 'Vertikalna Os',
    polarAxis: 'Polarna Os',
    radiusAxis: 'Radijus Os',
    navigator: 'Navigator',
    zoom: 'Zum',
    animation: 'Animacija',
    crosshair: 'Križ',
    color: 'Boja',
    thickness: 'Debljina',
    preferredLength: 'Poželjna Duljina',
    xType: 'X Tip',
    axisType: 'Tip Osi',
    automatic: 'Automatski',
    category: 'Kategorija',
    number: 'Broj',
    time: 'Vrijeme',
    timeFormat: 'Format Vremena',
    autoRotate: 'Auto Rotacija',
    labelRotation: 'Rotacija',
    circle: 'Krug',
    polygon: 'Poligon',
    square: 'Kvadrat',
    cross: 'Križ',
    diamond: 'Dijamant',
    plus: 'Plus',
    triangle: 'Trokut',
    heart: 'Srce',
    orientation: 'Orijentacija',
    fixed: 'Fiksno',
    parallel: 'Paralelno',
    perpendicular: 'Okomito',
    radiusAxisPosition: 'Pozicija',
    ticks: 'Oznake',
    gridLines: 'Mrežne Linije',
    width: 'Širina',
    height: 'Visina',
    length: 'Duljina',
    padding: 'Razmak',
    spacing: 'Razmak',
    chartStyle: 'Stil Grafa',
    title: 'Naslov',
    chartTitles: 'Naslovi',
    chartTitle: 'Naslov Grafa',
    chartSubtitle: 'Podnaslov',
    horizontalAxisTitle: 'Naslov Horizontalne Osi',
    verticalAxisTitle: 'Naslov Vertikalne Osi',
    polarAxisTitle: 'Naslov Polarne Osi',
    titlePlaceholder: 'Naslov Grafa',
    background: 'Pozadina',
    font: 'Font',
    weight: 'Težina',
    top: 'Gore',
    right: 'Desno',
    bottom: 'Dolje',
    left: 'Lijevo',
    labels: 'Oznake',
    calloutLabels: 'Natpisne Oznake',
    sectorLabels: 'Oznake Sektora',
    positionRatio: 'Omjer Pozicije',
    size: 'Veličina',
    shape: 'Oblik',
    minSize: 'Minimalna Veličina',
    maxSize: 'Maksimalna Veličina',
    legend: 'Legenda',
    position: 'Pozicija',
    markerSize: 'Veličina Oznake',
    markerStroke: 'Obrub Oznake',
    markerPadding: 'Razmak Oznake',
    itemSpacing: 'Razmak Stavki',
    itemPaddingX: 'Razmak Stavki X',
    itemPaddingY: 'Razmak Stavki Y',
    layoutHorizontalSpacing: 'Horizontalni Razmak',
    layoutVerticalSpacing: 'Vertikalni Razmak',
    strokeWidth: 'Debljina Obruba',
    offset: 'Pomak',
    offsets: 'Pomaci',
    tooltips: 'Savjeti',
    callout: 'Natpis',
    markers: 'Oznake',
    shadow: 'Sjena',
    blur: 'Zamagljenje',
    xOffset: 'X Pomak',
    yOffset: 'Y Pomak',
    lineWidth: 'Debljina Linije',
    lineDash: 'Isprekidana Linija',
    lineDashOffset: 'Pomak Isprekidanih Linija',
    scrollingZoom: 'Pomični Zum',
    scrollingStep: 'Korak Pomicanja',
    selectingZoom: 'Odabir',
    durationMillis: 'Trajanje (ms)',
    crosshairLabel: 'Oznaka',
    crosshairSnap: 'Snap na Čvor',
    normal: 'Normalno',
    bold: 'Podebljano',
    italic: 'Kurziv',
    boldItalic: 'Podebljano Kurziv',
    predefined: 'Predefinirano',
    fillOpacity: 'Prozirnost Ispune',
    strokeColor: 'Boja Linije',
    strokeOpacity: 'Prozirnost Linije',
    miniChart: 'Mini Graf',
    histogramBinCount: 'Broj Kanta',
    connectorLine: 'Povezivačka Linija',
    seriesItems: 'Stavke Serija',
    seriesItemType: 'Tip Stavke',
    seriesItemPositive: 'Pozitivno',
    seriesItemNegative: 'Negativno',
    seriesItemLabels: 'Oznake Stavki',
    columnGroup: 'Stupac',
    barGroup: 'Traka',
    pieGroup: 'Pita',
    lineGroup: 'Linija',
    scatterGroup: 'X Y (Raspršenje)',
    areaGroup: 'Područje',
    polarGroup: 'Polarni',
    statisticalGroup: 'Statistički',
    hierarchicalGroup: 'Hijerarhijski',
    specializedGroup: 'Specijalizirani',
    combinationGroup: 'Kombinacija',
    groupedColumnTooltip: 'Grupirani',
    stackedColumnTooltip: 'Naslagani',
    normalizedColumnTooltip: '100% Naslagani',
    groupedBarTooltip: 'Grupirani',
    stackedBarTooltip: 'Naslagani',
    normalizedBarTooltip: '100% Naslagani',
    pieTooltip: 'Pita',
    donutTooltip: 'Krafna',
    lineTooltip: 'Linija',
    stackedLineTooltip: 'Složen',
    normalizedLineTooltip: '100% Složen',
    groupedAreaTooltip: 'Područje',
    stackedAreaTooltip: 'Naslagano',
    normalizedAreaTooltip: '100% Naslagano',
    scatterTooltip: 'Raspršenje',
    bubbleTooltip: 'Balon',
    histogramTooltip: 'Histograma',
    radialColumnTooltip: 'Radijalni Stupac',
    radialBarTooltip: 'Radijalna Traka',
    radarLineTooltip: 'Radar Linija',
    radarAreaTooltip: 'Radar Područje',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Raspon Trake',
    rangeAreaTooltip: 'Raspon Područja',
    boxPlotTooltip: 'Box Plot',
    treemapTooltip: 'Treemap',
    sunburstTooltip: 'Sunburst',
    waterfallTooltip: 'Slap',
    heatmapTooltip: 'Toplinska Mapa',
    columnLineComboTooltip: 'Stupac i Linija',
    areaColumnComboTooltip: 'Područje i Stupac',
    customComboTooltip: 'Prilagođena Kombinacija',
    innerRadius: 'Unutarnji Radijus',
    startAngle: 'Početni Kut',
    endAngle: 'Završni Kut',
    reverseDirection: 'Obrnuti Smjer',
    groupPadding: 'Razmak Grupe',
    seriesPadding: 'Razmak Serija',
    tile: 'Pločica',
    whisker: 'Brk',
    cap: 'Poklopac',
    capLengthRatio: 'Omjer Duljine',
    labelPlacement: 'Postavljanje Oznake',
    inside: 'Unutra',
    outside: 'Vani',
    noDataToChart: 'Nema podataka za prikaz.',
    pivotChartRequiresPivotMode: 'Pivot Graf zahtijeva uključen Pivot način.',
    chartSettingsToolbarTooltip: 'Izbornik',
    chartLinkToolbarTooltip: 'Povezano s Mrežom',
    chartUnlinkToolbarTooltip: 'Nepovezano od Mreže',
    chartDownloadToolbarTooltip: 'Preuzmi Graf',
    chartMenuToolbarTooltip: 'Izbornik',
    chartEdit: 'Uredi Graf',
    chartAdvancedSettings: 'Napredne Postavke',
    chartLink: 'Poveži s Mrežom',
    chartUnlink: 'Odspoji od Mreže',
    chartDownload: 'Preuzmi Graf',
    histogramFrequency: 'Frekvencija',
    seriesChartType: 'Tip Serije Grafa',
    seriesType: 'Tip Serije',
    secondaryAxis: 'Sekundarna Os',
    seriesAdd: 'Dodaj seriju',
    categoryAdd: 'Dodaj kategoriju',
    bar: 'Traka',
    column: 'Stupac',
    histogram: 'Histograma',
    advancedSettings: 'Napredne Postavke',
    direction: 'Smjer',
    horizontal: 'Horizontalno',
    vertical: 'Vertikalno',
    seriesGroupType: 'Tip Grupe Serije',
    groupedSeriesGroupType: 'Grupirani',
    stackedSeriesGroupType: 'Naslagani',
    normalizedSeriesGroupType: '100% Naslagani',
    legendEnabled: 'Omogućeno',
    invalidColor: 'Nevažeća vrijednost boje',
    groupedColumnFull: 'Grupirani Stupac',
    stackedColumnFull: 'Naslagani Stupac',
    normalizedColumnFull: '100% Naslagani Stupac',
    groupedBarFull: 'Grupirana Traka',
    stackedBarFull: 'Naslagana Traka',
    normalizedBarFull: '100% Naslagana Traka',
    stackedAreaFull: 'Naslagano Područje',
    normalizedAreaFull: '100% Naslagano Područje',
    customCombo: 'Prilagođena Kombinacija',
    funnel: 'Lijevak',
    coneFunnel: 'Konični lijevak',
    pyramid: 'Piramida',
    funnelGroup: 'Lijevak',
    funnelTooltip: 'Lijevak',
    coneFunnelTooltip: 'Konični lijevak',
    pyramidTooltip: 'Piramida',
    dropOff: 'Odljev',
    stageLabels: 'Oznake faza',
    reverse: 'Obrnuto',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Razina ${variable}. Pritisnite ENTER za uređivanje.',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Razina ${variable}. ${variable} Pritisnite ENTER za uređivanje.',
    ariaAdvancedFilterBuilderList: 'Popis naprednog graditelja filtera',
    ariaAdvancedFilterBuilderFilterItem: 'Uvjet filtera',
    ariaAdvancedFilterBuilderGroupItem: 'Grupa filtera',
    ariaAdvancedFilterBuilderColumn: 'Stupac',
    ariaAdvancedFilterBuilderOption: 'Opcija',
    ariaAdvancedFilterBuilderValueP: 'Vrijednost',
    ariaAdvancedFilterBuilderJoinOperator: 'Operator spajanja',
    ariaAdvancedFilterInput: 'Unos naprednog filtera',
    ariaChecked: 'označeno',
    ariaColumn: 'Stupac',
    ariaColumnGroup: 'Grupa stupaca',
    ariaColumnFiltered: 'Stupac filtriran',
    ariaColumnSelectAll: 'Uključi/isključi vidljivost svih stupaca',
    ariaDateFilterInput: 'Unos filtera datuma',
    ariaDefaultListName: 'Popis',
    ariaFilterColumnsInput: 'Unos za filtriranje stupaca',
    ariaFilterFromValue: 'Filtriraj od vrijednosti',
    ariaFilterInput: 'Unos filtera',
    ariaFilterList: 'Popis filtera',
    ariaFilterToValue: 'Filtriraj do vrijednosti',
    ariaFilterValue: 'Vrijednost filtera',
    ariaFilterMenuOpen: 'Otvori izbornik filtera',
    ariaFilteringOperator: 'Operator filtriranja',
    ariaHidden: 'skriveno',
    ariaIndeterminate: 'neodređeno',
    ariaInputEditor: 'Uređivač unosa',
    ariaMenuColumn: 'Pritisnite ALT DOLJE za otvaranje izbornika stupca',
    ariaFilterColumn: 'Pritisnite CTRL ENTER za otvaranje filtera',
    ariaRowDeselect: 'Pritisnite SPACE za poništavanje odabira ovog retka',
    ariaHeaderSelection: 'Stupac s odabirom zaglavlja',
    ariaSelectAllCells: 'Pritisnite razmaknicu za odabir svih ćelija',
    ariaRowSelectAll: 'Pritisnite SPACE za uključivanje/isključivanje odabira svih redaka',
    ariaRowToggleSelection: 'Pritisnite SPACE za uključivanje/isključivanje odabira retka',
    ariaRowSelect: 'Pritisnite SPACE za odabir ovog retka',
    ariaRowSelectionDisabled: 'Odabir retka je onemogućen za ovaj redak',
    ariaSearch: 'Pretraživanje',
    ariaSortableColumn: 'Pritisnite ENTER za sortiranje',
    ariaToggleVisibility: 'Pritisnite SPACE za uključivanje/isključivanje vidljivosti',
    ariaToggleCellValue: 'Pritisnite SPACE za uključivanje/isključivanje vrijednosti ćelije',
    ariaUnchecked: 'neoznačeno',
    ariaVisible: 'vidljivo',
    ariaSearchFilterValues: 'Pretraži vrijednosti filtera',
    ariaPageSizeSelectorLabel: 'Veličina stranice',
    ariaChartMenuClose: 'Zatvori izbornik uređivanja grafikona',
    ariaChartSelected: 'Odabrano',
    ariaSkeletonCellLoadingFailed: 'Redak nije uspješno učitan',
    ariaSkeletonCellLoading: 'Podaci retka se učitavaju',
    ariaDeferSkeletonCellLoading: 'Ćelija se učitava',

    // ARIA for Batch Edit
    ariaPendingChange: 'Promjena na čekanju',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Grupe redaka',
    ariaValuesDropZonePanelLabel: 'Vrijednosti',
    ariaPivotDropZonePanelLabel: 'Oznake stupaca',
    ariaDropZoneColumnComponentDescription: 'Pritisnite DELETE za uklanjanje',
    ariaDropZoneColumnValueItemDescription: 'Pritisnite ENTER za promjenu tipa agregacije',
    ariaDropZoneColumnGroupItemDescription: 'Pritisnite ENTER za sortiranje',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' od ',
    ariaDropZoneColumnComponentSortAscending: 'uzlazno',
    ariaDropZoneColumnComponentSortDescending: 'silazno',
    ariaLabelDialog: 'Dijalog',
    ariaLabelColumnMenu: 'Izbornik stupca',
    ariaLabelColumnFilter: 'Filtar stupca',
    ariaLabelSelectField: 'Odaberi polje',

    // Cell Editor
    ariaLabelCellEditor: 'Uređivač ćelija',
    ariaValidationErrorPrefix: 'Validacija Uređivača ćelija',
    ariaLabelLoadingContextMenu: 'Učitavanje kontekstnog izbornika',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Polje s bogatim izborom',
    ariaLabelRichSelectToggleSelection: 'Pritisnite RAZMAKNICU za prebacivanje odabira',
    ariaLabelRichSelectDeselectAllItems: 'Pritisnite DELETE za poništavanje odabira svih stavki',
    ariaLabelRichSelectDeleteSelection: 'Pritisnite DELETE za poništavanje odabira stavke',
    ariaLabelTooltip: 'Alatni savjet',
    ariaLabelContextMenu: 'Kontekstni izbornik',
    ariaLabelSubMenu: 'Podizbornik',
    ariaLabelAggregationFunction: 'Funkcija agregacije',
    ariaLabelAdvancedFilterAutocomplete: 'Napredna funkcija automatskog dovršavanja filtera',
    ariaLabelAdvancedFilterBuilderAddField: 'Napredni graditelj filtera - dodajte polje',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Napredni graditelj filtera - odaberite polje stupca',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Napredni graditelj filtera - odaberite polje opcije',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Napredni graditelj filtera - odaberite polje operatera za spajanje',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Popis stupaca',
    ariaFilterPanelList: 'Popis filtara',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Dodaj polje filtera',
    ariaLabelFilterCardDelete: 'Izbriši filter',
    ariaLabelFilterCardHasEdits: 'Ima izmjene',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'Istina',
    false: 'Lažno',
    invalidDate: 'Nevažeći datum',
    invalidNumber: 'Nevažeći broj',
    january: 'Siječanj',
    february: 'Veljača',
    march: 'Ožujak',
    april: 'Travanj',
    may: 'Svibanj',
    june: 'Lipanj',
    july: 'Srpanj',
    august: 'Kolovoz',
    september: 'Rujan',
    october: 'Listopad',
    november: 'Studeni',
    december: 'Prosinac',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/GGGG',
    timeFormatSlashesMMDDYYYY: 'MM/DD/GGGG',
    timeFormatSlashesDDMMYY: 'DD/MM/GG',
    timeFormatSlashesMMDDYY: 'MM/DD/GG',
    timeFormatDotsDDMYY: 'DD.M.GG',
    timeFormatDotsMDDYY: 'M.DD.GG',
    timeFormatDashesYYYYMMDD: 'GGGG-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM GGGG',
    timeFormatHHMMSS: 'SS:MM:VV',
    timeFormatHHMMSSAmPm: 'SS:MM:VV AM/PM',
};
