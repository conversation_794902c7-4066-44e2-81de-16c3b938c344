/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_HU = {
    // Set Filter
    selectAll: '(Mindet kiválaszt)',
    selectAllSearchResults: '(Összes keresési találat kiválasztása)',
    addCurrentSelectionToFilter: 'Aktuális kiv<PERSON>t<PERSON> hozz<PERSON>ad<PERSON>a a szűrőhöz',
    searchOoo: 'Keresés...',
    blanks: '(Üres)',
    noMatches: 'Nincs találat',

    // Number Filter & Text Filter
    filterOoo: 'Szűrő...',
    equals: 'Egy<PERSON>l<PERSON>',
    notEqual: 'Nem egyenlő',
    blank: 'Üres',
    notBlank: 'Nem üres',
    empty: 'Válasszon egyet',

    // Number Filter
    lessThan: 'Kevesebb mint',
    greaterThan: 'Több mint',
    lessThanOrEqual: '<PERSON><PERSON><PERSON><PERSON> vagy egyenl<PERSON>',
    greaterThanOrEqual: 'Több vagy egyenlő',
    inRange: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    inRangeStart: 'Innen',
    inRangeEnd: 'Eddig',

    // Text Filter
    contains: 'Tartalmazza',
    notContains: 'Nem tartalmazza',
    startsWith: 'Ezzel kezdődik',
    endsWith: 'Ezzel végződik',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Előtte',
    after: 'Utána',

    // Filter Conditions
    andCondition: 'ÉS',
    orCondition: 'VAGY',

    // Filter Buttons
    applyFilter: 'Alkalmaz',
    resetFilter: 'Visszaállítás',
    clearFilter: 'Törlés',
    cancelFilter: 'Mégse',

    // Filter Titles
    textFilter: 'Szövegszűrő',
    numberFilter: 'Szám szűrő',
    dateFilter: 'Dátum szűrő',
    setFilter: 'Készlet szűrő',

    // Group Column Filter
    groupFilterSelect: 'Válassza ki a mezőt:',

    // New Filter Tool Panel
    filterSummaryInactive: 'mindent tartalmaz',
    filterSummaryContains: 'tartalmazza',
    filterSummaryNotContains: 'nem tartalmazza',
    filterSummaryTextEquals: 'egyenlő',
    filterSummaryTextNotEqual: 'nem egyenlő',
    filterSummaryStartsWith: 'kezdődik',
    filterSummaryEndsWith: 'végződik',
    filterSummaryBlank: 'üres',
    filterSummaryNotBlank: 'nem üres',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'között',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'mindent tartalmaz',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: '(${variable})',
    filterSummaryListLong: '(${variable}) és ${variable} további',
    addFilterCard: 'Szűrő hozzáadása',
    agTextColumnFilterDisplayName: 'Egyszerű szűrő',
    agNumberColumnFilterDisplayName: 'Egyszerű szűrő',
    agDateColumnFilterDisplayName: 'Egyszerű szűrő',
    agSetColumnFilterDisplayName: 'Kiválasztási szűrő',
    agMultiColumnFilterDisplayName: 'Kombinált szűrő',
    addFilterPlaceholder: 'Oszlopok keresése...',

    // Advanced Filter
    advancedFilterContains: 'tartalmazza',
    advancedFilterNotContains: 'nem tartalmazza',
    advancedFilterTextEquals: 'egyenlő',
    advancedFilterTextNotEqual: 'nem egyenlő',
    advancedFilterStartsWith: 'ezzel kezdődik',
    advancedFilterEndsWith: 'ezzel végződik',
    advancedFilterBlank: 'üres',
    advancedFilterNotBlank: 'nem üres',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'igaz',
    advancedFilterFalse: 'hamis',
    advancedFilterAnd: 'ÉS',
    advancedFilterOr: 'VAGY',
    advancedFilterApply: 'Alkalmaz',
    advancedFilterBuilder: 'Szűrő Készítő',
    advancedFilterValidationMissingColumn: 'Oszlop hiányzik',
    advancedFilterValidationMissingOption: 'Opció hiányzik',
    advancedFilterValidationMissingValue: 'Érték hiányzik',
    advancedFilterValidationInvalidColumn: 'Oszlop nem található',
    advancedFilterValidationInvalidOption: 'Opció nem található',
    advancedFilterValidationMissingQuote: 'Idézőjel hiányzik az érték végéről',
    advancedFilterValidationNotANumber: 'Érték nem szám',
    advancedFilterValidationInvalidDate: 'Érték nem érvényes dátum',
    advancedFilterValidationMissingCondition: 'Feltétel hiányzik',
    advancedFilterValidationJoinOperatorMismatch: 'A feltételek összekapcsoló operátorai azonosak kell, hogy legyenek',
    advancedFilterValidationInvalidJoinOperator: 'Összekapcsoló operátor nem található',
    advancedFilterValidationMissingEndBracket: 'Hiányzó zárózárójelek',
    advancedFilterValidationExtraEndBracket: 'Túl sok zárózárójel',
    advancedFilterValidationMessage: 'A kifejezés hibát tartalmaz. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'A kifejezés hibát tartalmaz. ${variable} a kifejezés végén.',
    advancedFilterBuilderTitle: 'Speciális Szűrő',
    advancedFilterBuilderApply: 'Alkalmaz',
    advancedFilterBuilderCancel: 'Mégsem',
    advancedFilterBuilderAddButtonTooltip: 'Szűrő vagy Csoport hozzáadása',
    advancedFilterBuilderRemoveButtonTooltip: 'Eltávolítás',
    advancedFilterBuilderMoveUpButtonTooltip: 'Mozgatás felfelé',
    advancedFilterBuilderMoveDownButtonTooltip: 'Mozgatás lefelé',
    advancedFilterBuilderAddJoin: 'Csoport hozzáadása',
    advancedFilterBuilderAddCondition: 'Szűrő hozzáadása',
    advancedFilterBuilderSelectColumn: 'Oszlop kiválasztása',
    advancedFilterBuilderSelectOption: 'Opció kiválasztása',
    advancedFilterBuilderEnterValue: 'Érték megadása...',
    advancedFilterBuilderValidationAlreadyApplied: 'Az aktuális szűrő már alkalmazva.',
    advancedFilterBuilderValidationIncomplete: 'Nem minden feltétel teljes.',
    advancedFilterBuilderValidationSelectColumn: 'Oszlopot kell választani.',
    advancedFilterBuilderValidationSelectOption: 'Opciót kell választani.',
    advancedFilterBuilderValidationEnterValue: 'Értéket kell megadni.',

    // Editor Validation Errors
    minDateValidation: 'A dátumnak későbbinek kell lennie, mint ${variable}',
    maxDateValidation: 'A dátumnak korábbinak kell lennie, mint ${variable}',
    maxLengthValidation: 'Legfeljebb ${variable} karakter hosszú lehet.',
    minValueValidation: 'Az értéknek nagyobbnak vagy egyenlőnek kell lennie ${variable}-val/vel',
    maxValueValidation: 'Az értéknek kisebbnek vagy egyenlőnek kell lennie ${variable}-val/vel',
    invalidSelectionValidation: 'Érvénytelen kiválasztás.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Oszlopok',
    filters: 'Szűrők',

    // columns tool panel
    pivotMode: 'Forgatási mód',
    groups: 'Sor csoportok',
    rowGroupColumnsEmptyMessage: 'Húzza ide a sorcsoportok beállításához',
    values: 'Értékek',
    valueColumnsEmptyMessage: 'Húzza ide az összesítéshez',
    pivots: 'Oszlop címkék',
    pivotColumnsEmptyMessage: 'Húzza ide az oszlopcímkék beállításához',

    // Header of the Default Group Column
    group: 'Csoport',

    // Row Drag
    rowDragRow: 'sor',
    rowDragRows: 'sorok',

    // Other
    loadingOoo: 'Betöltés...',
    loadingError: 'HIBA',
    noRowsToShow: 'Nincs megjeleníthető sor',
    enabled: 'Engedélyezve',

    // Menu
    pinColumn: 'Oszlop rögzítése',
    pinLeft: 'Rögzítés balra',
    pinRight: 'Rögzítés jobbra',
    noPin: 'Nincs rögzítés',
    valueAggregation: 'Érték összesítés',
    noAggregation: 'Nincs',
    autosizeThisColumn: 'Oszlop automatikus átméretezése',
    autosizeAllColumns: 'Összes oszlop automatikus átméretezése',
    groupBy: 'Csoportosítás',
    ungroupBy: 'Csoportbontás',
    ungroupAll: 'Összes csoport bontása',
    addToValues: '${variable} hozzáadása az értékekhez',
    removeFromValues: '${variable} eltávolítása az értékek közül',
    addToLabels: '${variable} hozzáadása a címkékhez',
    removeFromLabels: '${variable} eltávolítása a címkék közül',
    resetColumns: 'Oszlopok visszaállítása',
    expandAll: 'Összes sorcsoport kibontása',
    collapseAll: 'Összes sorcsoport bezárása',
    copy: 'Másolás',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Másolás fejlécekkel',
    copyWithGroupHeaders: 'Másolás csoportos fejlécekkel',
    cut: 'Kivágás',
    paste: 'Beillesztés',
    ctrlV: 'Ctrl+V',
    export: 'Exportálás',
    csvExport: 'CSV exportálás',
    excelExport: 'Excel exportálás',
    columnFilter: 'Oszlopszűrés',
    columnChooser: 'Oszlopok kiválasztása',
    chooseColumns: 'Oszlopok kiválasztása',
    sortAscending: 'Rendezés növekvő sorrendben',
    sortDescending: 'Rendezés csökkenő sorrendben',
    sortUnSort: 'Rendezés törlése',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Összeg',
    first: 'Első',
    last: 'Utolsó',
    min: 'Minimum',
    max: 'Maximum',
    none: 'Nincs',
    count: 'Darabszám',
    avg: 'Átlag',
    filteredRows: 'Szűrt',
    selectedRows: 'Kiválasztott',
    totalRows: 'Összes sor',
    totalAndFilteredRows: 'Sorok',
    more: 'Több',
    to: 'tól',
    of: ' -ből',
    page: 'Oldal',
    pageLastRowUnknown: '?',
    nextPage: 'Következő oldal',
    lastPage: 'Utolsó oldal',
    firstPage: 'Első oldal',
    previousPage: 'Előző oldal',
    pageSizeSelectorLabel: 'Oldalméret:',
    footerTotal: 'Összesen',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Görgetés ${variable} megtekintéséhez',

    // Pivoting
    pivotColumnGroupTotals: 'Összesen',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivot Diagram & Pivot Mód',
    pivotChart: 'Pivot Diagram',
    chartRange: 'Diagram Tartomány',
    columnChart: 'Oszlopdiagram',
    groupedColumn: 'Csoportosított',
    stackedColumn: 'Halmozott',
    normalizedColumn: '100% Halmozott',
    barChart: 'Sávdiagram',
    groupedBar: 'Csoportosított',
    stackedBar: 'Halmozott',
    normalizedBar: '100% Halmozott',
    pieChart: 'Tortadiagram',
    pie: 'Torta',
    donut: 'Donut',
    lineChart: 'Vonal',
    stackedLine: 'Halmozott',
    normalizedLine: '100% Halmozott',
    xyChart: 'X Y (Szórt)',
    scatter: 'Szórt',
    bubble: 'Buborék',
    areaChart: 'Területdiagram',
    area: 'Terület',
    stackedArea: 'Halmozott',
    normalizedArea: '100% Halmozott',
    histogramChart: 'Hisztogram',
    polarChart: 'Poláris',
    radarLine: 'Radar Vonal',
    radarArea: 'Radar Terület',
    nightingale: 'Nightingale',
    radialColumn: 'Radiális Oszlop',
    radialBar: 'Radiális Sáv',
    statisticalChart: 'Statisztikai',
    boxPlot: 'Box Plot',
    rangeBar: 'Tartomány Sáv',
    rangeArea: 'Tartomány Terület',
    hierarchicalChart: 'Hierarchikus',
    treemap: 'Fa diagram',
    sunburst: 'Napkitörés',
    specializedChart: 'Speciális',
    waterfall: 'Vízeseés',
    heatmap: 'Hőtérkép',
    combinationChart: 'Kombinált Diagram',
    columnLineCombo: 'Oszlop & Vonal',
    AreaColumnCombo: 'Terület & Oszlop',

    // Charts
    pivotChartTitle: 'Forgatódiagram',
    rangeChartTitle: 'Tartománydiagram',
    settings: 'Diagram',
    data: 'Beállítások',
    format: 'Testreszabás',
    categories: 'Kategóriák',
    defaultCategory: '(Nincs)',
    series: 'Sorozat',
    switchCategorySeries: 'Kategória / Sorozat váltása',
    categoryValues: 'Kategóriaértékek',
    seriesLabels: 'Sorozatcímkék',
    aggregate: 'Összegzés',
    xyValues: 'X Y értékek',
    paired: 'Párosított mód',
    axis: 'Tengely',
    xAxis: 'Vízszintes tengely',
    yAxis: 'Függőleges tengely',
    polarAxis: 'Poláris tengely',
    radiusAxis: 'Sugártengely',
    navigator: 'Navigátor',
    zoom: 'Nagyítás',
    animation: 'Animáció',
    crosshair: 'Célkereszt',
    color: 'Szín',
    thickness: 'Vastagság',
    preferredLength: 'Előnyben részesített hossz',
    xType: 'X típus',
    axisType: 'Tengely típusa',
    automatic: 'Automatikus',
    category: 'Kategória',
    number: 'Szám',
    time: 'Idő',
    timeFormat: 'Időformátum',
    autoRotate: 'Automatikus forgatás',
    labelRotation: 'Címkeforgatás',
    circle: 'Kör',
    polygon: 'Sokszög',
    square: 'Négyzet',
    cross: 'Kereszt',
    diamond: 'Rombusz',
    plus: 'Plusz',
    triangle: 'Háromszög',
    heart: 'Szív',
    orientation: 'Tájolás',
    fixed: 'Rögzített',
    parallel: 'Párhuzamos',
    perpendicular: 'Merőleges',
    radiusAxisPosition: 'Pozíció',
    ticks: 'Jelölések',
    gridLines: 'Rácsvonalak',
    width: 'Szélesség',
    height: 'Magasság',
    length: 'Hossz',
    padding: 'Kihagyás',
    spacing: 'Távolság',
    chartStyle: 'Diagram stílus',
    title: 'Cím',
    chartTitles: 'Címek',
    chartTitle: 'Diagram címe',
    chartSubtitle: 'Alcím',
    horizontalAxisTitle: 'Vízszintes tengely címe',
    verticalAxisTitle: 'Függőleges tengely címe',
    polarAxisTitle: 'Poláris tengely címe',
    titlePlaceholder: 'Diagram címe',
    background: 'Háttér',
    font: 'Betűtípus',
    weight: 'Súly',
    top: 'Felső',
    right: 'Jobb',
    bottom: 'Alsó',
    left: 'Bal',
    labels: 'Címkék',
    calloutLabels: 'Figyelmeztető címkék',
    sectorLabels: 'Szeletcímkék',
    positionRatio: 'Pozíció arány',
    size: 'Méret',
    shape: 'Alak',
    minSize: 'Minimális méret',
    maxSize: 'Maximális méret',
    legend: 'Jelmagyarázat',
    position: 'Pozíció',
    markerSize: 'Jelző méret',
    markerStroke: 'Jelző vonal',
    markerPadding: 'Jelző kihagyás',
    itemSpacing: 'Elemköz',
    itemPaddingX: 'Elem kihagyás X',
    itemPaddingY: 'Elem kihagyás Y',
    layoutHorizontalSpacing: 'Vízszintes távolság',
    layoutVerticalSpacing: 'Függőleges távolság',
    strokeWidth: 'Vonalvastagság',
    offset: 'Eltolás',
    offsets: 'Eltolások',
    tooltips: 'Eszköztippek',
    callout: 'Figyelmeztetés',
    markers: 'Jelzők',
    shadow: 'Árnyék',
    blur: 'Elmosás',
    xOffset: 'X eltolás',
    yOffset: 'Y eltolás',
    lineWidth: 'Vonalvastagság',
    lineDash: 'Szaggatott vonal',
    lineDashOffset: 'Szaggatott vonal eltolás',
    scrollingZoom: 'Görgetés',
    scrollingStep: 'Görgetési lépés',
    selectingZoom: 'Kiválasztás',
    durationMillis: 'Időtartam (ms)',
    crosshairLabel: 'Címke',
    crosshairSnap: 'Csomóponthoz rögzítés',
    normal: 'Normál',
    bold: 'Félkövér',
    italic: 'Dőlt',
    boldItalic: 'Félkövér dőlt',
    predefined: 'Előre definiált',
    fillOpacity: 'Kitöltési átlátszatlanság',
    strokeColor: 'Vonal szín',
    strokeOpacity: 'Vonal átlátszatlanság',
    miniChart: 'Mini-diagram',
    histogramBinCount: 'Bin szám',
    connectorLine: 'Összekötő vonal',
    seriesItems: 'Sorozat elemek',
    seriesItemType: 'Elem típusa',
    seriesItemPositive: 'Pozitív',
    seriesItemNegative: 'Negatív',
    seriesItemLabels: 'Elemcímkék',
    columnGroup: 'Oszlop',
    barGroup: 'Sáv',
    pieGroup: 'Torta',
    lineGroup: 'Vonal',
    scatterGroup: 'X Y (Szórás)',
    areaGroup: 'Terület',
    polarGroup: 'Poláris',
    statisticalGroup: 'Statisztikai',
    hierarchicalGroup: 'Hierarchikus',
    specializedGroup: 'Speciális',
    combinationGroup: 'Kombinált',
    groupedColumnTooltip: 'Csoportosított',
    stackedColumnTooltip: 'Halmozott',
    normalizedColumnTooltip: '100% Halmozott',
    groupedBarTooltip: 'Csoportosított',
    stackedBarTooltip: 'Halmozott',
    normalizedBarTooltip: '100% Halmozott',
    pieTooltip: 'Torta',
    donutTooltip: 'Fánk',
    lineTooltip: 'Vonal',
    stackedLineTooltip: 'Halmozott',
    normalizedLineTooltip: '100% Halmozott',
    groupedAreaTooltip: 'Terület',
    stackedAreaTooltip: 'Halmozott',
    normalizedAreaTooltip: '100% Halmozott',
    scatterTooltip: 'Szórás',
    bubbleTooltip: 'Buborék',
    histogramTooltip: 'Hisztogram',
    radialColumnTooltip: 'Radiális oszlop',
    radialBarTooltip: 'Radiális sáv',
    radarLineTooltip: 'Radar vonal',
    radarAreaTooltip: 'Radar terület',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Tartománysáv',
    rangeAreaTooltip: 'Tartományterület',
    boxPlotTooltip: 'Box diagram',
    treemapTooltip: 'Fastruktúra',
    sunburstTooltip: 'Napkitörés',
    waterfallTooltip: 'Vízesés',
    heatmapTooltip: 'Hőtérkép',
    columnLineComboTooltip: 'Oszlop és vonal',
    areaColumnComboTooltip: 'Terület és oszlop',
    customComboTooltip: 'Egyedi kombináció',
    innerRadius: 'Belső sugár',
    startAngle: 'Kezdőszög',
    endAngle: 'Végszög',
    reverseDirection: 'Irány megfordítása',
    groupPadding: 'Csoportkifutás',
    seriesPadding: 'Sorozatkifutás',
    tile: 'Csempe',
    whisker: 'Szál',
    cap: 'Sapka',
    capLengthRatio: 'Hosszarany',
    labelPlacement: 'Címkeelhelyezés',
    inside: 'Belsejében',
    outside: 'Kívül',
    noDataToChart: 'Nincs elérhető adat diagramra.',
    pivotChartRequiresPivotMode: 'A forgatódiagramhoz pivot mód bekapcsolása szükséges.',
    chartSettingsToolbarTooltip: 'Menü',
    chartLinkToolbarTooltip: 'Rácshoz csatolva',
    chartUnlinkToolbarTooltip: 'Rácstól leválasztva',
    chartDownloadToolbarTooltip: 'Diagram letöltése',
    chartMenuToolbarTooltip: 'Menü',
    chartEdit: 'Diagram szerkesztése',
    chartAdvancedSettings: 'Speciális beállítások',
    chartLink: 'Csatolás a rácshoz',
    chartUnlink: 'Leválasztás a rácsról',
    chartDownload: 'Diagram letöltése',
    histogramFrequency: 'Gyakoriság',
    seriesChartType: 'Sorozatdiagram típusa',
    seriesType: 'Sorozat típusa',
    secondaryAxis: 'Másodlagos tengely',
    seriesAdd: 'Sorozat hozzáadása',
    categoryAdd: 'Kategória hozzáadása',
    bar: 'Sáv',
    column: 'Oszlop',
    histogram: 'Hisztogram',
    advancedSettings: 'Speciális beállítások',
    direction: 'Irány',
    horizontal: 'Vízszintes',
    vertical: 'Függőleges',
    seriesGroupType: 'Csoport típusa',
    groupedSeriesGroupType: 'Csoportosított',
    stackedSeriesGroupType: 'Halmozott',
    normalizedSeriesGroupType: '100% Halmozott',
    legendEnabled: 'Bekapcsolva',
    invalidColor: 'Színérték érvénytelen',
    groupedColumnFull: 'Csoportosított oszlop',
    stackedColumnFull: 'Halmozott oszlop',
    normalizedColumnFull: '100% Halmozott oszlop',
    groupedBarFull: 'Csoportosított sáv',
    stackedBarFull: 'Halmozott sáv',
    normalizedBarFull: '100% Halmozott sáv',
    stackedAreaFull: 'Halmozott terület',
    normalizedAreaFull: '100% Halmozott terület',
    customCombo: 'Egyedi kombináció',
    funnel: 'Tölcsér',
    coneFunnel: 'Kúpos Tölcsér',
    pyramid: 'Piramis',
    funnelGroup: 'Tölcsér',
    funnelTooltip: 'Tölcsér',
    coneFunnelTooltip: 'Kúpos Tölcsér',
    pyramidTooltip: 'Piramis',
    dropOff: 'Hanyatlás',
    stageLabels: 'Szint Címkék',
    reverse: 'Visszafordít',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Szint ${variable}. Nyomja meg az ENTER billentyűt a szerkesztéshez.',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Szint ${variable}. ${variable} Nyomja meg az ENTER billentyűt a szerkesztéshez.',
    ariaAdvancedFilterBuilderList: 'Speciális szűrő készítő lista',
    ariaAdvancedFilterBuilderFilterItem: 'Szűrési feltétel',
    ariaAdvancedFilterBuilderGroupItem: 'Szűrési csoport',
    ariaAdvancedFilterBuilderColumn: 'Oszlop',
    ariaAdvancedFilterBuilderOption: 'Opció',
    ariaAdvancedFilterBuilderValueP: 'Érték',
    ariaAdvancedFilterBuilderJoinOperator: 'Egyesítő operátor',
    ariaAdvancedFilterInput: 'Speciális szűrő bevitel',
    ariaChecked: 'kiválasztva',
    ariaColumn: 'Oszlop',
    ariaColumnGroup: 'Oszlop csoport',
    ariaColumnFiltered: 'Szűrt oszlop',
    ariaColumnSelectAll: 'Az összes oszlop láthatóságának váltása',
    ariaDateFilterInput: 'Dátum szűrő bevitel',
    ariaDefaultListName: 'Lista',
    ariaFilterColumnsInput: 'Oszlopok szűrése bevitel',
    ariaFilterFromValue: 'Szűrés értéktől',
    ariaFilterInput: 'Szűrő bevitel',
    ariaFilterList: 'Szűrő lista',
    ariaFilterToValue: 'Szűrés értékig',
    ariaFilterValue: 'Szűrő érték',
    ariaFilterMenuOpen: 'Szűrő menü megnyitása',
    ariaFilteringOperator: 'Szűrő operátor',
    ariaHidden: 'elrejtve',
    ariaIndeterminate: 'határozatlan',
    ariaInputEditor: 'Bevitel szerkesztő',
    ariaMenuColumn: 'Nyomja le az ALT + LE billentyűt az oszlop menü megnyitásához',
    ariaFilterColumn: 'Nyomja meg a CTRL + ENTER billentyűket a szűrő megnyitásához',
    ariaRowDeselect: 'Nyomja le a SPACE billentyűt ezen sor kiválasztásának megszüntetéséhez',
    ariaHeaderSelection: 'Oszlop fejléc kiválasztásával',
    ariaSelectAllCells: 'Nyomja meg a szóközt az összes cella kiválasztásához',
    ariaRowSelectAll: 'Nyomja le a SPACE billentyűt az összes sor kiválasztásának váltásához',
    ariaRowToggleSelection: 'Nyomja le a SPACE billentyűt a sor kiválasztásának váltásához',
    ariaRowSelect: 'Nyomja le a SPACE billentyűt ezen sor kiválasztásához',
    ariaRowSelectionDisabled: 'A sor kiválasztása le van tiltva ehhez a sorhoz',
    ariaSearch: 'Keresés',
    ariaSortableColumn: 'Nyomja meg az ENTER billentyűt a rendezéshez',
    ariaToggleVisibility: 'Nyomja meg a SPACE billentyűt a láthatóság váltásához',
    ariaToggleCellValue: 'Nyomja meg a SPACE billentyűt a cella értékének váltásához',
    ariaUnchecked: 'nincs kiválasztva',
    ariaVisible: 'látható',
    ariaSearchFilterValues: 'Keresés szűrő értékek között',
    ariaPageSizeSelectorLabel: 'Oldal méret',
    ariaChartMenuClose: 'Diagram szerkesztési menü bezárása',
    ariaChartSelected: 'Kiválasztva',
    ariaSkeletonCellLoadingFailed: 'A sor betöltése sikertelen volt',
    ariaSkeletonCellLoading: 'A sor adatai betöltődnek',
    ariaDeferSkeletonCellLoading: 'A cella betöltődik',

    // ARIA for Batch Edit
    ariaPendingChange: 'Függőben lévő változtatás',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Sor csoportok',
    ariaValuesDropZonePanelLabel: 'Értékek',
    ariaPivotDropZonePanelLabel: 'Oszlop címkék',
    ariaDropZoneColumnComponentDescription: 'Nyomja meg a DELETE-tá, hogy eltávolítsa',
    ariaDropZoneColumnValueItemDescription: 'Nyomja meg az ENTER-t, hogy megváltoztassa az aggregáció típusát',
    ariaDropZoneColumnGroupItemDescription: 'Nyomja meg az ENTER-t, hogy rendezze',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' közül ',
    ariaDropZoneColumnComponentSortAscending: 'növekvő',
    ariaDropZoneColumnComponentSortDescending: 'csökkenő',
    ariaLabelDialog: 'Párbeszédpanel',
    ariaLabelColumnMenu: 'Oszlop menü',
    ariaLabelColumnFilter: 'Oszlop szűrő',
    ariaLabelSelectField: 'Mező kiválasztása',

    // Cell Editor
    ariaLabelCellEditor: 'Cella szerkesztő',
    ariaValidationErrorPrefix: 'Cella szerkesztő érvényesítés',
    ariaLabelLoadingContextMenu: 'Kontextusmenü betöltése',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Gazdag kiválasztási mező',
    ariaLabelRichSelectToggleSelection: 'Nyomja meg a SPACE-t a kiválasztás váltásához',
    ariaLabelRichSelectDeselectAllItems: 'Nyomja meg a DELETE-t minden elem kiválasztásának megszüntetéséhez',
    ariaLabelRichSelectDeleteSelection: 'Nyomja meg a DELETE-t az elem kiválasztásának megszüntetéséhez',
    ariaLabelTooltip: 'Tooltip',
    ariaLabelContextMenu: 'Kontekstusmenü',
    ariaLabelSubMenu: 'Almenü',
    ariaLabelAggregationFunction: 'Összesítő függvény',
    ariaLabelAdvancedFilterAutocomplete: 'Fejlett szűrő automatikus kiegészítés',
    ariaLabelAdvancedFilterBuilderAddField: 'Fejlett szűrő készítő mező hozzáadása',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Fejlett szűrő készítő oszlop kiválasztó mező',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Fejlett szűrő készítő opció kiválasztó mező',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Fejlett szűrő készítő összekapcsoló operátor mező',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Oszloplista',
    ariaFilterPanelList: 'Szűrőlista',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Szűrőmező hozzáadása',
    ariaLabelFilterCardDelete: 'Szűrő törlése',
    ariaLabelFilterCardHasEdits: 'Módosításokat tartalmaz',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Igaz',
    false: 'Hamis',
    invalidDate: 'Érvénytelen dátum',
    invalidNumber: 'Érvénytelen szám',
    january: 'Január',
    february: 'Február',
    march: 'Március',
    april: 'Április',
    may: 'Május',
    june: 'Június',
    july: 'Július',
    august: 'Augusztus',
    september: 'Szeptember',
    october: 'Október',
    november: 'November',
    december: 'December',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'NN/HH/ÉÉÉÉ',
    timeFormatSlashesMMDDYYYY: 'HH/NN/ÉÉÉÉ',
    timeFormatSlashesDDMMYY: 'NN/HH/ÉÉ',
    timeFormatSlashesMMDDYY: 'HH/NN/ÉÉ',
    timeFormatDotsDDMYY: 'NN.H.ÉÉ',
    timeFormatDotsMDDYY: 'H.NN.ÉÉ',
    timeFormatDashesYYYYMMDD: 'ÉÉÉÉ-HH-NN',
    timeFormatSpacesDDMMMMYYYY: 'NN MMMM ÉÉÉÉ',
    timeFormatHHMMSS: 'ÓÓ:PP:MP',
    timeFormatHHMMSSAmPm: 'ÓÓ:PP:MP DE/DU',
};
