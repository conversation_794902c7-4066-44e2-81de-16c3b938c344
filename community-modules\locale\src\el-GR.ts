/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_GR = {
    // Set Filter
    selectAll: '(Επιλογή όλων)',
    selectAllSearchResults: '(Επιλογή όλων των αποτελεσμάτων αναζήτησης)',
    addCurrentSelectionToFilter: 'Προσθήκη τρέχουσας επιλογής στο φίλτρο',
    searchOoo: 'Αναζήτηση...',
    blanks: '(Κενά)',
    noMatches: 'Δεν βρέθηκαν αποτελέσματα',

    // Number Filter & Text Filter
    filterOoo: 'Φίλτρο...',
    equals: 'Ίσο με',
    notEqual: 'Δεν είναι ίσο με',
    blank: 'Κενό',
    notBlank: 'Δεν είναι κενό',
    empty: 'Επιλέξτε ένα',

    // Number Filter
    lessThan: 'Λιγότερο από',
    greaterThan: 'Μεγαλύτερο από',
    lessThanOrEqual: 'Λιγότερο ή ίσο με',
    greaterThanOrEqual: 'Μεγαλύτερο ή ίσο με',
    inRange: 'Ανάμεσα',
    inRangeStart: 'Από',
    inRangeEnd: 'Έως',

    // Text Filter
    contains: 'Περιέχει',
    notContains: 'Δεν περιέχει',
    startsWith: 'Ξεκινά με',
    endsWith: 'Τελειώνει με',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Πριν',
    after: 'Μετά',

    // Filter Conditions
    andCondition: 'ΚΑΙ',
    orCondition: 'Ή',

    // Filter Buttons
    applyFilter: 'Εφαρμογή',
    resetFilter: 'Επαναφορά',
    clearFilter: 'Εκκαθάριση',
    cancelFilter: 'Ακύρωση',

    // Filter Titles
    textFilter: 'Φίλτρο Κειμένου',
    numberFilter: 'Φίλτρο Αριθμού',
    dateFilter: 'Φίλτρο Ημερομηνίας',
    setFilter: 'Φίλτρο Συνόλου',

    // Group Column Filter
    groupFilterSelect: 'Επιλέξτε πεδίο:',

    // New Filter Tool Panel
    filterSummaryInactive: 'είναι (Όλα)',
    filterSummaryContains: 'περιέχει',
    filterSummaryNotContains: 'δεν περιέχει',
    filterSummaryTextEquals: 'ισούται με',
    filterSummaryTextNotEqual: 'δεν ισούται με',
    filterSummaryStartsWith: 'αρχίζει με',
    filterSummaryEndsWith: 'τελειώνει με',
    filterSummaryBlank: 'είναι κενό',
    filterSummaryNotBlank: 'δεν είναι κενό',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'μεταξύ',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'είναι (Όλα)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'είναι (${variable})',
    filterSummaryListLong: 'είναι (${variable}) και ${variable} περισσότερα',
    addFilterCard: 'Προσθήκη Φίλτρου',
    agTextColumnFilterDisplayName: 'Απλό Φίλτρο',
    agNumberColumnFilterDisplayName: 'Απλό Φίλτρο',
    agDateColumnFilterDisplayName: 'Απλό Φίλτρο',
    agSetColumnFilterDisplayName: 'Φίλτρο Επιλογής',
    agMultiColumnFilterDisplayName: 'Σύνθετο Φίλτρο',
    addFilterPlaceholder: 'Αναζήτηση στηλών...',

    // Advanced Filter
    advancedFilterContains: 'περιέχει',
    advancedFilterNotContains: 'δεν περιέχει',
    advancedFilterTextEquals: 'είναι ίσο με',
    advancedFilterTextNotEqual: 'δεν είναι ίσο με',
    advancedFilterStartsWith: 'αρχίζει με',
    advancedFilterEndsWith: 'τελειώνει με',
    advancedFilterBlank: 'είναι κενό',
    advancedFilterNotBlank: 'δεν είναι κενό',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'είναι αληθές',
    advancedFilterFalse: 'είναι ψευδές',
    advancedFilterAnd: 'ΚΑΙ',
    advancedFilterOr: 'Ή',
    advancedFilterApply: 'Εφαρμογή',
    advancedFilterBuilder: 'Κατασκευαστής',
    advancedFilterValidationMissingColumn: 'Η στήλη λείπει',
    advancedFilterValidationMissingOption: 'Η επιλογή λείπει',
    advancedFilterValidationMissingValue: 'Η τιμή λείπει',
    advancedFilterValidationInvalidColumn: 'Η στήλη δεν βρέθηκε',
    advancedFilterValidationInvalidOption: 'Η επιλογή δεν βρέθηκε',
    advancedFilterValidationMissingQuote: 'Η τιμή λείπει τελική εισαγωγική',
    advancedFilterValidationNotANumber: 'Η τιμή δεν είναι αριθμός',
    advancedFilterValidationInvalidDate: 'Η τιμή δεν είναι έγκυρη ημερομηνία',
    advancedFilterValidationMissingCondition: 'Η συνθήκη λείπει',
    advancedFilterValidationJoinOperatorMismatch: 'Οι τελεστές ένωσης εντός συνθήκης πρέπει να είναι οι ίδιοι',
    advancedFilterValidationInvalidJoinOperator: 'Ο τελεστής ένωσης δεν βρέθηκε',
    advancedFilterValidationMissingEndBracket: 'Λείπει το τελικά στηρίγμα',
    advancedFilterValidationExtraEndBracket: 'Περισσότερα από ένα τελικά στηρίγματα',
    advancedFilterValidationMessage: 'Η έκφραση έχει σφάλμα. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Η έκφραση έχει σφάλμα. ${variable} στο τέλος της έκφρασης.',
    advancedFilterBuilderTitle: 'Προηγμένο Φίλτρο',
    advancedFilterBuilderApply: 'Εφαρμογή',
    advancedFilterBuilderCancel: 'Άκυρο',
    advancedFilterBuilderAddButtonTooltip: 'Προσθήκη Φίλτρου ή Ομάδας',
    advancedFilterBuilderRemoveButtonTooltip: 'Αφαίρεση',
    advancedFilterBuilderMoveUpButtonTooltip: 'Μετακίνηση Πάνω',
    advancedFilterBuilderMoveDownButtonTooltip: 'Μετακίνηση Κάτω',
    advancedFilterBuilderAddJoin: 'Προσθήκη Ομάδας',
    advancedFilterBuilderAddCondition: 'Προσθήκη Φίλτρου',
    advancedFilterBuilderSelectColumn: 'Επιλέξτε μια στήλη',
    advancedFilterBuilderSelectOption: 'Επιλέξτε μια επιλογή',
    advancedFilterBuilderEnterValue: 'Εισάγετε μια τιμή...',
    advancedFilterBuilderValidationAlreadyApplied: 'Το τρέχον φίλτρο έχει ήδη εφαρμοστεί.',
    advancedFilterBuilderValidationIncomplete: 'Δεν έχουν ολοκληρωθεί όλες οι συνθήκες.',
    advancedFilterBuilderValidationSelectColumn: 'Πρέπει να επιλέξετε μια στήλη.',
    advancedFilterBuilderValidationSelectOption: 'Πρέπει να επιλέξετε μια επιλογή.',
    advancedFilterBuilderValidationEnterValue: 'Πρέπει να εισάγετε μια τιμή.',

    // Editor Validation Errors
    minDateValidation: 'Η ημερομηνία πρέπει να είναι μετά από ${variable}',
    maxDateValidation: 'Η ημερομηνία πρέπει να είναι πριν από ${variable}',
    maxLengthValidation: 'Πρέπει να είναι ${variable} χαρακτήρες ή λιγότερο.',
    minValueValidation: 'Πρέπει να είναι μεγαλύτερο ή ίσο με ${variable}',
    maxValueValidation: 'Πρέπει να είναι μικρότερο ή ίσο με ${variable}',
    invalidSelectionValidation: 'Μη έγκυρη επιλογή.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Στήλες',
    filters: 'Φίλτρα',

    // columns tool panel
    pivotMode: 'Λειτουργία Περιστροφής',
    groups: 'Ομάδες Γραμμών',
    rowGroupColumnsEmptyMessage: 'Σύρετε εδώ για να ορίσετε ομάδες γραμμών',
    values: 'Τιμές',
    valueColumnsEmptyMessage: 'Σύρετε εδώ για να συγκεντρώσετε',
    pivots: 'Ετικέτες Στηλών',
    pivotColumnsEmptyMessage: 'Σύρετε εδώ για να ορίσετε ετικέτες στηλών',

    // Header of the Default Group Column
    group: 'Ομάδα',

    // Row Drag
    rowDragRow: 'γραμμή',
    rowDragRows: 'γραμμές',

    // Other
    loadingOoo: 'Φόρτωση...',
    loadingError: 'ΣΦΑΛΜΑ',
    noRowsToShow: 'Δεν υπάρχουν γραμμές για εμφάνιση',
    enabled: 'Ενεργοποιημένο',

    // Menu
    pinColumn: 'Καρφίτσωμα Στήλης',
    pinLeft: 'Καρφίτσωμα Αριστερά',
    pinRight: 'Καρφίτσωμα Δεξιά',
    noPin: 'Χωρίς Καρφίτσωμα',
    valueAggregation: 'Συγκέντρωση Τιμών',
    noAggregation: 'Καμία',
    autosizeThisColumn: 'Αυτόματο Μέγεθος Αυτής της Στήλης',
    autosizeAllColumns: 'Αυτόματο Μέγεθος Όλων των Στηλών',
    groupBy: 'Ομαδοποίηση κατά',
    ungroupBy: 'Διακοπή Ομαδοποίησης κατά',
    ungroupAll: 'Διακοπή Ομαδοποίησης Όλων',
    addToValues: 'Προσθήκη ${variable} στις τιμές',
    removeFromValues: 'Αφαίρεση ${variable} από τις τιμές',
    addToLabels: 'Προσθήκη ${variable} στις ετικέτες',
    removeFromLabels: 'Αφαίρεση ${variable} από τις ετικέτες',
    resetColumns: 'Επαναφορά Στηλών',
    expandAll: 'Επέκταση Όλων των Ομάδων Γραμμών',
    collapseAll: 'Κλείσιμο Όλων των Ομάδων Γραμμών',
    copy: 'Αντιγραφή',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Αντιγραφή με Κεφαλίδες',
    copyWithGroupHeaders: 'Αντιγραφή με Ομαδικές Κεφαλίδες',
    cut: 'Αποκοπή',
    paste: 'Επικόλληση',
    ctrlV: 'Ctrl+V',
    export: 'Εξαγωγή',
    csvExport: 'Εξαγωγή σε CSV',
    excelExport: 'Εξαγωγή σε Excel',
    columnFilter: 'Φίλτρο Στηλών',
    columnChooser: 'Επιλογή Στηλών',
    chooseColumns: 'Επιλογή Στηλών',
    sortAscending: 'Επιλογή Αύξουσας Ταξινόμησης',
    sortDescending: 'Επιλογή Φθίνουσας Ταξινόμησης',
    sortUnSort: 'Καθαρισμός Ταξινόμησης',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Άθροισμα',
    first: 'Πρώτο',
    last: 'Τελευταίο',
    min: 'Ελάχ.',
    max: 'Μέγ.',
    none: 'Κανένα',
    count: 'Αριθμός',
    avg: 'Μέσος Όρος',
    filteredRows: 'Φιλτράρισμα',
    selectedRows: 'Επιλεγμένες',
    totalRows: 'Συνολικές Γραμμές',
    totalAndFilteredRows: 'Γραμμές',
    more: 'Περισσότερα',
    to: 'έως',
    of: 'από',
    page: 'Σελίδα',
    pageLastRowUnknown: '?',
    nextPage: 'Επόμενη Σελίδα',
    lastPage: 'Τελευταία Σελίδα',
    firstPage: 'Πρώτη Σελίδα',
    previousPage: 'Προηγούμενη Σελίδα',
    pageSizeSelectorLabel: 'Μέγεθος Σελίδας:',
    footerTotal: 'Σύνολο',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Μετακίνηση ${variable} σε προβολή',

    // Pivoting
    pivotColumnGroupTotals: 'Σύνολο',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Γράφημα Περιστροφής & Λειτουργία Περιστροφής',
    pivotChart: 'Γράφημα Περιστροφής',
    chartRange: 'Εύρος Γραφήματος',
    columnChart: 'Στήλη',
    groupedColumn: 'Ομαδοποιημένη',
    stackedColumn: 'Σωρευτική',
    normalizedColumn: '100% Σωρευτική',
    barChart: 'Ράβδος',
    groupedBar: 'Ομαδοποιημένη',
    stackedBar: 'Σωρευτική',
    normalizedBar: '100% Σωρευτική',
    pieChart: 'Πίτα',
    pie: 'Πίτα',
    donut: 'Ντόνατ',
    lineChart: 'Γραμμή',
    stackedLine: 'Σωρευμένο',
    normalizedLine: '100% Σωρευμένο',
    xyChart: 'X Y (Scatter)',
    scatter: 'Διασπορά',
    bubble: 'Φούσκα',
    areaChart: 'Περιοχή',
    area: 'Περιοχή',
    stackedArea: 'Σωρευτική',
    normalizedArea: '100% Σωρευτική',
    histogramChart: 'Ιστόγραμμα',
    polarChart: 'Πολικό',
    radarLine: 'Radar Γραμμής',
    radarArea: 'Radar Περιοχής',
    nightingale: 'Nightingale',
    radialColumn: 'Ακτινωτή Στήλη',
    radialBar: 'Ακτινωτή Ράβδος',
    statisticalChart: 'Στατιστικό',
    boxPlot: 'Διάγραμμα Κουτιού',
    rangeBar: 'Ράβδος Εύρους',
    rangeArea: 'Περιοχή Εύρους',
    hierarchicalChart: 'Ιεραρχικό',
    treemap: 'Χάρτης Δέντρου',
    sunburst: 'Ήλιος',
    specializedChart: 'Εξειδικευμένο',
    waterfall: 'Καταρράκτης',
    heatmap: 'Θερμικός Χάρτης',
    combinationChart: 'Συνδυασμένο',
    columnLineCombo: 'Στήλη και Γραμμή',
    AreaColumnCombo: 'Περιοχή & Στήλη',

    // Charts
    pivotChartTitle: 'Συγκεντρωτικό Γράφημα',
    rangeChartTitle: 'Γράφημα Εύρους',
    settings: 'Γράφημα',
    data: 'Ρύθμιση',
    format: 'Εξατομίκευση',
    categories: 'Κατηγορίες',
    defaultCategory: '(Καμία)',
    series: 'Σειρές',
    switchCategorySeries: 'Αλλαγή Κατηγορίας / Σειράς',
    categoryValues: 'Τιμές Κατηγορίας',
    seriesLabels: 'Ετικέτες Σειρών',
    aggregate: 'Συγκεντρωτικά',
    xyValues: 'Τιμές Χ Υ',
    paired: 'Συνδυασμένη Λειτουργία',
    axis: 'Άξονας',
    xAxis: 'Οριζόντιος Άξονας',
    yAxis: 'Κατακόρυφος Άξονας',
    polarAxis: 'Πολικός Άξονας',
    radiusAxis: 'Άξονας Ακτίνας',
    navigator: 'Πλοηγός',
    zoom: 'Μεγέθυνση',
    animation: 'Κινούμενα Γραφικά',
    crosshair: 'Διχώς',
    color: 'Χρώμα',
    thickness: 'Πάχος',
    preferredLength: 'Προτιμώμενο Μήκος',
    xType: 'Τύπος Χ',
    axisType: 'Τύπος Άξονα',
    automatic: 'Αυτόματο',
    category: 'Κατηγορία',
    number: 'Αριθμός',
    time: 'Χρόνος',
    timeFormat: 'Μορφή Χρόνου',
    autoRotate: 'Αυτόματη Περιστροφή',
    labelRotation: 'Περιστροφή',
    circle: 'Κύκλος',
    polygon: 'Πολύγωνο',
    square: 'Τετράγωνο',
    cross: 'Σταυρός',
    diamond: 'Διαμάντι',
    plus: 'Συν',
    triangle: 'Τρίγωνο',
    heart: 'Καρδιά',
    orientation: 'Προσανατολισμός',
    fixed: 'Σταθερό',
    parallel: 'Παράλληλο',
    perpendicular: 'Κατακόρυφο',
    radiusAxisPosition: 'Θέση',
    ticks: 'Τικ',
    gridLines: 'Γραμμές Πλέγματος',
    width: 'Πλάτος',
    height: 'Ύψος',
    length: 'Μήκος',
    padding: 'Εσοχή',
    spacing: 'Διάκενο',
    chartStyle: 'Στυλ Γραφήματος',
    title: 'Τίτλος',
    chartTitles: 'Τίτλοι',
    chartTitle: 'Τίτλος Γραφήματος',
    chartSubtitle: 'Υπότιτλος',
    horizontalAxisTitle: 'Τίτλος Οριζόντιου Άξονα',
    verticalAxisTitle: 'Τίτλος Κατακόρυφου Άξονα',
    polarAxisTitle: 'Τίτλος Πολικού Άξονα',
    titlePlaceholder: 'Τίτλος Γραφήματος',
    background: 'Ιστορικό',
    font: 'Γραμματοσειρά',
    weight: 'Βάρος',
    top: 'Πάνω',
    right: 'Δεξιά',
    bottom: 'Κάτω',
    left: 'Αριστερά',
    labels: 'Ετικέτες',
    calloutLabels: 'Ετικέτες Κλήσης',
    sectorLabels: 'Ετικέτες Τομέων',
    positionRatio: 'Αναλογία Θέσης',
    size: 'Μέγεθος',
    shape: 'Σχήμα',
    minSize: 'Ελάχιστο Μέγεθος',
    maxSize: 'Μέγιστο Μέγεθος',
    legend: 'Υπόμνημα',
    position: 'Θέση',
    markerSize: 'Μέγεθος Δείκτη',
    markerStroke: 'Βάρος Δείκτη',
    markerPadding: 'Ολοκληρωτική Εσοχή',
    itemSpacing: 'Διάκενο Στοιχείου',
    itemPaddingX: 'Εσοχή Στοιχείου X',
    itemPaddingY: 'Εσοχή Στοιχείου Y',
    layoutHorizontalSpacing: 'Οριζόντιο Διάκενο',
    layoutVerticalSpacing: 'Κατακόρυφο Διάκενο',
    strokeWidth: 'Πάχος Γραμμής',
    offset: 'Μετατόπιση',
    offsets: 'Μετατοπίσεις',
    tooltips: 'Υποδείξεις',
    callout: 'Κλήση',
    markers: 'Δείκτες',
    shadow: 'Σκιά',
    blur: 'Θάμπωμα',
    xOffset: 'Μετατόπιση Χ',
    yOffset: 'Μετατόπιση Υ',
    lineWidth: 'Πάχος Γραμμής',
    lineDash: 'Παύλα Γραμμής',
    lineDashOffset: 'Μετατόπιση Παυλας',
    scrollingZoom: 'Κύλιση',
    scrollingStep: 'Βήμα Κύλισης',
    selectingZoom: 'Επιλογή',
    durationMillis: 'Διάρκεια (μσ)',
    crosshairLabel: 'Ετικέτα',
    crosshairSnap: 'Συλλήψτε τον Κόμβο',
    normal: 'Κανονικό',
    bold: 'Έντονο',
    italic: 'Πλάγιο',
    boldItalic: 'Έντονο Πλάγιο',
    predefined: 'Προκαθορισμένο',
    fillOpacity: 'Αδιαφάνεια Συμπλήρωσης',
    strokeColor: 'Χρώμα Γραμμής',
    strokeOpacity: 'Αδιαφάνεια Γραμμής',
    miniChart: 'Μίνι-Γράφημα',
    histogramBinCount: 'Πλήθος Κάδων',
    connectorLine: 'Γραμμή Σύνδεσης',
    seriesItems: 'Στοιχεία Σειρών',
    seriesItemType: 'Τύπος Στοιχείου',
    seriesItemPositive: 'Θετικό',
    seriesItemNegative: 'Αρνητικό',
    seriesItemLabels: 'Ετικέτες Στοιχείων',
    columnGroup: 'Ομάδα Στήλης',
    barGroup: 'Ομάδα Ραβδογράφημα',
    pieGroup: 'Ομάδα Πίτας',
    lineGroup: 'Ομάδα Γραμμής',
    scatterGroup: 'Χ Υ (Σκόρπιο)',
    areaGroup: 'Ομάδα Περιοχής',
    polarGroup: 'Πολική Ομάδα',
    statisticalGroup: 'Στατιστική Ομάδα',
    hierarchicalGroup: 'Ιεραρχική Ομάδα',
    specializedGroup: 'Ειδικευμένη Ομάδα',
    combinationGroup: 'Συνδυαστική Ομάδα',
    groupedColumnTooltip: 'Ομαδοποιημένο',
    stackedColumnTooltip: 'Στοιβαγμένο',
    normalizedColumnTooltip: '100% Στοιβαγμένο',
    groupedBarTooltip: 'Ομαδοποιημένο',
    stackedBarTooltip: 'Στοιβαγμένο',
    normalizedBarTooltip: '100% Στοιβαγμένο',
    pieTooltip: 'Πίτα',
    donutTooltip: 'Ντόνατ',
    lineTooltip: 'Γραμμή',
    stackedLineTooltip: 'Συσσωρευμένο',
    normalizedLineTooltip: 'Συσσωρευμένο 100%',
    groupedAreaTooltip: 'Περιοχή',
    stackedAreaTooltip: 'Στοιβαγμένη',
    normalizedAreaTooltip: '100% Στοιβαγμένη',
    scatterTooltip: 'Σκόρπιο',
    bubbleTooltip: 'Φούσκα',
    histogramTooltip: 'Ιστόγραμμα',
    radialColumnTooltip: 'Ακτινική Στήλη',
    radialBarTooltip: 'Ακτινικό Ραβδογράφημα',
    radarLineTooltip: 'Ραντάρ Γραμμή',
    radarAreaTooltip: 'Ραντάρ Περιοχή',
    nightingaleTooltip: 'Αηδόνι',
    rangeBarTooltip: 'Μπάρα Εύρους',
    rangeAreaTooltip: 'Περιοχή Εύρους',
    boxPlotTooltip: 'Κουτί Σχεδίου',
    treemapTooltip: 'Χάρτης Δέντρου',
    sunburstTooltip: 'Ηλιακή Έκλαμψη',
    waterfallTooltip: 'Καταρράκτης',
    heatmapTooltip: 'Χάρτης Θερμότητας',
    columnLineComboTooltip: 'Στήλη & Γραμμή',
    areaColumnComboTooltip: 'Περιοχή & Στήλη',
    customComboTooltip: 'Προσαρμοσμένος Συνδυασμός',
    innerRadius: 'Εσωτερική Ακτίνα',
    startAngle: 'Γωνία Εκκίνησης',
    endAngle: 'Τελική Γωνία',
    reverseDirection: 'Αντίθετη Κατεύθυνση',
    groupPadding: 'Εσοχή Ομάδας',
    seriesPadding: 'Εσοχή Σειράς',
    tile: 'Πλακάκι',
    whisker: 'Μουστάκι',
    cap: 'Καπάκι',
    capLengthRatio: 'Λόγος Μήκους',
    labelPlacement: 'Τοποθέτηση Ετικέτας',
    inside: 'Εσωτερικά',
    outside: 'Εξωτερικά',
    noDataToChart: 'Δεν υπάρχουν διαθέσιμα δεδομένα για το γράφημα.',
    pivotChartRequiresPivotMode: 'Το Συγκεντρωτικό Γράφημα απαιτεί την ενεργοποίηση της λειτουργίας Συγκεντρωτικού.',
    chartSettingsToolbarTooltip: 'Μενού',
    chartLinkToolbarTooltip: 'Συνδεδεμένο στο Πλέγμα',
    chartUnlinkToolbarTooltip: 'Αποσυνδεδεμένο από το Πλέγμα',
    chartDownloadToolbarTooltip: 'Λήψη Γραφήματος',
    chartMenuToolbarTooltip: 'Μενού',
    chartEdit: 'Επεξεργασία Γραφήματος',
    chartAdvancedSettings: 'Προχωρημένες Ρυθμίσεις',
    chartLink: 'Σύνδεση στο Πλέγμα',
    chartUnlink: 'Αποσύνδεση από το Πλέγμα',
    chartDownload: 'Λήψη Γραφήματος',
    histogramFrequency: 'Συχνότητα',
    seriesChartType: 'Τύπος Γραφήματος Σειράς',
    seriesType: 'Τύπος Σειράς',
    secondaryAxis: 'Δευτερεύων Άξονας',
    seriesAdd: 'Προσθήκη σειράς',
    categoryAdd: 'Προσθήκη κατηγορίας',
    bar: 'Ραβδογράφημα',
    column: 'Στήλη',
    histogram: 'Ιστόγραμμα',
    advancedSettings: 'Προχωρημένες Ρυθμίσεις',
    direction: 'Κατεύθυνση',
    horizontal: 'Οριζόντιο',
    vertical: 'Κατακόρυφο',
    seriesGroupType: 'Τύπος Ομάδας',
    groupedSeriesGroupType: 'Ομαδοποιημένο',
    stackedSeriesGroupType: 'Στοιβαγμένο',
    normalizedSeriesGroupType: '100% Στοιβαγμένο',
    legendEnabled: 'Ενεργοποιημένο',
    invalidColor: 'Η τιμή του χρώματος δεν είναι έγκυρη',
    groupedColumnFull: 'Ομαδοποιημένη Στήλη',
    stackedColumnFull: 'Στοιβαγμένη Στήλη',
    normalizedColumnFull: '100% Στοιβαγμένη Στήλη',
    groupedBarFull: 'Ομαδοποιημένο Ραβδογράφημα',
    stackedBarFull: 'Στοιβαγμένο Ραβδογράφημα',
    normalizedBarFull: '100% Στοιβαγμένο Ραβδογράφημα',
    stackedAreaFull: 'Στοιβαγμένη Περιοχή',
    normalizedAreaFull: '100% Στοιβαγμένη Περιοχή',
    customCombo: 'Προσαρμοσμένος Συνδυασμός',
    funnel: 'Χοάνη',
    coneFunnel: 'Κωνική Χοάνη',
    pyramid: 'Πυραμίδα',
    funnelGroup: 'Χοάνη',
    funnelTooltip: 'Χοάνη',
    coneFunnelTooltip: 'Κωνική Χοάνη',
    pyramidTooltip: 'Πυραμίδα',
    dropOff: 'Μείωση',
    stageLabels: 'Ετικέτες Σταδίων',
    reverse: 'Αντιστροφή',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Επίπεδο ${variable}. Πατήστε ENTER για επεξεργασία',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Επίπεδο ${variable}. ${variable} Πατήστε ENTER για επεξεργασία.',
    ariaAdvancedFilterBuilderList: 'Λίστα Προχωρημένου Κατασκευαστή Φίλτρων',
    ariaAdvancedFilterBuilderFilterItem: 'Συνθήκη Φίλτρου',
    ariaAdvancedFilterBuilderGroupItem: 'Ομάδα Φίλτρου',
    ariaAdvancedFilterBuilderColumn: 'Στήλη',
    ariaAdvancedFilterBuilderOption: 'Επιλογή',
    ariaAdvancedFilterBuilderValueP: 'Τιμή',
    ariaAdvancedFilterBuilderJoinOperator: 'Τελεστής Συνένωσης',
    ariaAdvancedFilterInput: 'Εισαγωγή Προχωρημένου Φίλτρου',
    ariaChecked: 'ελεγμένο',
    ariaColumn: 'Στήλη',
    ariaColumnGroup: 'Ομάδα Στήλης',
    ariaColumnFiltered: 'Φιλτραρισμένη Στήλη',
    ariaColumnSelectAll: 'Εναλλαγή Όλων των Ορατότητας Στηλών',
    ariaDateFilterInput: 'Εισαγωγή Φίλτρου Ημερομηνίας',
    ariaDefaultListName: 'Λίστα',
    ariaFilterColumnsInput: 'Εισαγωγή Φιλτραρίσματος Στηλών',
    ariaFilterFromValue: 'Φίλτρο από τιμή',
    ariaFilterInput: 'Εισαγωγή Φίλτρου',
    ariaFilterList: 'Λίστα Φίλτρων',
    ariaFilterToValue: 'Φίλτρο σε τιμή',
    ariaFilterValue: 'Τιμή Φίλτρου',
    ariaFilterMenuOpen: 'Άνοιγμα Μενού Φίλτρου',
    ariaFilteringOperator: 'Τελεστής Φιλτραρίσματος',
    ariaHidden: 'κρυφό',
    ariaIndeterminate: 'απροσδιόριστο',
    ariaInputEditor: 'Επεξεργαστής Εισαγωγής',
    ariaMenuColumn: 'Πατήστε ALT DOWN για άνοιγμα μενού στήλης',
    ariaFilterColumn: 'Πατήστε CTRL ENTER για άνοιγμα φίλτρου',
    ariaRowDeselect: 'Πατήστε SPACE για αποεπιλογή αυτής της γραμμής',
    ariaHeaderSelection: 'Στήλη με Επιλογή Κεφαλίδων',
    ariaSelectAllCells: 'Πατήστε Space για επιλογή όλων των κελιών',
    ariaRowSelectAll: 'Πατήστε SPACE για αλλαγή επιλογής όλων των γραμμών',
    ariaRowToggleSelection: 'Πατήστε SPACE για εναλλαγή επιλογής γραμμής',
    ariaRowSelect: 'Πατήστε SPACE για επιλογή αυτής της γραμμής',
    ariaRowSelectionDisabled: 'Η επιλογή γραμμής έχει απενεργοποιηθεί για αυτή τη γραμμή',
    ariaSearch: 'Αναζήτηση',
    ariaSortableColumn: 'Πατήστε ENTER για ταξινόμηση',
    ariaToggleVisibility: 'Πατήστε SPACE για αλλαγή ορατότητας',
    ariaToggleCellValue: 'Πατήστε SPACE για εναλλαγή τιμής κελιού',
    ariaUnchecked: 'μη ελεγμένο',
    ariaVisible: 'ορατό',
    ariaSearchFilterValues: 'Αναζήτηση τιμών φίλτρου',
    ariaPageSizeSelectorLabel: 'Μέγεθος Σελίδας',
    ariaChartMenuClose: 'Κλείσιμο Μενού Επεξεργασίας Γραφήματος',
    ariaChartSelected: 'Επιλεγμένο',
    ariaSkeletonCellLoadingFailed: 'Αποτυχία φόρτωσης γραμμής',
    ariaSkeletonCellLoading: 'Τα δεδομένα γραμμής φορτώνονται',
    ariaDeferSkeletonCellLoading: 'Το κύτταρο φορτώνεται',

    // ARIA for Batch Edit
    ariaPendingChange: 'Εκκρεμής αλλαγή',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Ομάδες Σειρών',
    ariaValuesDropZonePanelLabel: 'Τιμές',
    ariaPivotDropZonePanelLabel: 'Ετικέτες Στηλών',
    ariaDropZoneColumnComponentDescription: 'Πιέστε DELETE για να αφαιρέσετε',
    ariaDropZoneColumnValueItemDescription: 'Πιέστε ENTER για να αλλάξετε τον τύπο συνάθροισης',
    ariaDropZoneColumnGroupItemDescription: 'Πιέστε ENTER για να ταξινομήσετε',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' από ',
    ariaDropZoneColumnComponentSortAscending: 'αύξουσα',
    ariaDropZoneColumnComponentSortDescending: 'φθίνουσα',
    ariaLabelDialog: 'Διαλογή',
    ariaLabelColumnMenu: 'Μενού Στήλης',
    ariaLabelColumnFilter: 'Φίλτρο Στήλης',
    ariaLabelSelectField: 'Επιλογή Πεδίου',

    // Cell Editor
    ariaLabelCellEditor: 'Επεξεργαστής Κελιού',
    ariaValidationErrorPrefix: 'Επιβεβαίωση Επεξεργαστή Κελιού',
    ariaLabelLoadingContextMenu: 'Φόρτωση Μενού Περιβάλλοντος',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Πεδίο Rich Select',
    ariaLabelRichSelectToggleSelection: 'Πατήστε SPACE για εναλλαγή επιλογής',
    ariaLabelRichSelectDeselectAllItems: 'Πατήστε DELETE για αποεπιλογή όλων των αντικειμένων',
    ariaLabelRichSelectDeleteSelection: 'Πατήστε DELETE για αποεπιλογή αντικειμένου',
    ariaLabelTooltip: 'Εργαλείο Συμβουλών',
    ariaLabelContextMenu: 'Μενού Περιεχομένου',
    ariaLabelSubMenu: 'Υπομενού',
    ariaLabelAggregationFunction: 'Συνάρτηση Συνολικοποίησης',
    ariaLabelAdvancedFilterAutocomplete: 'Αυτόματη Συμπλήρωση Προχωρημένου Φίλτρου',
    ariaLabelAdvancedFilterBuilderAddField: 'Προσθήκη Πεδίου Δημιουργού Προχωρημένου Φίλτρου',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Επιλογή Στήλης Δημιουργού Προχωρημένου Φίλτρου',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Επιλογή Επιλογής Δημιουργού Προχωρημένου Φίλτρου',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Επιλογή Τελεστή Σύνδεσης Δημιουργού Προχωρημένου Φίλτρου',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Λίστα Στηλών',
    ariaFilterPanelList: 'Λίστα Φίλτρων',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Προσθήκη πεδίου φίλτρου',
    ariaLabelFilterCardDelete: 'Διαγραφή φίλτρου',
    ariaLabelFilterCardHasEdits: 'Έχει επεξεργασίες',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Αληθές',
    false: 'Ψευδές',
    invalidDate: 'Μη έγκυρη ημερομηνία',
    invalidNumber: 'Μη έγκυρος αριθμός',
    january: 'Ιανουάριος',
    february: 'Φεβρουάριος',
    march: 'Μάρτιος',
    april: 'Απρίλιος',
    may: 'Μάιος',
    june: 'Ιούνιος',
    july: 'Ιούλιος',
    august: 'Αύγουστος',
    september: 'Σεπτέμβριος',
    october: 'Οκτώβριος',
    november: 'Νοέμβριος',
    december: 'Δεκέμβριος',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'ΗΗ/ΜΜ/ΕΕΕΕ',
    timeFormatSlashesMMDDYYYY: 'ΜΜ/ΗΗ/ΕΕΕΕ',
    timeFormatSlashesDDMMYY: 'ΗΗ/ΜΜ/ΕΕ',
    timeFormatSlashesMMDDYY: 'ΜΜ/ΗΗ/ΕΕ',
    timeFormatDotsDDMYY: 'ΗΗ.Μ.ΕΕ',
    timeFormatDotsMDDYY: 'Μ.ΗΗ.ΕΕ',
    timeFormatDashesYYYYMMDD: 'ΕΕΕΕ-ΜΜ-ΗΗ',
    timeFormatSpacesDDMMMMYYYY: 'ΗΗ ΜΜΜΜ ΕΕΕΕ',
    timeFormatHHMMSS: 'ΩΩ:ΛΛ:ΔΔ',
    timeFormatHHMMSSAmPm: 'ΩΩ:ΛΛ:ΔΔ ΠΜ/ΜΜ',
};
