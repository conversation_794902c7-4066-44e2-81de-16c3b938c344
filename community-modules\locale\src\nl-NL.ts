/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_NL = {
    // Set Filter
    selectAll: '(Alles selecteren)',
    selectAllSearchResults: '(Alle zoekresultaten selecteren)',
    addCurrentSelectionToFilter: 'Huidige selectie toevoegen aan filter',
    searchOoo: 'Zoeken...',
    blanks: '(<PERSON><PERSON><PERSON>)',
    noMatches: 'Gee<PERSON> overeenko<PERSON>ten',

    // Number Filter & Text Filter
    filterOoo: 'Filter...',
    equals: 'Gelijk aan',
    notEqual: 'Niet gelijk aan',
    blank: 'Leeg',
    notBlank: 'Niet leeg',
    empty: 'Kies een',

    // Number Filter
    lessThan: 'Minder dan',
    greaterThan: 'Groter dan',
    lessThanOrEqual: '<PERSON>er dan of gelijk aan',
    greaterThanOrEqual: '<PERSON><PERSON><PERSON> dan of gelijk aan',
    inRange: 'Tu<PERSON>',
    inRangeStart: 'Van',
    inRangeEnd: 'Tot',

    // Text Filter
    contains: 'Bevat',
    notContains: 'Bevat niet',
    startsWith: 'Begint met',
    endsWith: 'Eindigt met',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Voor',
    after: 'Na',

    // Filter Conditions
    andCondition: 'EN',
    orCondition: 'OF',

    // Filter Buttons
    applyFilter: 'Toepassen',
    resetFilter: 'Resetten',
    clearFilter: 'Wissen',
    cancelFilter: 'Annuleren',

    // Filter Titles
    textFilter: 'Tekstfilter',
    numberFilter: 'Getallenfilter',
    dateFilter: 'Datumfilter',
    setFilter: 'Setfilter',

    // Group Column Filter
    groupFilterSelect: 'Selecteer veld:',

    // New Filter Tool Panel
    filterSummaryInactive: 'is (Alles)',
    filterSummaryContains: 'bevat',
    filterSummaryNotContains: 'bevat niet',
    filterSummaryTextEquals: 'is gelijk aan',
    filterSummaryTextNotEqual: 'is niet gelijk aan',
    filterSummaryStartsWith: 'begint met',
    filterSummaryEndsWith: 'eindigt met',
    filterSummaryBlank: 'is leeg',
    filterSummaryNotBlank: 'is niet leeg',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'tussen',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'is (Alles)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'is (${variable})',
    filterSummaryListLong: 'is (${variable}) en ${variable} meer',
    addFilterCard: 'Filter toevoegen',
    agTextColumnFilterDisplayName: 'Eenvoudig Filter',
    agNumberColumnFilterDisplayName: 'Eenvoudig Filter',
    agDateColumnFilterDisplayName: 'Eenvoudig Filter',
    agSetColumnFilterDisplayName: 'Selectie Filter',
    agMultiColumnFilterDisplayName: 'Combinatie Filter',
    addFilterPlaceholder: 'Zoek kolommen...',

    // Advanced Filter
    advancedFilterContains: 'bevat',
    advancedFilterNotContains: 'bevat-niet',
    advancedFilterTextEquals: 'is-gelijk-aan',
    advancedFilterTextNotEqual: 'is-niet-gelijk-aan',
    advancedFilterStartsWith: 'begint-met',
    advancedFilterEndsWith: 'eindigt-met',
    advancedFilterBlank: 'is-leeg',
    advancedFilterNotBlank: 'is-niet-leeg',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'is-waar',
    advancedFilterFalse: 'is-niet-waar',
    advancedFilterAnd: 'EN',
    advancedFilterOr: 'OF',
    advancedFilterApply: 'Toepassen',
    advancedFilterBuilder: 'Bouwer',
    advancedFilterValidationMissingColumn: 'Kolom ontbreekt',
    advancedFilterValidationMissingOption: 'Optie ontbreekt',
    advancedFilterValidationMissingValue: 'Waarde ontbreekt',
    advancedFilterValidationInvalidColumn: 'Kolom niet gevonden',
    advancedFilterValidationInvalidOption: 'Optie niet gevonden',
    advancedFilterValidationMissingQuote: 'Waarde mist een aanhalingsteken aan het einde',
    advancedFilterValidationNotANumber: 'Waarde is geen getal',
    advancedFilterValidationInvalidDate: 'Waarde is geen geldige datum',
    advancedFilterValidationMissingCondition: 'Voorwaarde ontbreekt',
    advancedFilterValidationJoinOperatorMismatch: 'Logische operatoren binnen een voorwaarde moeten gelijk zijn',
    advancedFilterValidationInvalidJoinOperator: 'Logische operator niet gevonden',
    advancedFilterValidationMissingEndBracket: 'Ontbrekende eindhaak',
    advancedFilterValidationExtraEndBracket: 'Te veel eindhaken',
    advancedFilterValidationMessage: 'Uitdrukking bevat een fout. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Uitdrukking bevat een fout. ${variable} aan het einde van de uitdrukking.',
    advancedFilterBuilderTitle: 'Geavanceerd filter',
    advancedFilterBuilderApply: 'Toepassen',
    advancedFilterBuilderCancel: 'Annuleren',
    advancedFilterBuilderAddButtonTooltip: 'Filter of groep toevoegen',
    advancedFilterBuilderRemoveButtonTooltip: 'Verwijderen',
    advancedFilterBuilderMoveUpButtonTooltip: 'Omhoog verplaatsen',
    advancedFilterBuilderMoveDownButtonTooltip: 'Omlaag verplaatsen',
    advancedFilterBuilderAddJoin: 'Groep toevoegen',
    advancedFilterBuilderAddCondition: 'Filter toevoegen',
    advancedFilterBuilderSelectColumn: 'Selecteer een kolom',
    advancedFilterBuilderSelectOption: 'Selecteer een optie',
    advancedFilterBuilderEnterValue: 'Voer een waarde in...',
    advancedFilterBuilderValidationAlreadyApplied: 'Huidige filter al toegepast.',
    advancedFilterBuilderValidationIncomplete: 'Niet alle voorwaarden zijn voltooid.',
    advancedFilterBuilderValidationSelectColumn: 'Moet een kolom selecteren.',
    advancedFilterBuilderValidationSelectOption: 'Moet een optie selecteren.',
    advancedFilterBuilderValidationEnterValue: 'Moet een waarde invoeren.',

    // Editor Validation Errors
    minDateValidation: 'Datum moet na ${variable} zijn',
    maxDateValidation: 'Datum moet voor ${variable} zijn',
    maxLengthValidation: 'Moet ${variable} tekens of minder zijn.',
    minValueValidation: 'Moet groter dan of gelijk aan ${variable} zijn',
    maxValueValidation: 'Moet kleiner dan of gelijk aan ${variable} zijn',
    invalidSelectionValidation: 'Ongeldige selectie.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Kolommen',
    filters: 'Filters',

    // columns tool panel
    pivotMode: 'Draaimodus',
    groups: 'Rijgroepen',
    rowGroupColumnsEmptyMessage: 'Sleep hierheen om rijen te groeperen',
    values: 'Waarden',
    valueColumnsEmptyMessage: 'Sleep hierheen om te aggregeren',
    pivots: 'Kolomlabels',
    pivotColumnsEmptyMessage: 'Sleep hierheen om kolomlabels te instellen',

    // Header of the Default Group Column
    group: 'Groep',

    // Row Drag
    rowDragRow: 'rij',
    rowDragRows: 'rijen',

    // Other
    loadingOoo: 'Laden...',
    loadingError: 'FOUT',
    noRowsToShow: 'Geen rijen om weer te geven',
    enabled: 'Ingeschakeld',

    // Menu
    pinColumn: 'Kolom vastzetten',
    pinLeft: 'Links vastzetten',
    pinRight: 'Rechts vastzetten',
    noPin: 'Niet vastzetten',
    valueAggregation: 'Waarde-aggregering',
    noAggregation: 'Geen',
    autosizeThisColumn: 'Deze kolom automatisch aanpassen',
    autosizeAllColumns: 'Alle kolommen automatisch aanpassen',
    groupBy: 'Groeperen op',
    ungroupBy: 'Degroeperen op',
    ungroupAll: 'Alle groepen opheffen',
    addToValues: 'Voeg ${variable} toe aan waarden',
    removeFromValues: 'Verwijder ${variable} van waarden',
    addToLabels: 'Voeg ${variable} toe aan labels',
    removeFromLabels: 'Verwijder ${variable} van labels',
    resetColumns: 'Kolommen resetten',
    expandAll: 'Alle rijen groepen uitvouwen',
    collapseAll: 'Alle rijen groepen inklappen',
    copy: 'Kopiëren',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopiëren met kopteksten',
    copyWithGroupHeaders: 'Kopiëren met groep kopteksten',
    cut: 'Knippen',
    paste: 'Plakken',
    ctrlV: 'Ctrl+V',
    export: 'Exporteren',
    csvExport: 'CSV Export',
    excelExport: 'Excel Export',
    columnFilter: 'Kolomfilter',
    columnChooser: 'Kolommen kiezen',
    chooseColumns: 'Kolommen Kiezen',
    sortAscending: 'Oplopend sorteren',
    sortDescending: 'Aflopend sorteren',
    sortUnSort: 'Sortering wissen',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Som',
    first: 'Eerste',
    last: 'Laatste',
    min: 'Min',
    max: 'Max',
    none: 'Geen',
    count: 'Aantal',
    avg: 'Gemiddelde',
    filteredRows: 'Gefilterd',
    selectedRows: 'Geselecteerd',
    totalRows: 'Totaal Aantal Rijen',
    totalAndFilteredRows: 'Rijen',
    more: 'Meer',
    to: 'tot',
    of: 'van',
    page: 'Pagina',
    pageLastRowUnknown: '?',
    nextPage: 'Volgende Pagina',
    lastPage: 'Laatste Pagina',
    firstPage: 'Eerste Pagina',
    previousPage: 'Vorige Pagina',
    pageSizeSelectorLabel: 'Pagina Grootte:',
    footerTotal: 'Totaal',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Scroll ${variable} in beeld',

    // Pivoting
    pivotColumnGroupTotals: 'Totaal',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Draaidiagram & Draaistand',
    pivotChart: 'Draaidiagram',
    chartRange: 'Diagrambereik',
    columnChart: 'Kolom',
    groupedColumn: 'Gegroepeerd',
    stackedColumn: 'Gestapeld',
    normalizedColumn: '100% Gestapeld',
    barChart: 'Staaf',
    groupedBar: 'Gegroepeerd',
    stackedBar: 'Gestapeld',
    normalizedBar: '100% Gestapeld',
    pieChart: 'Taart',
    pie: 'Taart',
    donut: 'Donut',
    lineChart: 'Lijn',
    stackedLine: 'Gestapeld',
    normalizedLine: '100% Gestapeld',
    xyChart: 'X Y (Spreiding)',
    scatter: 'Spreiding',
    bubble: 'Bubble',
    areaChart: 'Gebied',
    area: 'Gebied',
    stackedArea: 'Gestapeld',
    normalizedArea: '100% Gestapeld',
    histogramChart: 'Histogram',
    polarChart: 'Polair',
    radarLine: 'Radar Lijn',
    radarArea: 'Radar Gebied',
    nightingale: 'Nightingale',
    radialColumn: 'Radiale Kolom',
    radialBar: 'Radiale Staaf',
    statisticalChart: 'Statistisch',
    boxPlot: 'Boxplot',
    rangeBar: 'Reeks Staaf',
    rangeArea: 'Reeks Gebied',
    hierarchicalChart: 'Hiërarchisch',
    treemap: 'Boomkaart',
    sunburst: 'Zonnevlam',
    specializedChart: 'Gespecialiseerd',
    waterfall: 'Waterval',
    heatmap: 'Warmtekaart',
    combinationChart: 'Combinatie',
    columnLineCombo: 'Kolom & Lijn',
    AreaColumnCombo: 'Gebied & Kolom',

    // Charts
    pivotChartTitle: 'Draai Grafiek',
    rangeChartTitle: 'Bereik Grafiek',
    settings: 'Grafiek',
    data: 'Instellen',
    format: 'Aanpassen',
    categories: 'Categorieën',
    defaultCategory: '(Geen)',
    series: 'Reeksen',
    switchCategorySeries: 'Wissel Categorie / Reeks',
    categoryValues: 'Categorie Waarden',
    seriesLabels: 'Reeks Labels',
    aggregate: 'Aggregeren',
    xyValues: 'X Y Waarden',
    paired: 'Gekoppelde Modus',
    axis: 'As',
    xAxis: 'Horizontale As',
    yAxis: 'Verticale As',
    polarAxis: 'Polar As',
    radiusAxis: 'Radius As',
    navigator: 'Navigator',
    zoom: 'Zoom',
    animation: 'Animatie',
    crosshair: 'Kruisdraad',
    color: 'Kleur',
    thickness: 'Dikte',
    preferredLength: 'Voorkeurslengte',
    xType: 'X Type',
    axisType: 'As Type',
    automatic: 'Automatisch',
    category: 'Categorie',
    number: 'Nummer',
    time: 'Tijd',
    timeFormat: 'Tijdformaat',
    autoRotate: 'Auto Rotatie',
    labelRotation: 'Rotatie',
    circle: 'Cirkel',
    polygon: 'Polygoon',
    square: 'Vierkant',
    cross: 'Kruis',
    diamond: 'Diamant',
    plus: 'Plus',
    triangle: 'Driehoek',
    heart: 'Hart',
    orientation: 'Oriëntatie',
    fixed: 'Vast',
    parallel: 'Parallel',
    perpendicular: 'Loodrecht',
    radiusAxisPosition: 'Positie',
    ticks: 'Tikken',
    gridLines: 'Rasterlijnen',
    width: 'Breedte',
    height: 'Hoogte',
    length: 'Lengte',
    padding: 'Opvulling',
    spacing: 'Afstand',
    chartStyle: 'Grafiekstijl',
    title: 'Titel',
    chartTitles: 'Titels',
    chartTitle: 'Grafiektitel',
    chartSubtitle: 'Ondertitel',
    horizontalAxisTitle: 'Horizontale As Titel',
    verticalAxisTitle: 'Verticale As Titel',
    polarAxisTitle: 'Polar As Titel',
    titlePlaceholder: 'Grafiektitel',
    background: 'Achtergrond',
    font: 'Lettertype',
    weight: 'Gewicht',
    top: 'Boven',
    right: 'Rechts',
    bottom: 'Onder',
    left: 'Links',
    labels: 'Labels',
    calloutLabels: 'Uitroep Labels',
    sectorLabels: 'Sector Labels',
    positionRatio: 'Positie Verhouding',
    size: 'Grootte',
    shape: 'Vorm',
    minSize: 'Minimale Grootte',
    maxSize: 'Maximale Grootte',
    legend: 'Legenda',
    position: 'Positie',
    markerSize: 'Markergrootte',
    markerStroke: 'Markeromtrek',
    markerPadding: 'Markeropvulling',
    itemSpacing: 'Itemafstand',
    itemPaddingX: 'Item Opvulling X',
    itemPaddingY: 'Item Opvulling Y',
    layoutHorizontalSpacing: 'Horizontale Afstand',
    layoutVerticalSpacing: 'Verticale Afstand',
    strokeWidth: 'Omtrekbreedte',
    offset: 'Offset',
    offsets: 'Offsets',
    tooltips: 'Toelichtingen',
    callout: 'Uitroep',
    markers: 'Markeringen',
    shadow: 'Schaduw',
    blur: 'Vervagen',
    xOffset: 'X Offset',
    yOffset: 'Y Offset',
    lineWidth: 'Lijnbreedte',
    lineDash: 'Lijnstreep',
    lineDashOffset: 'Streep Offset',
    scrollingZoom: 'Scrollen',
    scrollingStep: 'Scrollstap',
    selectingZoom: 'Selecteren',
    durationMillis: 'Duur (ms)',
    crosshairLabel: 'Label',
    crosshairSnap: 'Vastklikken op Knoop',
    normal: 'Normaal',
    bold: 'Vetgedrukt',
    italic: 'Cursief',
    boldItalic: 'Vet Cursief',
    predefined: 'Voorgedefinieerd',
    fillOpacity: 'Vuldoorzichtigheid',
    strokeColor: 'Lijnkleur',
    strokeOpacity: 'Lijndoorzichtigheid',
    miniChart: 'Mini-Grafiek',
    histogramBinCount: 'Bak aantal',
    connectorLine: 'Connector Lijn',
    seriesItems: 'Reeks Items',
    seriesItemType: 'Item Type',
    seriesItemPositive: 'Positief',
    seriesItemNegative: 'Negatief',
    seriesItemLabels: 'Item Labels',
    columnGroup: 'Kolom',
    barGroup: 'Staaf',
    pieGroup: 'Taart',
    lineGroup: 'Lijn',
    scatterGroup: 'X Y (Spreiding)',
    areaGroup: 'Gebied',
    polarGroup: 'Polar',
    statisticalGroup: 'Statistisch',
    hierarchicalGroup: 'Hiërarchisch',
    specializedGroup: 'Gespecialiseerd',
    combinationGroup: 'Combinatie',
    groupedColumnTooltip: 'Gegroepeerd',
    stackedColumnTooltip: 'Gestapeld',
    normalizedColumnTooltip: '100% Gestapeld',
    groupedBarTooltip: 'Gegroepeerd',
    stackedBarTooltip: 'Gestapeld',
    normalizedBarTooltip: '100% Gestapeld',
    pieTooltip: 'Taart',
    donutTooltip: 'Donut',
    lineTooltip: 'Lijn',
    stackedLineTooltip: 'Gestapeld',
    normalizedLineTooltip: '100% Gestapeld',
    groupedAreaTooltip: 'Gebied',
    stackedAreaTooltip: 'Gestapeld',
    normalizedAreaTooltip: '100% Gestapeld',
    scatterTooltip: 'Spreiding',
    bubbleTooltip: 'Bel',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Radiale Kolom',
    radialBarTooltip: 'Radiale Staaf',
    radarLineTooltip: 'Radar Lijn',
    radarAreaTooltip: 'Radar Gebied',
    nightingaleTooltip: 'Nachtegaal',
    rangeBarTooltip: 'Bereik Staaf',
    rangeAreaTooltip: 'Bereik Gebied',
    boxPlotTooltip: 'Box Plot',
    treemapTooltip: 'Boomdiagram',
    sunburstTooltip: 'Zonnestraal',
    waterfallTooltip: 'Waterval',
    heatmapTooltip: 'Warmtekaart',
    columnLineComboTooltip: 'Kolom & Lijn',
    areaColumnComboTooltip: 'Gebied & Kolom',
    customComboTooltip: 'Aangepaste Combinatie',
    innerRadius: 'Binnenstraal',
    startAngle: 'Start Hoek',
    endAngle: 'Eind Hoek',
    reverseDirection: 'Omkeren',
    groupPadding: 'Groep Opvulling',
    seriesPadding: 'Reeks Opvulling',
    tile: 'Tegel',
    whisker: 'Snavel',
    cap: 'Kap',
    capLengthRatio: 'Lengte Verhouding',
    labelPlacement: 'Label Plaatsing',
    inside: 'Binnen',
    outside: 'Buiten',
    noDataToChart: 'Geen gegevens beschikbaar om in een grafiek weer te geven.',
    pivotChartRequiresPivotMode: 'Draai Grafiek vereist Draai Modus ingeschakeld.',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Gekoppeld aan Raster',
    chartUnlinkToolbarTooltip: 'Ontkoppeld van Raster',
    chartDownloadToolbarTooltip: 'Download Grafiek',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Bewerk Grafiek',
    chartAdvancedSettings: 'Geavanceerde Instellingen',
    chartLink: 'Koppelen aan Raster',
    chartUnlink: 'Ontkoppelen van Raster',
    chartDownload: 'Download Grafiek',
    histogramFrequency: 'Frequentie',
    seriesChartType: 'Reeks Grafiek Type',
    seriesType: 'Reeks Type',
    secondaryAxis: 'Secundaire As',
    seriesAdd: 'Voeg een reeks toe',
    categoryAdd: 'Voeg een categorie toe',
    bar: 'Staaf',
    column: 'Kolom',
    histogram: 'Histogram',
    advancedSettings: 'Geavanceerde Instellingen',
    direction: 'Richting',
    horizontal: 'Horizontaal',
    vertical: 'Verticaal',
    seriesGroupType: 'Groep Type',
    groupedSeriesGroupType: 'Gegroepeerd',
    stackedSeriesGroupType: 'Gestapeld',
    normalizedSeriesGroupType: '100% Gestapeld',
    legendEnabled: 'Ingeschakeld',
    invalidColor: 'Kleurwaarde is ongeldig',
    groupedColumnFull: 'Gegroepeerde Kolom',
    stackedColumnFull: 'Gestapelde Kolom',
    normalizedColumnFull: '100% Gestapelde Kolom',
    groupedBarFull: 'Gegroepeerde Staaf',
    stackedBarFull: 'Gestapelde Staaf',
    normalizedBarFull: '100% Gestapelde Staaf',
    stackedAreaFull: 'Gestapeld Gebied',
    normalizedAreaFull: '100% Gestapeld Gebied',
    customCombo: 'Aangepaste Combinatie',
    funnel: 'Trechter',
    coneFunnel: 'Kegel Trechter',
    pyramid: 'Piramide',
    funnelGroup: 'Trechter',
    funnelTooltip: 'Trechter',
    coneFunnelTooltip: 'Kegel Trechter',
    pyramidTooltip: 'Piramide',
    dropOff: 'Uitval',
    stageLabels: 'Stadiumlabels',
    reverse: 'Omkeren',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Niveau ${variable}. Druk op ENTER om te bewerken',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Niveau ${variable}. ${variable} Druk op ENTER om te bewerken.',
    ariaAdvancedFilterBuilderList: 'Geavanceerde Filter Builder Lijst',
    ariaAdvancedFilterBuilderFilterItem: 'Filter Voorwaarde',
    ariaAdvancedFilterBuilderGroupItem: 'Filter Groep',
    ariaAdvancedFilterBuilderColumn: 'Kolom',
    ariaAdvancedFilterBuilderOption: 'Optie',
    ariaAdvancedFilterBuilderValueP: 'Waarde',
    ariaAdvancedFilterBuilderJoinOperator: 'Koppeloperator',
    ariaAdvancedFilterInput: 'Geavanceerde Filter Invoer',
    ariaChecked: 'geselecteerd',
    ariaColumn: 'Kolom',
    ariaColumnGroup: 'Kolom Groep',
    ariaColumnFiltered: 'Kolom Gefilterd',
    ariaColumnSelectAll: 'Schakel zichtbaarheid van alle kolommen in/uit',
    ariaDateFilterInput: 'Datum Filter Invoer',
    ariaDefaultListName: 'Lijst',
    ariaFilterColumnsInput: 'Filter Kolommen Invoer',
    ariaFilterFromValue: 'Filter vanuit waarde',
    ariaFilterInput: 'Filter Invoer',
    ariaFilterList: 'Filter Lijst',
    ariaFilterToValue: 'Filter naar waarde',
    ariaFilterValue: 'Filter Waarde',
    ariaFilterMenuOpen: 'Open Filtermenu',
    ariaFilteringOperator: 'Filter Operator',
    ariaHidden: 'verborgen',
    ariaIndeterminate: 'onbepaald',
    ariaInputEditor: 'Invoer Editor',
    ariaMenuColumn: 'Druk op ALT DOWN om het kolommenmenu te openen',
    ariaFilterColumn: 'Druk op CTRL ENTER om te filteren',
    ariaRowDeselect: 'Druk op SPATIE om deze rij te deselecteren',
    ariaHeaderSelection: 'Kolom met Hoofdselectie',
    ariaSelectAllCells: 'Druk op Spatiebalk om alle cellen te selecteren',
    ariaRowSelectAll: 'Druk op SPATIE om alle rijen te selecteren/deselecteren',
    ariaRowToggleSelection: 'Druk op SPATIE om rijselectie te schakelen',
    ariaRowSelect: 'Druk op SPATIE om deze rij te selecteren',
    ariaRowSelectionDisabled: 'Rijselectie is uitgeschakeld voor deze rij',
    ariaSearch: 'Zoeken',
    ariaSortableColumn: 'Druk op ENTER om te sorteren',
    ariaToggleVisibility: 'Druk op SPATIE om zichtbaarheid te schakelen',
    ariaToggleCellValue: 'Druk op SPATIE om celwaarde te schakelen',
    ariaUnchecked: 'niet geselecteerd',
    ariaVisible: 'zichtbaar',
    ariaSearchFilterValues: 'Zoek filterwaarden',
    ariaPageSizeSelectorLabel: 'Pagina Grootte',
    ariaChartMenuClose: 'Sluit Grafiek Bewerkmenu',
    ariaChartSelected: 'Geselecteerd',
    ariaSkeletonCellLoadingFailed: 'Rij laden is mislukt',
    ariaSkeletonCellLoading: 'Rijgegevens worden geladen',
    ariaDeferSkeletonCellLoading: 'Cel wordt geladen',

    // ARIA for Batch Edit
    ariaPendingChange: 'Wijziging in behandeling',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Rijgroepen',
    ariaValuesDropZonePanelLabel: 'Waarden',
    ariaPivotDropZonePanelLabel: 'Kolomlabels',
    ariaDropZoneColumnComponentDescription: 'Druk op DELETE om te verwijderen',
    ariaDropZoneColumnValueItemDescription: 'Druk op ENTER om het aggregatietype te wijzigen',
    ariaDropZoneColumnGroupItemDescription: 'Druk op ENTER om te sorteren',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' of ',
    ariaDropZoneColumnComponentSortAscending: 'oplopend',
    ariaDropZoneColumnComponentSortDescending: 'aflopend',
    ariaLabelDialog: 'Dialoog',
    ariaLabelColumnMenu: 'Kolommenu',
    ariaLabelColumnFilter: 'Kolomfilter',
    ariaLabelSelectField: 'Selectievak',

    // Cell Editor
    ariaLabelCellEditor: 'Celleditor',
    ariaValidationErrorPrefix: 'Celleditorvalidatie',
    ariaLabelLoadingContextMenu: 'Contextmenu laden',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Rijk Selectieveld',
    ariaLabelRichSelectToggleSelection: 'Druk op SPATIE om selectie te wisselen',
    ariaLabelRichSelectDeselectAllItems: 'Druk op VERWIJDEREN om alle items te deselecteren',
    ariaLabelRichSelectDeleteSelection: 'Druk op VERWIJDEREN om item te deselecteren',
    ariaLabelTooltip: 'Tooltip',
    ariaLabelContextMenu: 'Contextmenu',
    ariaLabelSubMenu: 'Submenu',
    ariaLabelAggregationFunction: 'Aggregatiefunctie',
    ariaLabelAdvancedFilterAutocomplete: 'Geavanceerde Filter Autocompleteren',
    ariaLabelAdvancedFilterBuilderAddField: 'Geavanceerde Filter Builder Veld Toevoegen',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Geavanceerde Filter Builder Kolom Selecteer Veld',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Geavanceerde Filter Builder Optie Selecteer Veld',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Geavanceerde Filter Builder Koppeloperator Selecteer Veld',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Kolom Lijst',
    ariaFilterPanelList: 'Filterlijst',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Filterveld toevoegen',
    ariaLabelFilterCardDelete: 'Filter verwijderen',
    ariaLabelFilterCardHasEdits: 'Heeft wijzigingen',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Waar',
    false: 'Onwaar',
    invalidDate: 'Ongeldige Datum',
    invalidNumber: 'Ongeldig Nummer',
    january: 'Januari',
    february: 'Februari',
    march: 'Maart',
    april: 'April',
    may: 'Mei',
    june: 'Juni',
    july: 'Juli',
    august: 'Augustus',
    september: 'September',
    october: 'Oktober',
    november: 'November',
    december: 'December',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/JJJJ',
    timeFormatSlashesMMDDYYYY: 'MM/DD/JJJJ',
    timeFormatSlashesDDMMYY: 'DD/MM/JJ',
    timeFormatSlashesMMDDYY: 'MM/DD/JJ',
    timeFormatDotsDDMYY: 'DD.M.JJ',
    timeFormatDotsMDDYY: 'M.DD.JJ',
    timeFormatDashesYYYYMMDD: 'JJJJ-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM JJJJ',
    timeFormatHHMMSS: 'UU:MM:SS',
    timeFormatHHMMSSAmPm: 'UU:MM:SS AM/PM',
};
