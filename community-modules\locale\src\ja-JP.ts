/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_JP = {
    // Set Filter
    selectAll: '(すべて選択)',
    selectAllSearchResults: '(検索結果をすべて選択)',
    addCurrentSelectionToFilter: '現在の選択をフィルターに追加',
    searchOoo: '検索...',
    blanks: '(空白)',
    noMatches: '一致するものがありません',

    // Number Filter & Text Filter
    filterOoo: 'フィルター...',
    equals: '等しい',
    notEqual: '等しくない',
    blank: '空白',
    notBlank: '空白ではない',
    empty: '選択してください',

    // Number Filter
    lessThan: '未満',
    greaterThan: 'より大きい',
    lessThanOrEqual: '以下',
    greaterThanOrEqual: '以上',
    inRange: '間',
    inRangeStart: 'から',
    inRangeEnd: 'まで',

    // Text Filter
    contains: '含む',
    notContains: '含まない',
    startsWith: 'で始まる',
    endsWith: 'で終わる',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: '前',
    after: '後',

    // Filter Conditions
    andCondition: 'AND',
    orCondition: 'OR',

    // Filter Buttons
    applyFilter: '適用',
    resetFilter: 'リセット',
    clearFilter: 'クリア',
    cancelFilter: 'キャンセル',

    // Filter Titles
    textFilter: 'テキストフィルター',
    numberFilter: '数値フィルター',
    dateFilter: '日付フィルター',
    setFilter: 'セットフィルター',

    // Group Column Filter
    groupFilterSelect: 'フィールドを選択:',

    // New Filter Tool Panel
    filterSummaryInactive: 'すべて',
    filterSummaryContains: '含む',
    filterSummaryNotContains: '含まない',
    filterSummaryTextEquals: '等しい',
    filterSummaryTextNotEqual: '等しくない',
    filterSummaryStartsWith: 'で始まる',
    filterSummaryEndsWith: 'で終わる',
    filterSummaryBlank: '空白',
    filterSummaryNotBlank: '空白ではない',
    filterSummaryEquals: '＝',
    filterSummaryNotEqual: '≠',
    filterSummaryGreaterThan: '＞',
    filterSummaryGreaterThanOrEqual: '≧',
    filterSummaryLessThan: '＜',
    filterSummaryLessThanOrEqual: '≦',
    filterSummaryInRange: 'の間',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'すべて',
    filterSummaryListSeparator: '、',
    filterSummaryListShort: '(${variable})',
    filterSummaryListLong: '(${variable}) と ${variable} 件以上',
    addFilterCard: 'フィルターを追加',
    agTextColumnFilterDisplayName: 'シンプルフィルター',
    agNumberColumnFilterDisplayName: 'シンプルフィルター',
    agDateColumnFilterDisplayName: 'シンプルフィルター',
    agSetColumnFilterDisplayName: 'セレクションフィルター',
    agMultiColumnFilterDisplayName: 'コンボフィルター',
    addFilterPlaceholder: '列を検索...',

    // Advanced Filter
    advancedFilterContains: '含む',
    advancedFilterNotContains: '含まない',
    advancedFilterTextEquals: '等しい',
    advancedFilterTextNotEqual: '等しくない',
    advancedFilterStartsWith: 'で始まる',
    advancedFilterEndsWith: 'で終わる',
    advancedFilterBlank: '空白',
    advancedFilterNotBlank: '空白でない',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: '真',
    advancedFilterFalse: '偽',
    advancedFilterAnd: 'かつ',
    advancedFilterOr: 'または',
    advancedFilterApply: '適用',
    advancedFilterBuilder: 'ビルダー',
    advancedFilterValidationMissingColumn: '列が欠落しています',
    advancedFilterValidationMissingOption: 'オプションが欠落しています',
    advancedFilterValidationMissingValue: '値が欠落しています',
    advancedFilterValidationInvalidColumn: '列が見つかりません',
    advancedFilterValidationInvalidOption: 'オプションが見つかりません',
    advancedFilterValidationMissingQuote: '値の終端の引用符が欠落しています',
    advancedFilterValidationNotANumber: '値が数値ではありません',
    advancedFilterValidationInvalidDate: '値が有効な日付ではありません',
    advancedFilterValidationMissingCondition: '条件が欠落しています',
    advancedFilterValidationJoinOperatorMismatch: '条件内の結合演算子は同じである必要があります',
    advancedFilterValidationInvalidJoinOperator: '結合演算子が見つかりません',
    advancedFilterValidationMissingEndBracket: '終端の括弧が欠落しています',
    advancedFilterValidationExtraEndBracket: '終端の括弧が多すぎます',
    advancedFilterValidationMessage: '式にエラーがあります。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '式にエラーがあります。式の最後に${variable}。',
    advancedFilterBuilderTitle: '高度なフィルター',
    advancedFilterBuilderApply: '適用',
    advancedFilterBuilderCancel: 'キャンセル',
    advancedFilterBuilderAddButtonTooltip: 'フィルターまたはグループを追加',
    advancedFilterBuilderRemoveButtonTooltip: '削除',
    advancedFilterBuilderMoveUpButtonTooltip: '上へ移動',
    advancedFilterBuilderMoveDownButtonTooltip: '下へ移動',
    advancedFilterBuilderAddJoin: 'グループを追加',
    advancedFilterBuilderAddCondition: 'フィルターを追加',
    advancedFilterBuilderSelectColumn: '列を選択',
    advancedFilterBuilderSelectOption: 'オプションを選択',
    advancedFilterBuilderEnterValue: '値を入力...',
    advancedFilterBuilderValidationAlreadyApplied: '現在のフィルターは既に適用されています。',
    advancedFilterBuilderValidationIncomplete: 'すべての条件が完了していません。',
    advancedFilterBuilderValidationSelectColumn: '列を選択する必要があります。',
    advancedFilterBuilderValidationSelectOption: 'オプションを選択する必要があります。',
    advancedFilterBuilderValidationEnterValue: '値を入力する必要があります。',

    // Editor Validation Errors
    minDateValidation: '日付は${variable}より後でなければなりません',
    maxDateValidation: '日付は${variable}より前でなければなりません',
    maxLengthValidation: '${variable}文字以内でなければなりません。',
    minValueValidation: '${variable}以上でなければなりません',
    maxValueValidation: '${variable}以下でなければなりません',
    invalidSelectionValidation: '無効な選択です。',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: '列',
    filters: 'フィルター',

    // columns tool panel
    pivotMode: 'ピボットモード',
    groups: '行グループ',
    rowGroupColumnsEmptyMessage: 'ここにドラッグして行グループを設定します',
    values: '値',
    valueColumnsEmptyMessage: 'ここにドラッグして集計します',
    pivots: '列ラベル',
    pivotColumnsEmptyMessage: 'ここにドラッグして列ラベルを設定します',

    // Header of the Default Group Column
    group: 'グループ',

    // Row Drag
    rowDragRow: '行',
    rowDragRows: '行',

    // Other
    loadingOoo: '読み込み中...',
    loadingError: 'エラー',
    noRowsToShow: '表示する行がありません',
    enabled: '有効',

    // Menu
    pinColumn: '列の固定',
    pinLeft: '左に固定',
    pinRight: '右に固定',
    noPin: '固定なし',
    valueAggregation: '値の集計',
    noAggregation: 'なし',
    autosizeThisColumn: 'この列の自動サイズ調整',
    autosizeAllColumns: 'すべての列の自動サイズ調整',
    groupBy: 'グループ化',
    ungroupBy: 'グループ化解除',
    ungroupAll: 'すべてのグループ化を解除',
    addToValues: '${variable}を値に追加',
    removeFromValues: '${variable}を値から削除',
    addToLabels: '${variable}をラベルに追加',
    removeFromLabels: '${variable}をラベルから削除',
    resetColumns: '列をリセット',
    expandAll: 'すべての行グループを展開',
    collapseAll: 'すべての行グループを閉じる',
    copy: 'コピー',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'ヘッダー付きでコピー',
    copyWithGroupHeaders: 'グループヘッダー付きでコピー',
    cut: 'カット',
    paste: 'ペースト',
    ctrlV: 'Ctrl+V',
    export: 'エクスポート',
    csvExport: 'CSVエクスポート',
    excelExport: 'Excelエクスポート',
    columnFilter: '列フィルター',
    columnChooser: '列の選択',
    chooseColumns: '列を選択',
    sortAscending: '昇順で並べ替え',
    sortDescending: '降順で並べ替え',
    sortUnSort: 'ソート解除',

    // Enterprise Menu Aggregation and Status Bar
    sum: '合計',
    first: '最初',
    last: '最後',
    min: '最小',
    max: '最大',
    none: 'なし',
    count: 'カウント',
    avg: '平均',
    filteredRows: 'フィルタ済み',
    selectedRows: '選択済み',
    totalRows: '総行数',
    totalAndFilteredRows: '行',
    more: 'もっと',
    to: 'から',
    of: 'の',
    page: 'ページ',
    pageLastRowUnknown: '?',
    nextPage: '次のページ',
    lastPage: '最後のページ',
    firstPage: '最初のページ',
    previousPage: '前のページ',
    pageSizeSelectorLabel: 'ページサイズ：',
    footerTotal: '合計',
    statusBarLastRowUnknown: '？',
    scrollColumnIntoView: '${variable} を表示範囲にスクロール',

    // Pivoting
    pivotColumnGroupTotals: '合計',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'ピボットチャートとピボットモード',
    pivotChart: 'ピボットチャート',
    chartRange: 'チャート範囲',
    columnChart: 'カラム',
    groupedColumn: 'グループ化',
    stackedColumn: '積み重ね',
    normalizedColumn: '100% 積み重ね',
    barChart: 'バー',
    groupedBar: 'グループ化',
    stackedBar: '積み重ね',
    normalizedBar: '100% 積み重ね',
    pieChart: 'パイ',
    pie: 'パイ',
    donut: 'ドーナツ',
    lineChart: 'ライン',
    stackedLine: '積み重ね',
    normalizedLine: '100% 積み重ね',
    xyChart: 'X Y (散布図)',
    scatter: '散布図',
    bubble: 'バブル',
    areaChart: 'エリア',
    area: 'エリア',
    stackedArea: '積み重ね',
    normalizedArea: '100% 積み重ね',
    histogramChart: 'ヒストグラム',
    polarChart: '極座標',
    radarLine: 'レーダーライン',
    radarArea: 'レーダーエリア',
    nightingale: 'ナイチンゲール',
    radialColumn: '放射状カラム',
    radialBar: '放射状バー',
    statisticalChart: '統計',
    boxPlot: 'ボックスプロット',
    rangeBar: 'レンジバー',
    rangeArea: 'レンジエリア',
    hierarchicalChart: '階層',
    treemap: 'ツリーマップ',
    sunburst: 'サンバースト',
    specializedChart: '特殊',
    waterfall: 'ウォーターフォール',
    heatmap: 'ヒートマップ',
    combinationChart: 'コンビネーション',
    columnLineCombo: 'カラムとライン',
    AreaColumnCombo: 'エリアとカラム',

    // Charts
    pivotChartTitle: 'ピボットチャート',
    rangeChartTitle: '範囲チャート',
    settings: 'チャート',
    data: '設定',
    format: 'カスタマイズ',
    categories: 'カテゴリー',
    defaultCategory: '(なし)',
    series: 'シリーズ',
    switchCategorySeries: 'カテゴリー/シリーズの切り替え',
    categoryValues: 'カテゴリー値',
    seriesLabels: 'シリーズラベル',
    aggregate: '集計',
    xyValues: 'X Y値',
    paired: 'ペアモード',
    axis: '軸',
    xAxis: '水平軸',
    yAxis: '垂直軸',
    polarAxis: '極軸',
    radiusAxis: '半径軸',
    navigator: 'ナビゲーター',
    zoom: 'ズーム',
    animation: 'アニメーション',
    crosshair: 'クロスヘア',
    color: '色',
    thickness: '厚さ',
    preferredLength: '推奨長さ',
    xType: 'Xタイプ',
    axisType: '軸タイプ',
    automatic: '自動',
    category: 'カテゴリー',
    number: '数値',
    time: '時間',
    timeFormat: '時間形式',
    autoRotate: '自動回転',
    labelRotation: '回転',
    circle: '円',
    polygon: '多角形',
    square: '四角形',
    cross: 'クロス',
    diamond: 'ダイヤモンド',
    plus: 'プラス',
    triangle: '三角形',
    heart: 'ハート',
    orientation: '方向',
    fixed: '固定',
    parallel: '平行',
    perpendicular: '垂直',
    radiusAxisPosition: '位置',
    ticks: '目盛',
    gridLines: 'グリッド線',
    width: '幅',
    height: '高さ',
    length: '長さ',
    padding: 'パディング',
    spacing: '間隔',
    chartStyle: 'チャートスタイル',
    title: 'タイトル',
    chartTitles: 'タイトル',
    chartTitle: 'チャートタイトル',
    chartSubtitle: 'サブタイトル',
    horizontalAxisTitle: '水平軸タイトル',
    verticalAxisTitle: '垂直軸タイトル',
    polarAxisTitle: '極軸タイトル',
    titlePlaceholder: 'チャートタイトル',
    background: '背景',
    font: 'フォント',
    weight: '太さ',
    top: '上',
    right: '右',
    bottom: '下',
    left: '左',
    labels: 'ラベル',
    calloutLabels: 'コールアウトラベル',
    sectorLabels: 'セクターラベル',
    positionRatio: '位置比率',
    size: 'サイズ',
    shape: '形状',
    minSize: '最小サイズ',
    maxSize: '最大サイズ',
    legend: '凡例',
    position: '位置',
    markerSize: 'マーカーサイズ',
    markerStroke: 'マーカーストローク',
    markerPadding: 'マーカーパディング',
    itemSpacing: '項目間隔',
    itemPaddingX: '項目パディングX',
    itemPaddingY: '項目パディングY',
    layoutHorizontalSpacing: '水平間隔',
    layoutVerticalSpacing: '垂直間隔',
    strokeWidth: 'ストローク幅',
    offset: 'オフセット',
    offsets: 'オフセット',
    tooltips: 'ツールチップ',
    callout: 'コールアウト',
    markers: 'マーカー',
    shadow: '影',
    blur: 'ぼかし',
    xOffset: 'Xオフセット',
    yOffset: 'Yオフセット',
    lineWidth: '線の幅',
    lineDash: '線の破線',
    lineDashOffset: '破線オフセット',
    scrollingZoom: 'スクロール',
    scrollingStep: 'スクロールステップ',
    selectingZoom: '選択',
    durationMillis: '期間 (ms)',
    crosshairLabel: 'ラベル',
    crosshairSnap: 'ノードにスナップ',
    normal: '普通',
    bold: '太字',
    italic: 'イタリック',
    boldItalic: '太字イタリック',
    predefined: '定義済み',
    fillOpacity: '塗りの不透明度',
    strokeColor: '線の色',
    strokeOpacity: '線の不透明度',
    miniChart: 'ミニチャート',
    histogramBinCount: 'ビンの数',
    connectorLine: 'コネクター線',
    seriesItems: 'シリーズアイテム',
    seriesItemType: 'アイテムタイプ',
    seriesItemPositive: 'ポジティブ',
    seriesItemNegative: 'ネガティブ',
    seriesItemLabels: 'アイテムラベル',
    columnGroup: 'カラム',
    barGroup: 'バー',
    pieGroup: 'パイ',
    lineGroup: 'ライン',
    scatterGroup: 'X Y (散布図)',
    areaGroup: 'エリア',
    polarGroup: '極',
    statisticalGroup: '統計',
    hierarchicalGroup: '階層',
    specializedGroup: '専門',
    combinationGroup: 'コンビネーション',
    groupedColumnTooltip: 'グループ化',
    stackedColumnTooltip: '積み上げ',
    normalizedColumnTooltip: '100%積み上げ',
    groupedBarTooltip: 'グループ化',
    stackedBarTooltip: '積み上げ',
    normalizedBarTooltip: '100%積み上げ',
    pieTooltip: 'パイ',
    donutTooltip: 'ドーナツ',
    lineTooltip: 'ライン',
    stackedLineTooltip: '積み上げ',
    normalizedLineTooltip: '100%積み上げ',
    groupedAreaTooltip: 'エリア',
    stackedAreaTooltip: '積み上げ',
    normalizedAreaTooltip: '100%積み上げ',
    scatterTooltip: '散布図',
    bubbleTooltip: 'バブル',
    histogramTooltip: 'ヒストグラム',
    radialColumnTooltip: 'ラジアルカラム',
    radialBarTooltip: 'ラジアルバー',
    radarLineTooltip: 'レーダーライン',
    radarAreaTooltip: 'レーダーエリア',
    nightingaleTooltip: 'ナイチンゲール',
    rangeBarTooltip: '範囲バー',
    rangeAreaTooltip: '範囲エリア',
    boxPlotTooltip: 'ボックスプロット',
    treemapTooltip: 'ツリーマップ',
    sunburstTooltip: 'サンバースト',
    waterfallTooltip: 'ウォーターフォール',
    heatmapTooltip: 'ヒートマップ',
    columnLineComboTooltip: 'カラム＆ライン',
    areaColumnComboTooltip: 'エリア＆カラム',
    customComboTooltip: 'カスタムコンビネーション',
    innerRadius: '内側半径',
    startAngle: '開始角度',
    endAngle: '終了角度',
    reverseDirection: '逆方向',
    groupPadding: 'グループパディング',
    seriesPadding: 'シリーズパディング',
    tile: 'タイル',
    whisker: 'ウィスカー',
    cap: 'キャップ',
    capLengthRatio: '長さ比率',
    labelPlacement: '配置',
    inside: '内側',
    outside: '外側',
    noDataToChart: 'チャートにするデータがありません。',
    pivotChartRequiresPivotMode: 'ピボットチャートにはピボットモードが必要です。',
    chartSettingsToolbarTooltip: 'メニュー',
    chartLinkToolbarTooltip: 'グリッドにリンク',
    chartUnlinkToolbarTooltip: 'グリッドからリンク解除',
    chartDownloadToolbarTooltip: 'チャートをダウンロード',
    chartMenuToolbarTooltip: 'メニュー',
    chartEdit: 'チャートを編集',
    chartAdvancedSettings: '詳細設定',
    chartLink: 'グリッドにリンク',
    chartUnlink: 'グリッドからリンク解除',
    chartDownload: 'チャートをダウンロード',
    histogramFrequency: '頻度',
    seriesChartType: 'シリーズチャートタイプ',
    seriesType: 'シリーズタイプ',
    secondaryAxis: '第二軸',
    seriesAdd: 'シリーズを追加',
    categoryAdd: 'カテゴリを追加',
    bar: 'バー',
    column: 'カラム',
    histogram: 'ヒストグラム',
    advancedSettings: '詳細設定',
    direction: '方向',
    horizontal: '水平',
    vertical: '垂直',
    seriesGroupType: 'グループタイプ',
    groupedSeriesGroupType: 'グループ化',
    stackedSeriesGroupType: '積み上げ',
    normalizedSeriesGroupType: '100%積み上げ',
    legendEnabled: '有効',
    invalidColor: '色の値が無効です',
    groupedColumnFull: 'グループ化カラム',
    stackedColumnFull: '積み上げカラム',
    normalizedColumnFull: '100%積み上げカラム',
    groupedBarFull: 'グループ化バー',
    stackedBarFull: '積み上げバー',
    normalizedBarFull: '100%積み上げバー',
    stackedAreaFull: '積み上げエリア',
    normalizedAreaFull: '100%積み上げエリア',
    customCombo: 'カスタムコンビネーション',
    funnel: 'ファネル',
    coneFunnel: 'コーンファネル',
    pyramid: 'ピラミッド',
    funnelGroup: 'ファネル',
    funnelTooltip: 'ファネル',
    coneFunnelTooltip: 'コーンファネル',
    pyramidTooltip: 'ピラミッド',
    dropOff: 'ドロップオフ',
    stageLabels: 'ステージラベル',
    reverse: '反転',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}。 レベル ${variable}。 編集するにはENTERを押してください。',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}。 レベル ${variable}。 ${variable} 編集するにはENTERを押してください。',
    ariaAdvancedFilterBuilderList: '高度なフィルタービルダリスト',
    ariaAdvancedFilterBuilderFilterItem: 'フィルター条件',
    ariaAdvancedFilterBuilderGroupItem: 'フィルターグループ',
    ariaAdvancedFilterBuilderColumn: '列',
    ariaAdvancedFilterBuilderOption: 'オプション',
    ariaAdvancedFilterBuilderValueP: '値',
    ariaAdvancedFilterBuilderJoinOperator: '結合演算子',
    ariaAdvancedFilterInput: '高度なフィルター入力',
    ariaChecked: 'チェック済み',
    ariaColumn: '列',
    ariaColumnGroup: '列グループ',
    ariaColumnFiltered: 'フィルターが適用された列',
    ariaColumnSelectAll: 'すべての列の表示を切り替え',
    ariaDateFilterInput: '日付フィルター入力',
    ariaDefaultListName: 'リスト',
    ariaFilterColumnsInput: 'フィルター列入力',
    ariaFilterFromValue: '値からフィルター',
    ariaFilterInput: 'フィルター入力',
    ariaFilterList: 'フィルターリスト',
    ariaFilterToValue: '値までフィルター',
    ariaFilterValue: 'フィルター値',
    ariaFilterMenuOpen: 'フィルターメニューを開く',
    ariaFilteringOperator: 'フィルター演算子',
    ariaHidden: '非表示',
    ariaIndeterminate: '不確定',
    ariaInputEditor: '入力エディター',
    ariaMenuColumn: '列メニューを開くにはALT+↓を押してください',
    ariaFilterColumn: 'フィルターを開くにはCTRL+ENTERを押してください',
    ariaRowDeselect: 'この行の選択を解除するにはSPACEを押してください',
    ariaHeaderSelection: 'ヘッダー選択付きの列',
    ariaSelectAllCells: 'すべてのセルを選択するにはスペースキーを押してください',
    ariaRowSelectAll: 'すべての行の選択を切り替えるにはSPACEを押してください',
    ariaRowToggleSelection: '行の選択を切り替えるにはSPACEを押してください',
    ariaRowSelect: 'この行を選択するにはSPACEを押してください',
    ariaRowSelectionDisabled: 'この行の選択は無効です',
    ariaSearch: '検索',
    ariaSortableColumn: '並べ替えるにはENTERを押してください',
    ariaToggleVisibility: '表示を切り替えるにはSPACEを押してください',
    ariaToggleCellValue: 'セルの値を切り替えるにはSPACEを押してください',
    ariaUnchecked: '未チェック',
    ariaVisible: '表示',
    ariaSearchFilterValues: 'フィルター値を検索',
    ariaPageSizeSelectorLabel: 'ページサイズ',
    ariaChartMenuClose: 'チャート編集メニューを閉じる',
    ariaChartSelected: '選択済み',
    ariaSkeletonCellLoadingFailed: '行の読み込みに失敗しました',
    ariaSkeletonCellLoading: '行データを読み込み中',
    ariaDeferSkeletonCellLoading: 'セルを読み込み中',

    // ARIA for Batch Edit
    ariaPendingChange: '保留中の変更',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: '行グループ',
    ariaValuesDropZonePanelLabel: '値',
    ariaPivotDropZonePanelLabel: '列ラベル',
    ariaDropZoneColumnComponentDescription: '削除するには DELETE を押してください',
    ariaDropZoneColumnValueItemDescription: '集計タイプを変更するには ENTER を押してください',
    ariaDropZoneColumnGroupItemDescription: 'ソートするには ENTER を押してください',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' の ',
    ariaDropZoneColumnComponentSortAscending: '昇順',
    ariaDropZoneColumnComponentSortDescending: '降順',
    ariaLabelDialog: 'ダイアログ',
    ariaLabelColumnMenu: '列メニュー',
    ariaLabelColumnFilter: '列フィルター',
    ariaLabelSelectField: 'フィールドを選択',

    // Cell Editor
    ariaLabelCellEditor: 'セルエディタ',
    ariaValidationErrorPrefix: 'セルエディタの検証',
    ariaLabelLoadingContextMenu: 'コンテキストメニューを読み込んでいます',

    // aria labels for rich select
    ariaLabelRichSelectField: 'リッチセレクトフィールド',
    ariaLabelRichSelectToggleSelection: '選択を切り替えるにはスペースを押してください',
    ariaLabelRichSelectDeselectAllItems: 'すべてのアイテムの選択を解除するにはDELETEを押してください',
    ariaLabelRichSelectDeleteSelection: 'アイテムの選択を解除するにはDELETEを押してください',
    ariaLabelTooltip: 'ツールチップ',
    ariaLabelContextMenu: 'コンテキストメニュー',
    ariaLabelSubMenu: 'サブメニュー',
    ariaLabelAggregationFunction: '集計関数',
    ariaLabelAdvancedFilterAutocomplete: '高度なフィルターオートコンプリート',
    ariaLabelAdvancedFilterBuilderAddField: '高度なフィルタービルダーにフィールドを追加',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高度なフィルタービルダーの列選択フィールド',
    ariaLabelAdvancedFilterBuilderOptionSelectField: '高度なフィルタービルダーのオプション選択フィールド',
    ariaLabelAdvancedFilterBuilderJoinSelectField: '高度なフィルタービルダーの結合演算子選択フィールド',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: '列リスト',
    ariaFilterPanelList: 'フィルターリスト',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'フィルターフィールドを追加',
    ariaLabelFilterCardDelete: 'フィルターを削除',
    ariaLabelFilterCardHasEdits: '編集されています',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '、',
    decimalSeparator: '。',

    // Data types
    true: '真',
    false: '偽',
    invalidDate: '無効な日付',
    invalidNumber: '無効な数値',
    january: '1月',
    february: '2月',
    march: '3月',
    april: '4月',
    may: '5月',
    june: '6月',
    july: '7月',
    august: '8月',
    september: '9月',
    october: '10月',
    november: '11月',
    december: '12月',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS 午前/午後',
};
