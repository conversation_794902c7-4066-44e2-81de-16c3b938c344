@use 'sass:string';
@use 'sass:map';
@use 'sass:meta';

@use '../../../icon-font-codes' as *;
@use './generated/agGridAlpine';
@use './generated/agGridBalham';
@use './generated/agGridMaterial';
@use './generated/agGridQuartz';
@use './generated/agGridQuartzSubset';
@use './generated/agGridClassic';

$-fonts-emitted: ();

@mixin font-face($name) {
    @if not map.get($-fonts-emitted, $name) {
        $-fonts-emitted: map.set($-fonts-emitted, $name, true);

        $data: map.get(meta.module-variables($name), 'data');
        @if not $data {
            @error "No $data for #{$name} font";
        }
        @font-face {
            font-family: string.quote($name);
            src: url(#{$data});
            font-weight: normal;
            font-style: normal;
        }
    }
}

@mixin icon() {
    font-family: var(--ag-icon-font-family);
    font-weight: var(--ag-icon-font-weight);
    color: var(--ag-icon-font-color);
    font-size: var(--ag-icon-size);
    line-height: var(--ag-icon-size);
    font-style: normal;
    font-variant: normal;
    text-transform: none;

    // subpixel rendering can really mess up some icon fonts so disable it
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

// Get the content for a named icon
@function icon-content($name) {
    $glyph: map.get($icon-font-codes, $name);
    @if not $glyph {
        @error "No such icon: #{$name}";
    }
    @return var(--ag-icon-font-code-#{$name}, #{$glyph});
}
