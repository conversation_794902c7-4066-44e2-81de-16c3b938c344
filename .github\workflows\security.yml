name: Security

on:
    workflow_dispatch:
    schedule:
        - cron: '0 0 * * *' # Run daily at midnight UTC

permissions:
  contents: read

jobs:
    code-dependency-scan:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              id: checkout
              uses: actions/checkout@v4
              with:
                  fetch-depth: 1
            - name: Snyk scan workspaces
              id: snyk-scan-workspaces
              uses: ./external/ag-shared/github/actions/snyk-scan-workspaces
              with:
                  snyk_token: ${{ secrets.SNYK_TOKEN }}

    sast-code-scan:
        runs-on: ubuntu-latest
        steps:
            - name: Checkout
              id: checkout
              uses: actions/checkout@v4
              with:
                  fetch-depth: 1
            - name: Snyk code scan
              id: snyk-code-scan
              uses: ./external/ag-shared/github/actions/snyk-code-scan
              with:
                  snyk_token: ${{ secrets.SNYK_TOKEN }}
