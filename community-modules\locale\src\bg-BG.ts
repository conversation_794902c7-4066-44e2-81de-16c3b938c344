/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_BG = {
    // Set Filter
    selectAll: '(Избери Всички)',
    selectAllSearchResults: '(Избери Всички Резултати от Търсенето)',
    addCurrentSelectionToFilter: 'Добави текущия избор към филтъра',
    searchOoo: 'Търсене...',
    blanks: '(Празни)',
    noMatches: 'Няма съвпадения',

    // Number Filter & Text Filter
    filterOoo: 'Филтър...',
    equals: 'Равно на',
    notEqual: 'Не е равно на',
    blank: 'Празно',
    notBlank: 'Не е празно',
    empty: 'Изберете едно',

    // Number Filter
    lessThan: 'По-малко от',
    greaterThan: 'Повече от',
    lessThanOrEqual: 'По-малко или равно на',
    greaterThanOrEqual: 'Повече или равно на',
    inRange: 'Между',
    inRangeStart: 'От',
    inRangeEnd: 'До',

    // Text Filter
    contains: 'Съдържа',
    notContains: 'Не съдържа',
    startsWith: 'Започва с',
    endsWith: 'Завършва с',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Преди',
    after: 'След',

    // Filter Conditions
    andCondition: 'И',
    orCondition: 'ИЛИ',

    // Filter Buttons
    applyFilter: 'Приложи',
    resetFilter: 'Нулиране',
    clearFilter: 'Изчисти',
    cancelFilter: 'Отказ',

    // Filter Titles
    textFilter: 'Филтър за текст',
    numberFilter: 'Филтър за число',
    dateFilter: 'Филтър за дата',
    setFilter: 'Наборен филтър',

    // Group Column Filter
    groupFilterSelect: 'Изберете поле:',

    // New Filter Tool Panel
    filterSummaryInactive: 'е (Всички)',
    filterSummaryContains: 'съдържа',
    filterSummaryNotContains: 'не съдържа',
    filterSummaryTextEquals: 'равно на',
    filterSummaryTextNotEqual: 'не е равно на',
    filterSummaryStartsWith: 'започва с',
    filterSummaryEndsWith: 'завършва с',
    filterSummaryBlank: 'е празно',
    filterSummaryNotBlank: 'не е празно',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'между',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'е (Всички)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'е (${variable})',
    filterSummaryListLong: 'е (${variable}) и още ${variable}',
    addFilterCard: 'Добави филтър',
    agTextColumnFilterDisplayName: 'Прост филтър',
    agNumberColumnFilterDisplayName: 'Прост филтър',
    agDateColumnFilterDisplayName: 'Прост филтър',
    agSetColumnFilterDisplayName: 'Филтър за избор',
    agMultiColumnFilterDisplayName: 'Комбиниран филтър',
    addFilterPlaceholder: 'Търсене по колони...',

    // Advanced Filter
    advancedFilterContains: 'съдържа',
    advancedFilterNotContains: 'не съдържа',
    advancedFilterTextEquals: 'равен е на',
    advancedFilterTextNotEqual: 'не е равен на',
    advancedFilterStartsWith: 'започва с',
    advancedFilterEndsWith: 'завършва с',
    advancedFilterBlank: 'е празно',
    advancedFilterNotBlank: 'не е празно',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'е вярно',
    advancedFilterFalse: 'е невярно',
    advancedFilterAnd: 'И',
    advancedFilterOr: 'ИЛИ',
    advancedFilterApply: 'Приложи',
    advancedFilterBuilder: 'Създател',
    advancedFilterValidationMissingColumn: 'Колоната липсва',
    advancedFilterValidationMissingOption: 'Опцията липсва',
    advancedFilterValidationMissingValue: 'Стойността липсва',
    advancedFilterValidationInvalidColumn: 'Колоната не е намерена',
    advancedFilterValidationInvalidOption: 'Опцията не е намерена',
    advancedFilterValidationMissingQuote: 'Липсва крайна кавичка на стойността',
    advancedFilterValidationNotANumber: 'Стойността не е число',
    advancedFilterValidationInvalidDate: 'Стойността не е валидна дата',
    advancedFilterValidationMissingCondition: 'Липсва условие',
    advancedFilterValidationJoinOperatorMismatch: 'Операционните съединители в едно условие трябва да са еднакви',
    advancedFilterValidationInvalidJoinOperator: 'Операционният съединител не е намерен',
    advancedFilterValidationMissingEndBracket: 'Липсва крайна скоба',
    advancedFilterValidationExtraEndBracket: 'Прекалено много крайни скоби',
    advancedFilterValidationMessage: 'Има грешка в израза. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Има грешка в израза. ${variable} в края на израза.',
    advancedFilterBuilderTitle: 'Разширен филтър',
    advancedFilterBuilderApply: 'Приложи',
    advancedFilterBuilderCancel: 'Отмени',
    advancedFilterBuilderAddButtonTooltip: 'Добави филтър или група',
    advancedFilterBuilderRemoveButtonTooltip: 'Премахни',
    advancedFilterBuilderMoveUpButtonTooltip: 'Премести нагоре',
    advancedFilterBuilderMoveDownButtonTooltip: 'Премести надолу',
    advancedFilterBuilderAddJoin: 'Добави група',
    advancedFilterBuilderAddCondition: 'Добави филтър',
    advancedFilterBuilderSelectColumn: 'Избери колона',
    advancedFilterBuilderSelectOption: 'Избери опция',
    advancedFilterBuilderEnterValue: 'Въведи стойност...',
    advancedFilterBuilderValidationAlreadyApplied: 'Текущият филтър вече е приложен.',
    advancedFilterBuilderValidationIncomplete: 'Не всички условия са завършени.',
    advancedFilterBuilderValidationSelectColumn: 'Трябва да изберете колона.',
    advancedFilterBuilderValidationSelectOption: 'Трябва да изберете опция.',
    advancedFilterBuilderValidationEnterValue: 'Трябва да въведете стойност.',

    // Editor Validation Errors
    minDateValidation: 'Датата трябва да е след ${variable}',
    maxDateValidation: 'Датата трябва да е преди ${variable}',
    maxLengthValidation: 'Трябва да бъде ${variable} символа или по-малко.',
    minValueValidation: 'Трябва да бъде по-голямо или равно на ${variable}',
    maxValueValidation: 'Трябва да бъде по-малко или равно на ${variable}',
    invalidSelectionValidation: 'Невалиден избор.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Колони',
    filters: 'Филтри',

    // columns tool panel
    pivotMode: 'Режим на обобщение',
    groups: 'Групи по редове',
    rowGroupColumnsEmptyMessage: 'Плъзнете тук, за да зададете групи по редове',
    values: 'Стойности',
    valueColumnsEmptyMessage: 'Плъзнете тук, за да агрегиране',
    pivots: 'Етикети на колони',
    pivotColumnsEmptyMessage: 'Плъзнете тук, за да зададете етикети на колони',

    // Header of the Default Group Column
    group: 'Група',

    // Row Drag
    rowDragRow: 'ред',
    rowDragRows: 'редове',

    // Other
    loadingOoo: 'Зареждане...',
    loadingError: 'ГРЕШКА',
    noRowsToShow: 'Няма редове за показване',
    enabled: 'Активиран',

    // Menu
    pinColumn: 'Закрепване на колоната',
    pinLeft: 'Закрепване наляво',
    pinRight: 'Закрепване надясно',
    noPin: 'Без закрепване',
    valueAggregation: 'Агрегиране на стойности',
    noAggregation: 'Няма',
    autosizeThisColumn: 'Автоматично оразмеряване на тази колона',
    autosizeAllColumns: 'Автоматично оразмеряване на всички колони',
    groupBy: 'Групиране по',
    ungroupBy: 'Разгрупиране по',
    ungroupAll: 'Разгрупиране на всички',
    addToValues: 'Добавяне на ${variable} към стойности',
    removeFromValues: 'Премахване на ${variable} от стойности',
    addToLabels: 'Добавяне на ${variable} към етикети',
    removeFromLabels: 'Премахване на ${variable} от етикети',
    resetColumns: 'Нулиране на колони',
    expandAll: 'Разширяване на всички групи редове',
    collapseAll: 'Затваряне на всички групи редове',
    copy: 'Копиране',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Копиране с заглавия',
    copyWithGroupHeaders: 'Копиране с групови заглавия',
    cut: 'Изрязване',
    paste: 'Поставяне',
    ctrlV: 'Ctrl+V',
    export: 'Експортиране',
    csvExport: 'Експортиране в CSV',
    excelExport: 'Експортиране в Excel',
    columnFilter: 'Филтър на колона',
    columnChooser: 'Избор на колони',
    chooseColumns: 'Изберете колони',
    sortAscending: 'Сортиране във възходящ ред',
    sortDescending: 'Сортиране в низходящ ред',
    sortUnSort: 'Изчистване на сортирането',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Сума',
    first: 'Първи',
    last: 'Последен',
    min: 'Минимален',
    max: 'Максимален',
    none: 'Няма',
    count: 'Брой',
    avg: 'Средно',
    filteredRows: 'Филтрирани',
    selectedRows: 'Избрани',
    totalRows: 'Общо Редове',
    totalAndFilteredRows: 'Редове',
    more: 'Още',
    to: 'до',
    of: 'от',
    page: 'Страница',
    pageLastRowUnknown: '?',
    nextPage: 'Следваща Страница',
    lastPage: 'Последна Страница',
    firstPage: 'Първа Страница',
    previousPage: 'Предишна Страница',
    pageSizeSelectorLabel: 'Размер на Страницата:',
    footerTotal: 'Общо',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Превъртане на ${variable} във видимата част',

    // Pivoting
    pivotColumnGroupTotals: 'Общо',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Въртяща диаграма и въртящ режим',
    pivotChart: 'Въртяща диаграма',
    chartRange: 'Диапазон на диаграмата',
    columnChart: 'Колона',
    groupedColumn: 'Групирани',
    stackedColumn: 'Подредени',
    normalizedColumn: '100% Подредени',
    barChart: 'Лента',
    groupedBar: 'Групирани',
    stackedBar: 'Подредени',
    normalizedBar: '100% Подредени',
    pieChart: 'Кръгова диаграма',
    pie: 'Кръг',
    donut: 'Ринг',
    lineChart: 'Линейна',
    stackedLine: 'Натрупана',
    normalizedLine: '100% Натрупана',
    xyChart: 'X Y (Разпръснато)',
    scatter: 'Разпръснато',
    bubble: 'Мехурчеста',
    areaChart: 'Област',
    area: 'Област',
    stackedArea: 'Подредена',
    normalizedArea: '100% Подредена',
    histogramChart: 'Хистограма',
    polarChart: 'Полярна',
    radarLine: 'Радарна линия',
    radarArea: 'Радарна област',
    nightingale: 'Найтингейл',
    radialColumn: 'Радиална колона',
    radialBar: 'Радиална лента',
    statisticalChart: 'Статистическа',
    boxPlot: 'Кутиева диаграма',
    rangeBar: 'Диапазонна лента',
    rangeArea: 'Диапазонна област',
    hierarchicalChart: 'Йерархична',
    treemap: 'Дървовидна карта',
    sunburst: 'Слънчев изригване',
    specializedChart: 'Специализирана',
    waterfall: 'Каскадна',
    heatmap: 'Топлинна карта',
    combinationChart: 'Комбинирана',
    columnLineCombo: 'Колона и линия',
    AreaColumnCombo: 'Област и колона',

    // Charts
    pivotChartTitle: 'Сводна Диаграма',
    rangeChartTitle: 'Диапазон Диаграма',
    settings: 'Графика',
    data: 'Настройки',
    format: 'Персонализиране',
    categories: 'Категории',
    defaultCategory: '(Няма)',
    series: 'Серии',
    switchCategorySeries: 'Смяна на Категория/Серия',
    categoryValues: 'Стойности на Категория',
    seriesLabels: 'Етикети на Серии',
    aggregate: 'Агрегация',
    xyValues: 'X Y Стойности',
    paired: 'Свързан Режим',
    axis: 'Ос',
    xAxis: 'Хоризонтална Ос',
    yAxis: 'Вертикална Ос',
    polarAxis: 'Полярна Ос',
    radiusAxis: 'Радиус Ос',
    navigator: 'Навигатор',
    zoom: 'Увеличаване',
    animation: 'Анимация',
    crosshair: 'Прицел',
    color: 'Цвят',
    thickness: 'Дебелина',
    preferredLength: 'Предпочитана Дължина',
    xType: 'X Тип',
    axisType: 'Тип Ос',
    automatic: 'Автоматично',
    category: 'Категория',
    number: 'Число',
    time: 'Време',
    timeFormat: 'Формат на Времето',
    autoRotate: 'Автоматично Завъртане',
    labelRotation: 'Завъртане на Етикети',
    circle: 'Кръг',
    polygon: 'Многоъгълник',
    square: 'Квадрат',
    cross: 'Кръст',
    diamond: 'Ромб',
    plus: 'Плюс',
    triangle: 'Триъгълник',
    heart: 'Сърце',
    orientation: 'Ориентация',
    fixed: 'Фиксиран',
    parallel: 'Паралелен',
    perpendicular: 'Перпендикулярен',
    radiusAxisPosition: 'Позиция',
    ticks: 'Мерки',
    gridLines: 'Водещи Линии',
    width: 'Ширина',
    height: 'Височина',
    length: 'Дължина',
    padding: 'Попълване',
    spacing: 'Разстояние',
    chartStyle: 'Стил на Диаграма',
    title: 'Заглавие',
    chartTitles: 'Заглавия на Диаграма',
    chartTitle: 'Заглавие на Диаграма',
    chartSubtitle: 'Подзаглавие',
    horizontalAxisTitle: 'Заглавие на Хоризонтална Ос',
    verticalAxisTitle: 'Заглавие на Вертикална Ос',
    polarAxisTitle: 'Заглавие на Полярна Ос',
    titlePlaceholder: 'Заглавие на Диаграма',
    background: 'Фон',
    font: 'Шрифт',
    weight: 'Тегло',
    top: 'Горе',
    right: 'Дясно',
    bottom: 'Долу',
    left: 'Ляво',
    labels: 'Етикети',
    calloutLabels: 'Обаждащи Етикети',
    sectorLabels: 'Етикети на Сектори',
    positionRatio: 'Съотношение на Позиция',
    size: 'Размер',
    shape: 'Форма',
    minSize: 'Минимален Размер',
    maxSize: 'Максимален Размер',
    legend: 'Легенда',
    position: 'Позиция',
    markerSize: 'Размер на Маркера',
    markerStroke: 'Контур на Маркера',
    markerPadding: 'Попълване на Маркера',
    itemSpacing: 'Разстояние Между Артикули',
    itemPaddingX: 'Хоризонтално Попълване на Артикул',
    itemPaddingY: 'Вертикално Попълване на Артикул',
    layoutHorizontalSpacing: 'Хоризонтално Разстояние',
    layoutVerticalSpacing: 'Вертикално Разстояние',
    strokeWidth: 'Дебелина на Линията',
    offset: 'Изместване',
    offsets: 'Измествания',
    tooltips: 'Подсказки',
    callout: 'Обаждане',
    markers: 'Маркери',
    shadow: 'Сянка',
    blur: 'Размазване',
    xOffset: 'Изместване по X',
    yOffset: 'Изместване по Y',
    lineWidth: 'Ширина на Линията',
    lineDash: 'Прекъснати Линии',
    lineDashOffset: 'Изместване на Прекъснати Линии',
    scrollingZoom: 'Превъртане',
    scrollingStep: 'Стъпка при Превъртане',
    selectingZoom: 'Избиране',
    durationMillis: 'Продължителност (ms)',
    crosshairLabel: 'Етикет',
    crosshairSnap: 'Привързване към Връзка',
    normal: 'Нормален',
    bold: 'Удебелен',
    italic: 'Наклонен',
    boldItalic: 'Удебелен и Наклонен',
    predefined: 'Предварително Определен',
    fillOpacity: 'Прозрачност на Запълване',
    strokeColor: 'Цвят на Линията',
    strokeOpacity: 'Прозрачност на Линията',
    miniChart: 'Мини Диаграма',
    histogramBinCount: 'Брой на Кофи',
    connectorLine: 'Свързваща Линия',
    seriesItems: 'Обекти в Серия',
    seriesItemType: 'Тип Обект',
    seriesItemPositive: 'Положително',
    seriesItemNegative: 'Отрицателно',
    seriesItemLabels: 'Етикети на Обекти',
    columnGroup: 'Колона',
    barGroup: 'Лента',
    pieGroup: 'Пай',
    lineGroup: 'Линия',
    scatterGroup: 'X Y (Точки)',
    areaGroup: 'Област',
    polarGroup: 'Полярна',
    statisticalGroup: 'Статистическа',
    hierarchicalGroup: 'Йерархична',
    specializedGroup: 'Специализирана',
    combinationGroup: 'Комбинирана',
    groupedColumnTooltip: 'Групирано',
    stackedColumnTooltip: 'Слоено',
    normalizedColumnTooltip: '100% Слоено',
    groupedBarTooltip: 'Групирано',
    stackedBarTooltip: 'Слоено',
    normalizedBarTooltip: '100% Слоено',
    pieTooltip: 'Пай',
    donutTooltip: 'Поничка',
    lineTooltip: 'Линия',
    stackedLineTooltip: 'Натрупано',
    normalizedLineTooltip: '100% Натрупано',
    groupedAreaTooltip: 'Област',
    stackedAreaTooltip: 'Слоена',
    normalizedAreaTooltip: '100% Слоена',
    scatterTooltip: 'Точки',
    bubbleTooltip: 'Мехурче',
    histogramTooltip: 'Хистограма',
    radialColumnTooltip: 'Радиационна Колона',
    radialBarTooltip: 'Радиационна Лента',
    radarLineTooltip: 'Радарна Линия',
    radarAreaTooltip: 'Радарна Област',
    nightingaleTooltip: 'Найтингейл',
    rangeBarTooltip: 'Диапазонна Лента',
    rangeAreaTooltip: 'Диапазонна Област',
    boxPlotTooltip: 'Кутиево Разпределение',
    treemapTooltip: 'Дървесна Карта',
    sunburstTooltip: 'Слънчево Излъчване',
    waterfallTooltip: 'Водоопад',
    heatmapTooltip: 'Топлинна Карта',
    columnLineComboTooltip: 'Колона и Линия',
    areaColumnComboTooltip: 'Област и Колона',
    customComboTooltip: 'Персонализирана Комбинация',
    innerRadius: 'Вътрешен Радиус',
    startAngle: 'Начален Ъгъл',
    endAngle: 'Краен Ъгъл',
    reverseDirection: 'Обратна Посока',
    groupPadding: 'Попълване на Група',
    seriesPadding: 'Попълване на Серия',
    tile: 'Плочка',
    whisker: 'Уиски',
    cap: 'Сапун',
    capLengthRatio: 'Съотношение на Дължина',
    labelPlacement: 'Поставяне на Етикета',
    inside: 'Вътре',
    outside: 'Навън',
    noDataToChart: 'Няма налични данни за изобразяване.',
    pivotChartRequiresPivotMode: 'Сводната Диаграма изисква Режим на Свод.',
    chartSettingsToolbarTooltip: 'Меню',
    chartLinkToolbarTooltip: 'Свързано с Решетката',
    chartUnlinkToolbarTooltip: 'Разделено от Решетката',
    chartDownloadToolbarTooltip: 'Изтегли Диаграма',
    chartMenuToolbarTooltip: 'Меню',
    chartEdit: 'Редактирай Диаграма',
    chartAdvancedSettings: 'Разширени Настройки',
    chartLink: 'Свържи с Решетката',
    chartUnlink: 'Раздели от Решетката',
    chartDownload: 'Изтегли Диаграма',
    histogramFrequency: 'Честота',
    seriesChartType: 'Тип на Диаграмата',
    seriesType: 'Тип на Серията',
    secondaryAxis: 'Вторична Ос',
    seriesAdd: 'Добави Серия',
    categoryAdd: 'Добави Категория',
    bar: 'Лента',
    column: 'Колона',
    histogram: 'Хистограма',
    advancedSettings: 'Разширени Настройки',
    direction: 'Посока',
    horizontal: 'Хоризонтално',
    vertical: 'Вертикално',
    seriesGroupType: 'Тип на Групата',
    groupedSeriesGroupType: 'Групирано',
    stackedSeriesGroupType: 'Слоено',
    normalizedSeriesGroupType: '100% Слоено',
    legendEnabled: 'Активирано',
    invalidColor: 'Невалидна Стойност на Цвят',
    groupedColumnFull: 'Групирана Колона',
    stackedColumnFull: 'Слоена Колона',
    normalizedColumnFull: '100% Слоена Колона',
    groupedBarFull: 'Групирана Лента',
    stackedBarFull: 'Слоена Лента',
    normalizedBarFull: '100% Слоена Лента',
    stackedAreaFull: 'Слоена Област',
    normalizedAreaFull: '100% Слоена Област',
    customCombo: 'Персонализирана Комбинация',
    funnel: 'Фуния',
    coneFunnel: 'Конусна фуния',
    pyramid: 'Пирамида',
    funnelGroup: 'Фуния',
    funnelTooltip: 'Фуния',
    coneFunnelTooltip: 'Конусна фуния',
    pyramidTooltip: 'Пирамида',
    dropOff: 'Спад',
    stageLabels: 'Етикети на етапи',
    reverse: 'Обратно',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Ниво ${variable}. Натиснете ENTER за редактиране',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Ниво ${variable}. ${variable} Натиснете ENTER за редактиране.',
    ariaAdvancedFilterBuilderList: 'Разширен списък за създаване на филтри',
    ariaAdvancedFilterBuilderFilterItem: 'Условие за филтриране',
    ariaAdvancedFilterBuilderGroupItem: 'Група за филтриране',
    ariaAdvancedFilterBuilderColumn: 'Колона',
    ariaAdvancedFilterBuilderOption: 'Опция',
    ariaAdvancedFilterBuilderValueP: 'Стойност',
    ariaAdvancedFilterBuilderJoinOperator: 'Оператор за свързване',
    ariaAdvancedFilterInput: 'Вход за разширен филтър',
    ariaChecked: 'маркирано',
    ariaColumn: 'Колона',
    ariaColumnGroup: 'Група колони',
    ariaColumnFiltered: 'Колоната е филтрирана',
    ariaColumnSelectAll: 'Превключване на видимостта на всички колони',
    ariaDateFilterInput: 'Вход за филтър по дата',
    ariaDefaultListName: 'Списък',
    ariaFilterColumnsInput: 'Вход за филтриране на колони',
    ariaFilterFromValue: 'Филтър от стойност',
    ariaFilterInput: 'Вход за филтър',
    ariaFilterList: 'Списък за филтриране',
    ariaFilterToValue: 'Филтър до стойност',
    ariaFilterValue: 'Стойност на филтъра',
    ariaFilterMenuOpen: 'Отворете менюто за филтър',
    ariaFilteringOperator: 'Оператор за филтриране',
    ariaHidden: 'скрито',
    ariaIndeterminate: 'неопределено',
    ariaInputEditor: 'Редактор за вход',
    ariaMenuColumn: 'Натиснете ALT + СТРЕЛКА НАДОЛУ, за да отворите менюто на колоната',
    ariaFilterColumn: 'Натиснете CTRL + ENTER, за да отворите филтъра',
    ariaRowDeselect: 'Натиснете SPACE, за да отмаркирате този ред',
    ariaHeaderSelection: 'Колона с избор на заглавие',
    ariaSelectAllCells: 'Натиснете интервал, за да изберете всички клетки',
    ariaRowSelectAll: 'Натиснете SPACE, за да превключите избора на всички редове',
    ariaRowToggleSelection: 'Натиснете SPACE, за да превключите избора на реда',
    ariaRowSelect: 'Натиснете SPACE, за да изберете този ред',
    ariaRowSelectionDisabled: 'Изборът на редове е деактивиран за този ред',
    ariaSearch: 'Търсене',
    ariaSortableColumn: 'Натиснете ENTER, за да сортирате',
    ariaToggleVisibility: 'Натиснете SPACE, за да превключите видимостта',
    ariaToggleCellValue: 'Натиснете SPACE, за да превключите стойността на клетката',
    ariaUnchecked: 'немаркирано',
    ariaVisible: 'видимо',
    ariaSearchFilterValues: 'Търсене на стойности за филтър',
    ariaPageSizeSelectorLabel: 'Размер на страницата',
    ariaChartMenuClose: 'Затворете менюто за редактиране на диаграмата',
    ariaChartSelected: 'Избрано',
    ariaSkeletonCellLoadingFailed: 'Редът не можа да се зареди',
    ariaSkeletonCellLoading: 'Данните на реда се зареждат',
    ariaDeferSkeletonCellLoading: 'Зарежда се клетка',

    // ARIA for Batch Edit
    ariaPendingChange: 'Чакаща промяна',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Редови групи',
    ariaValuesDropZonePanelLabel: 'Стойности',
    ariaPivotDropZonePanelLabel: 'Етикети на колони',
    ariaDropZoneColumnComponentDescription: 'Натиснете DELETE, за да премахнете',
    ariaDropZoneColumnValueItemDescription: 'Натиснете ENTER, за да промените типа на агрегация',
    ariaDropZoneColumnGroupItemDescription: 'Натиснете ENTER, за да сортирате',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' от ',
    ariaDropZoneColumnComponentSortAscending: 'възходящо',
    ariaDropZoneColumnComponentSortDescending: 'низходящо',
    ariaLabelDialog: 'Диалог',
    ariaLabelColumnMenu: 'Меню на колона',
    ariaLabelColumnFilter: 'Филтър на колона',
    ariaLabelSelectField: 'Избор на поле',

    // Cell Editor
    ariaLabelCellEditor: 'Редактор на клетки',
    ariaValidationErrorPrefix: 'Валидиране на редактор на клетки',
    ariaLabelLoadingContextMenu: 'Зареждане на контекстно меню',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Поле за богато избиране',
    ariaLabelRichSelectToggleSelection: 'Натиснете SPACE, за да превключите избора',
    ariaLabelRichSelectDeselectAllItems: 'Натиснете DELETE, за да отмените избора на всички елементи',
    ariaLabelRichSelectDeleteSelection: 'Натиснете DELETE, за да отмените избора на елемент',
    ariaLabelTooltip: 'Пояснение',
    ariaLabelContextMenu: 'Контекстно меню',
    ariaLabelSubMenu: 'Подменю',
    ariaLabelAggregationFunction: 'Функция за агрегиране',
    ariaLabelAdvancedFilterAutocomplete: 'Разширено филтриране с автоматично допълване',
    ariaLabelAdvancedFilterBuilderAddField: 'Разширен конструктор на филтри - Добавяне на поле',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Разширен конструктор на филтри - Избиране на поле за колона',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Разширен конструктор на филтри - Избиране на поле за опция',
    ariaLabelAdvancedFilterBuilderJoinSelectField:
        'Разширен конструктор на филтри - Избиране на оператор за иницииране',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Списък с колони',
    ariaFilterPanelList: 'Списък с филтри',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Добавяне на поле за филтър',
    ariaLabelFilterCardDelete: 'Изтриване на филтър',
    ariaLabelFilterCardHasEdits: 'Има редакции',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'Вярно',
    false: 'Невярно',
    invalidDate: 'Невалидна дата',
    invalidNumber: 'Невалиден номер',
    january: 'Януари',
    february: 'Февруари',
    march: 'Март',
    april: 'Април',
    may: 'Май',
    june: 'Юни',
    july: 'Юли',
    august: 'Август',
    september: 'Септември',
    october: 'Октомври',
    november: 'Ноември',
    december: 'Декември',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'ДД/ММ/ГГГГ',
    timeFormatSlashesMMDDYYYY: 'ММ/ДД/ГГГГ',
    timeFormatSlashesDDMMYY: 'ДД/ММ/ГГ',
    timeFormatSlashesMMDDYY: 'ММ/ДД/ГГ',
    timeFormatDotsDDMYY: 'ДД.М.ГГ',
    timeFormatDotsMDDYY: 'М.ДД.ГГ',
    timeFormatDashesYYYYMMDD: 'ГГГГ-ММ-ДД',
    timeFormatSpacesDDMMMMYYYY: 'ДД МMMM ГГГГ',
    timeFormatHHMMSS: 'ЧЧ:ММ:СС',
    timeFormatHHMMSSAmPm: 'ЧЧ:ММ:СС AM/PM',
};
