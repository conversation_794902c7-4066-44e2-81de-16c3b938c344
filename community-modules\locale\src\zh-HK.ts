/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_HK = {
    // Set Filter
    selectAll: '(全選)',
    selectAllSearchResults: '(選擇所有搜索結果)',
    addCurrentSelectionToFilter: '將當前選擇添加到過濾器',
    searchOoo: '搜索...',
    blanks: '(空白)',
    noMatches: '未找到匹配項',

    // Number Filter & Text Filter
    filterOoo: '篩選...',
    equals: '等於',
    notEqual: '不等於',
    blank: '空白',
    notBlank: '非空白',
    empty: '選擇一個',

    // Number Filter
    lessThan: '少於',
    greaterThan: '大於',
    lessThanOrEqual: '少於或等於',
    greaterThanOrEqual: '大於或等於',
    inRange: '介乎',
    inRangeStart: '由',
    inRangeEnd: '至',

    // Text Filter
    contains: '包含',
    notContains: '不包含',
    startsWith: '開始於',
    endsWith: '結束於',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: '之前',
    after: '之後',

    // Filter Conditions
    andCondition: '和',
    orCondition: '或',

    // Filter Buttons
    applyFilter: '應用',
    resetFilter: '重置',
    clearFilter: '清除',
    cancelFilter: '取消',

    // Filter Titles
    textFilter: '文本篩選',
    numberFilter: '數字篩選',
    dateFilter: '日期篩選',
    setFilter: '集合篩選',

    // Group Column Filter
    groupFilterSelect: '選擇字段：',

    // New Filter Tool Panel
    filterSummaryInactive: '是 (全部)',
    filterSummaryContains: '包含',
    filterSummaryNotContains: '不包含',
    filterSummaryTextEquals: '等於',
    filterSummaryTextNotEqual: '不等於',
    filterSummaryStartsWith: '開始於',
    filterSummaryEndsWith: '結束於',
    filterSummaryBlank: '是空白',
    filterSummaryNotBlank: '不是空白',
    filterSummaryEquals: '＝',
    filterSummaryNotEqual: '≠',
    filterSummaryGreaterThan: '＞',
    filterSummaryGreaterThanOrEqual: '≥',
    filterSummaryLessThan: '＜',
    filterSummaryLessThanOrEqual: '≤',
    filterSummaryInRange: '介於',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: '是 (全部)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: '是 (${variable})',
    filterSummaryListLong: '是 (${variable}) 和另外 ${variable}',
    addFilterCard: '新增篩選',
    agTextColumnFilterDisplayName: '簡單篩選',
    agNumberColumnFilterDisplayName: '簡單篩選',
    agDateColumnFilterDisplayName: '簡單篩選',
    agSetColumnFilterDisplayName: '選擇篩選',
    agMultiColumnFilterDisplayName: '組合篩選',
    addFilterPlaceholder: '搜尋欄位...',

    // Advanced Filter
    advancedFilterContains: '包含',
    advancedFilterNotContains: '不包含',
    advancedFilterTextEquals: '等於',
    advancedFilterTextNotEqual: '不等於',
    advancedFilterStartsWith: '開始於',
    advancedFilterEndsWith: '結束於',
    advancedFilterBlank: '為空',
    advancedFilterNotBlank: '不為空',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: '為真',
    advancedFilterFalse: '為假',
    advancedFilterAnd: '和',
    advancedFilterOr: '或',
    advancedFilterApply: '應用',
    advancedFilterBuilder: '建造者',
    advancedFilterValidationMissingColumn: '缺少列',
    advancedFilterValidationMissingOption: '缺少選項',
    advancedFilterValidationMissingValue: '缺少值',
    advancedFilterValidationInvalidColumn: '未找到列',
    advancedFilterValidationInvalidOption: '未找到選項',
    advancedFilterValidationMissingQuote: '值缺少結尾引號',
    advancedFilterValidationNotANumber: '值不是一個數字',
    advancedFilterValidationInvalidDate: '值不是有效日期',
    advancedFilterValidationMissingCondition: '缺少條件',
    advancedFilterValidationJoinOperatorMismatch: '條件內的連接運算符必須相同',
    advancedFilterValidationInvalidJoinOperator: '未找到連接運算符',
    advancedFilterValidationMissingEndBracket: '缺少結尾括號',
    advancedFilterValidationExtraEndBracket: '結尾括號過多',
    advancedFilterValidationMessage: '表達式有錯誤。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '表達式有錯誤。${variable} 在表達式的結尾。',
    advancedFilterBuilderTitle: '高級過濾器',
    advancedFilterBuilderApply: '應用',
    advancedFilterBuilderCancel: '取消',
    advancedFilterBuilderAddButtonTooltip: '添加過濾器或分組',
    advancedFilterBuilderRemoveButtonTooltip: '移除',
    advancedFilterBuilderMoveUpButtonTooltip: '向上移動',
    advancedFilterBuilderMoveDownButtonTooltip: '向下移動',
    advancedFilterBuilderAddJoin: '添加分組',
    advancedFilterBuilderAddCondition: '添加過濾器',
    advancedFilterBuilderSelectColumn: '選擇一列',
    advancedFilterBuilderSelectOption: '選擇一個選項',
    advancedFilterBuilderEnterValue: '輸入一個值...',
    advancedFilterBuilderValidationAlreadyApplied: '當前過濾器已應用。',
    advancedFilterBuilderValidationIncomplete: '條件未完全。',
    advancedFilterBuilderValidationSelectColumn: '必須選擇一列。',
    advancedFilterBuilderValidationSelectOption: '必須選擇一個選項。',
    advancedFilterBuilderValidationEnterValue: '必須輸入一個值。',

    // Editor Validation Errors
    minDateValidation: '日期必須在 ${variable} 之後',
    maxDateValidation: '日期必須在 ${variable} 之前',
    maxLengthValidation: '必須少於或等於 ${variable} 個字符。',
    minValueValidation: '必須大於或等於 ${variable}',
    maxValueValidation: '必須小於或等於 ${variable}',
    invalidSelectionValidation: '選擇無效。',
    tooltipValidationErrorSeparator: "。 '",

    // Side Bar
    columns: '欄',
    filters: '篩選',

    // columns tool panel
    pivotMode: '樞紐模式',
    groups: '行組',
    rowGroupColumnsEmptyMessage: '拖動到這裡設置行組',
    values: '值',
    valueColumnsEmptyMessage: '拖動到這裡進行匯總',
    pivots: '列標籤',
    pivotColumnsEmptyMessage: '拖動到這裡設置列標籤',

    // Header of the Default Group Column
    group: '組',

    // Row Drag
    rowDragRow: '行',
    rowDragRows: '行',

    // Other
    loadingOoo: '載入中...',
    loadingError: '錯誤',
    noRowsToShow: '沒有行顯示',
    enabled: '已啟用',

    // Menu
    pinColumn: '固定列',
    pinLeft: '固定到左邊',
    pinRight: '固定到右邊',
    noPin: '不固定',
    valueAggregation: '值聚合',
    noAggregation: '無',
    autosizeThisColumn: '自動調整此列的寬度',
    autosizeAllColumns: '自動調整所有列的寬度',
    groupBy: '分組',
    ungroupBy: '取消分組',
    ungroupAll: '取消所有分組',
    addToValues: '添加 ${variable} 到數值',
    removeFromValues: '從數值中移除 ${variable}',
    addToLabels: '添加 ${variable} 到標籤',
    removeFromLabels: '從標籤中移除 ${variable}',
    resetColumns: '重置列',
    expandAll: '展開所有行組',
    collapseAll: '關閉所有行組',
    copy: '複製',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: '連同標頭一起複製',
    copyWithGroupHeaders: '連同分組標頭一起複製',
    cut: '剪切',
    paste: '粘貼',
    ctrlV: 'Ctrl+V',
    export: '匯出',
    csvExport: '匯出為 CSV',
    excelExport: '匯出為 Excel',
    columnFilter: '列篩選器',
    columnChooser: '選擇列',
    chooseColumns: '選擇列',
    sortAscending: '升序排序',
    sortDescending: '降序排序',
    sortUnSort: '清除排序',

    // Enterprise Menu Aggregation and Status Bar
    sum: '總和',
    first: '第一',
    last: '最後',
    min: '最小',
    max: '最大',
    none: '無',
    count: '計數',
    avg: '平均',
    filteredRows: '已篩選',
    selectedRows: '已選擇',
    totalRows: '總行數',
    totalAndFilteredRows: '行數',
    more: '更多',
    to: '至',
    of: '的',
    page: '頁',
    pageLastRowUnknown: '?',
    nextPage: '下一頁',
    lastPage: '最後一頁',
    firstPage: '第一頁',
    previousPage: '上一頁',
    pageSizeSelectorLabel: '頁面大小:',
    footerTotal: '總計',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: '將 ${variable} 捲動至可見範圍',

    // Pivoting
    pivotColumnGroupTotals: '總計',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: '樞紐圖表及樞紐模式',
    pivotChart: '樞紐圖表',
    chartRange: '圖表範圍',
    columnChart: '柱狀圖',
    groupedColumn: '分組',
    stackedColumn: '堆疊',
    normalizedColumn: '100% 堆疊',
    barChart: '橫條圖',
    groupedBar: '分組',
    stackedBar: '堆疊',
    normalizedBar: '100% 堆疊',
    pieChart: '餅圖',
    pie: '餅圖',
    donut: '圓環圖',
    lineChart: '折線',
    stackedLine: '堆疊',
    normalizedLine: '100% 堆疊',
    xyChart: 'X Y (散點圖)',
    scatter: '散點圖',
    bubble: '氣泡圖',
    areaChart: '區域圖',
    area: '區域',
    stackedArea: '堆疊',
    normalizedArea: '100% 堆疊',
    histogramChart: '直方圖',
    polarChart: '極座標圖',
    radarLine: '雷達線圖',
    radarArea: '雷達區域圖',
    nightingale: '夜鶯圖',
    radialColumn: '徑向柱狀圖',
    radialBar: '徑向條形圖',
    statisticalChart: '統計圖',
    boxPlot: '盒鬚圖',
    rangeBar: '範圍條形圖',
    rangeArea: '範圍區域圖',
    hierarchicalChart: '階層圖',
    treemap: '樹狀圖',
    sunburst: '旭日圖',
    specializedChart: '專業圖表',
    waterfall: '瀑布圖',
    heatmap: '熱圖',
    combinationChart: '組合圖',
    columnLineCombo: '柱狀圖和折線圖',
    AreaColumnCombo: '區域圖和柱狀圖',

    // Charts
    pivotChartTitle: '樞紐圖',
    rangeChartTitle: '範圍圖',
    settings: '圖表',
    data: '設置',
    format: '自訂',
    categories: '類別',
    defaultCategory: '(無)',
    series: '系列',
    switchCategorySeries: '切換類別 / 系列',
    categoryValues: '類別值',
    seriesLabels: '系列標籤',
    aggregate: '匯總',
    xyValues: 'X Y 值',
    paired: '配對模式',
    axis: '軸',
    xAxis: '水平軸',
    yAxis: '垂直軸',
    polarAxis: '極座標軸',
    radiusAxis: '半徑軸',
    navigator: '導航器',
    zoom: '縮放',
    animation: '動畫',
    crosshair: '準心',
    color: '顏色',
    thickness: '厚度',
    preferredLength: '首選長度',
    xType: 'X 類型',
    axisType: '軸類型',
    automatic: '自動',
    category: '類別',
    number: '數字',
    time: '時間',
    timeFormat: '時間格式',
    autoRotate: '自動旋轉',
    labelRotation: '旋轉',
    circle: '圓形',
    polygon: '多邊形',
    square: '正方形',
    cross: '叉號',
    diamond: '菱形',
    plus: '加號',
    triangle: '三角形',
    heart: '心形',
    orientation: '方向',
    fixed: '固定',
    parallel: '平行',
    perpendicular: '垂直',
    radiusAxisPosition: '位置',
    ticks: '刻度',
    gridLines: '網格線',
    width: '寬度',
    height: '高度',
    length: '長度',
    padding: '內距',
    spacing: '間距',
    chartStyle: '圖表樣式',
    title: '標題',
    chartTitles: '標題',
    chartTitle: '圖表標題',
    chartSubtitle: '副標題',
    horizontalAxisTitle: '水平軸標題',
    verticalAxisTitle: '垂直軸標題',
    polarAxisTitle: '極座標軸標題',
    titlePlaceholder: '圖表標題',
    background: '背景',
    font: '字體',
    weight: '字重',
    top: '頂部',
    right: '右邊',
    bottom: '底部',
    left: '左邊',
    labels: '標籤',
    calloutLabels: '標註標籤',
    sectorLabels: '扇區標籤',
    positionRatio: '位置比例',
    size: '大小',
    shape: '形狀',
    minSize: '最小大小',
    maxSize: '最大大小',
    legend: '圖例',
    position: '位置',
    markerSize: '標記大小',
    markerStroke: '標記邊框',
    markerPadding: '標記內距',
    itemSpacing: '項目間距',
    itemPaddingX: '項目內距 X',
    itemPaddingY: '項目內距 Y',
    layoutHorizontalSpacing: '水平間距',
    layoutVerticalSpacing: '垂直間距',
    strokeWidth: '邊框寬度',
    offset: '偏移',
    offsets: '偏移量',
    tooltips: '工具提示',
    callout: '標註',
    markers: '標記',
    shadow: '陰影',
    blur: '模糊',
    xOffset: 'X 偏移',
    yOffset: 'Y 偏移',
    lineWidth: '線寬',
    lineDash: '虛線',
    lineDashOffset: '虛線偏移',
    scrollingZoom: '滾動',
    scrollingStep: '滾動步驟',
    selectingZoom: '選擇',
    durationMillis: '持續時間 (毫秒)',
    crosshairLabel: '標籤',
    crosshairSnap: '吸附到節點',
    normal: '正常',
    bold: '加粗',
    italic: '斜體',
    boldItalic: '粗斜體',
    predefined: '預定義',
    fillOpacity: '填充不透明度',
    strokeColor: '線顏色',
    strokeOpacity: '線不透明度',
    miniChart: '迷你圖表',
    histogramBinCount: '直方圖箱數量',
    connectorLine: '連接線',
    seriesItems: '系列項目',
    seriesItemType: '項目類型',
    seriesItemPositive: '正面',
    seriesItemNegative: '負面',
    seriesItemLabels: '項目標籤',
    columnGroup: '柱狀圖',
    barGroup: '條形圖',
    pieGroup: '餅圖',
    lineGroup: '線圖',
    scatterGroup: 'X Y (散點圖)',
    areaGroup: '區域圖',
    polarGroup: '極地圖',
    statisticalGroup: '統計圖',
    hierarchicalGroup: '層次結構圖',
    specializedGroup: '專門圖',
    combinationGroup: '組合圖',
    groupedColumnTooltip: '分組',
    stackedColumnTooltip: '堆疊',
    normalizedColumnTooltip: '100% 堆疊',
    groupedBarTooltip: '分組',
    stackedBarTooltip: '堆疊',
    normalizedBarTooltip: '100% 堆疊',
    pieTooltip: '餅圖',
    donutTooltip: '甜甜圈圖',
    lineTooltip: '線圖',
    stackedLineTooltip: '堆疊',
    normalizedLineTooltip: '100% 堆疊',
    groupedAreaTooltip: '區域圖',
    stackedAreaTooltip: '堆疊區域圖',
    normalizedAreaTooltip: '100% 堆疊區域圖',
    scatterTooltip: '散點圖',
    bubbleTooltip: '氣泡圖',
    histogramTooltip: '直方圖',
    radialColumnTooltip: '徑向柱狀圖',
    radialBarTooltip: '徑向條形圖',
    radarLineTooltip: '雷達線圖',
    radarAreaTooltip: '雷達區域圖',
    nightingaleTooltip: '夜鶯圖',
    rangeBarTooltip: '範圍條形圖',
    rangeAreaTooltip: '範圍區域圖',
    boxPlotTooltip: '箱型圖',
    treemapTooltip: '樹圖',
    sunburstTooltip: '旭日圖',
    waterfallTooltip: '瀑布圖',
    heatmapTooltip: '熱圖',
    columnLineComboTooltip: '柱狀圖和折線圖',
    areaColumnComboTooltip: '區域圖和柱狀圖',
    customComboTooltip: '自定義組合',
    innerRadius: '內徑',
    startAngle: '開始角度',
    endAngle: '結束角度',
    reverseDirection: '反方向',
    groupPadding: '組間距',
    seriesPadding: '系列間距',
    tile: '圖塊',
    whisker: '觸角',
    cap: '帽子',
    capLengthRatio: '長度比例',
    labelPlacement: '標籤放置',
    inside: '內部',
    outside: '外部',
    noDataToChart: '沒有可用來繪製的數據。',
    pivotChartRequiresPivotMode: '樞紐圖需要啟用樞紐模式。',
    chartSettingsToolbarTooltip: '菜單',
    chartLinkToolbarTooltip: '與網格鏈接',
    chartUnlinkToolbarTooltip: '與網格解鏈接',
    chartDownloadToolbarTooltip: '下載圖表',
    chartMenuToolbarTooltip: '菜單',
    chartEdit: '編輯圖表',
    chartAdvancedSettings: '高級設置',
    chartLink: '鏈接到網格',
    chartUnlink: '與網格解鏈接',
    chartDownload: '下載圖表',
    histogramFrequency: '頻率',
    seriesChartType: '系列圖表類型',
    seriesType: '系列類型',
    secondaryAxis: '次要軸',
    seriesAdd: '添加系列',
    categoryAdd: '添加類別',
    bar: '條形圖',
    column: '柱狀圖',
    histogram: '直方圖',
    advancedSettings: '高級設置',
    direction: '方向',
    horizontal: '水平',
    vertical: '垂直',
    seriesGroupType: '組類型',
    groupedSeriesGroupType: '分組',
    stackedSeriesGroupType: '堆疊',
    normalizedSeriesGroupType: '100% 堆疊',
    legendEnabled: '已啟用',
    invalidColor: '顏色值無效',
    groupedColumnFull: '分組柱狀圖',
    stackedColumnFull: '堆疊柱狀圖',
    normalizedColumnFull: '100% 堆疊柱狀圖',
    groupedBarFull: '分組條形圖',
    stackedBarFull: '堆疊條形圖',
    normalizedBarFull: '100% 堆疊條形圖',
    stackedAreaFull: '堆疊區域圖',
    normalizedAreaFull: '100% 堆疊區域圖',
    customCombo: '自定義組合',
    funnel: '漏斗',
    coneFunnel: '圓錐漏斗',
    pyramid: '金字塔',
    funnelGroup: '漏斗',
    funnelTooltip: '漏斗',
    coneFunnelTooltip: '圓錐漏斗',
    pyramidTooltip: '金字塔',
    dropOff: '下降',
    stageLabels: '階段標籤',
    reverse: '反轉',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}。層次 ${variable}。按 ENTER 進行編輯。',
    ariaAdvancedFilterBuilderItemValidation: '${variable}。層次 ${variable}。${variable} 按 ENTER 進行編輯。',
    ariaAdvancedFilterBuilderList: '高級篩選器構建列表',
    ariaAdvancedFilterBuilderFilterItem: '篩選條件',
    ariaAdvancedFilterBuilderGroupItem: '篩選組',
    ariaAdvancedFilterBuilderColumn: '列',
    ariaAdvancedFilterBuilderOption: '選項',
    ariaAdvancedFilterBuilderValueP: '值',
    ariaAdvancedFilterBuilderJoinOperator: '連接運算符',
    ariaAdvancedFilterInput: '高級篩選器輸入',
    ariaChecked: '已選中',
    ariaColumn: '列',
    ariaColumnGroup: '列組',
    ariaColumnFiltered: '列被篩選',
    ariaColumnSelectAll: '切換所有欄的可見性',
    ariaDateFilterInput: '日期篩選器輸入',
    ariaDefaultListName: '列表',
    ariaFilterColumnsInput: '篩選列輸入',
    ariaFilterFromValue: '從值篩選',
    ariaFilterInput: '篩選器輸入',
    ariaFilterList: '篩選列表',
    ariaFilterToValue: '篩選到值',
    ariaFilterValue: '篩選值',
    ariaFilterMenuOpen: '打開篩選菜單',
    ariaFilteringOperator: '篩選運算符',
    ariaHidden: '已隱藏',
    ariaIndeterminate: '不確定',
    ariaInputEditor: '輸入編輯器',
    ariaMenuColumn: '按 ALT 下箭頭 打開列菜單',
    ariaFilterColumn: '按 CTRL ENTER 打開篩選器',
    ariaRowDeselect: '按 SPACE 取消選擇此行',
    ariaHeaderSelection: '帶有標題選擇的欄',
    ariaSelectAllCells: '按空格鍵以選擇所有單元格',
    ariaRowSelectAll: '按空格切換所有行的選擇',
    ariaRowToggleSelection: '按空格切換行選擇',
    ariaRowSelect: '按 SPACE 選擇此行',
    ariaRowSelectionDisabled: '此行的行選擇已禁用',
    ariaSearch: '搜索',
    ariaSortableColumn: '按 ENTER 排序',
    ariaToggleVisibility: '按 SPACE 切換可見性',
    ariaToggleCellValue: '按 SPACE 切換單元格值',
    ariaUnchecked: '未選中',
    ariaVisible: '可見',
    ariaSearchFilterValues: '搜索篩選值',
    ariaPageSizeSelectorLabel: '頁面大小',
    ariaChartMenuClose: '關閉圖表編輯菜單',
    ariaChartSelected: '已選擇',
    ariaSkeletonCellLoadingFailed: '行加載失敗',
    ariaSkeletonCellLoading: '行數據加載中',
    ariaDeferSkeletonCellLoading: '儲存格正在載入',

    // ARIA for Batch Edit
    ariaPendingChange: '待處理的變更',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: '行群組',
    ariaValuesDropZonePanelLabel: '值',
    ariaPivotDropZonePanelLabel: '列標籤',
    ariaDropZoneColumnComponentDescription: '按 DELETE 以刪除',
    ariaDropZoneColumnValueItemDescription: '按 ENTER 以更改聚合類型',
    ariaDropZoneColumnGroupItemDescription: '按 ENTER 以排序',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' 的 ',
    ariaDropZoneColumnComponentSortAscending: '升序',
    ariaDropZoneColumnComponentSortDescending: '降序',
    ariaLabelDialog: '對話框',
    ariaLabelColumnMenu: '列選單',
    ariaLabelColumnFilter: '列篩選',
    ariaLabelSelectField: '選擇欄位',

    // Cell Editor
    ariaLabelCellEditor: '單元編輯器',
    ariaValidationErrorPrefix: '單元編輯器驗證',
    ariaLabelLoadingContextMenu: '加載上下文選單',

    // aria labels for rich select
    ariaLabelRichSelectField: '豐富選擇欄位',
    ariaLabelRichSelectToggleSelection: '按空格切換選擇',
    ariaLabelRichSelectDeselectAllItems: '按Delete鍵取消選擇所有項目',
    ariaLabelRichSelectDeleteSelection: '按Delete鍵取消選擇項目',
    ariaLabelTooltip: '工具提示',
    ariaLabelContextMenu: '上下文選單',
    ariaLabelSubMenu: '子選單',
    ariaLabelAggregationFunction: '聚合函數',
    ariaLabelAdvancedFilterAutocomplete: '高級過濾自動完成',
    ariaLabelAdvancedFilterBuilderAddField: '高級過濾構建器添加欄位',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高級過濾構建器列選擇欄位',
    ariaLabelAdvancedFilterBuilderOptionSelectField: '高級過濾構建器選項選擇欄位',
    ariaLabelAdvancedFilterBuilderJoinSelectField: '高級過濾構建器連接操作符選擇欄位',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: '欄位清單',
    ariaFilterPanelList: '篩選清單',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: '新增篩選字段',
    ariaLabelFilterCardDelete: '刪除篩選',
    ariaLabelFilterCardHasEdits: '已有編輯',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: '真',
    false: '假',
    invalidDate: '無效日期',
    invalidNumber: '無效數字',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS 上午/下午',
};
