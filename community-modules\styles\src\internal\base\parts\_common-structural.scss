@use 'ag';

@mixin output {
    // /**
    //  ****************************
    //  * Generic Styles
    //  ****************************
    // */
    // if we don't do this, then the width and height of the grid would be ignored,
    // as there is no default display for the element ag-grid-a2 (as it's not a natural dom element)
    ag-grid,
    ag-grid-angular,
    ag-grid-ng2,
    ag-grid-polymer,
    ag-grid-aurelia {
        display: block;
    }

    // ARIA
    .ag-aria-description-container {
        z-index: 9999;
        border: 0px;
        clip: rect(1px, 1px, 1px, 1px);
        height: 1px;
        width: 1px;
        position: absolute;
        overflow: hidden;
        padding: 0px;
        white-space: nowrap;
    }

    .ag-hidden {
        display: none !important;
    }

    .ag-invisible {
        visibility: hidden !important;
    }

    .ag-drag-handle {
        cursor: grab;
    }

    .ag-column-drop-wrapper {
        display: flex;
    }

    .ag-column-drop-horizontal-half-width {
        display: inline-block;
        width: 50% !important;
    }

    .ag-unselectable {
        @include ag.selectable(none);
    }

    .ag-selectable {
        @include ag.selectable(text);
    }

    .ag-tab {
        position: relative;
    }

    .ag-tab-guard {
        position: absolute;
        width: 0;
        height: 0;
        display: block;
    }

    // special setting for lists to prevent scroll
    // while tabbing out of the container
    .ag-virtual-list-viewport .ag-tab-guard {
        // this keeps the tabguard within "visible" range
        // while scrolling so focusing the tabguard doesn't
        // scroll the list viewport.
        position: sticky;
    }

    .ag-tab-guard-top {
        top: 1px;
    }

    .ag-tab-guard-bottom {
        bottom: 1px;
    }

    .ag-select-agg-func-popup {
        position: absolute;
    }

    .ag-input-wrapper,
    .ag-picker-field-wrapper {
        display: flex;
        flex: 1 1 auto;
        align-items: center;
        line-height: normal;
        position: relative;
    }

    // setting shake class to an item will give it a left ot right animation
    // used for the 'left' and 'right' arrows when dragging columns and scrolling
    .ag-shake-left-to-right {
        animation-direction: alternate;
        animation-duration: 0.2s;
        animation-iteration-count: infinite;
        animation-name: ag-shake-left-to-right;
    }

    @keyframes ag-shake-left-to-right {
        from {
            padding-left: 6px;
            padding-right: 2px;
        }

        to {
            padding-left: 2px;
            padding-right: 6px;
        }
    }

    .ag-root-wrapper {
        cursor: default;
        position: relative; // set to relative, so absolute popups appear relative to this
        display: flex;
        flex-direction: column;
        overflow: hidden;
        white-space: normal;

        &.ag-layout-normal {
            height: 100%;
        }
    }

    .ag-watermark {
        position: absolute;
        bottom: 20px;
        right: 25px;
        opacity: 0.7;
        transition: opacity 1s ease-out 3s;
        color: #9b9b9b;
        &::before {
            content: '';
            background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjA5IiBoZWlnaHQ9IjM2IiB2aWV3Qm94PSIwIDAgMjA5IDM2IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cGF0aCBkPSJNMTkyLjk5MyAyMy42NTgyVjE1LjcxMTdIMTc5LjQ1MkwxNzEuNTA1IDIzLjY1ODJIMTkyLjk5M1oiIGZpbGw9IiM5QjlCOUIiLz4KPHBhdGggZD0iTTIwOC4yNSAzLjk1MDgxSDE5MS4yNzZMMTgzLjI2NiAxMS44OTczSDIwOC4yNVYzLjk1MDgxWiIgZmlsbD0iIzlCOUI5QiIvPgo8cGF0aCBkPSJNMTYzLjYyMiAzMS42MDQ4TDE2Ny42OTEgMjcuNTM2MUgxODEuNDIzVjM1LjQ4MjdIMTYzLjYyMlYzMS42MDQ4WiIgZmlsbD0iIzlCOUI5QiIvPgo8cGF0aCBkPSJNMTY2LjYxIDE5Ljc4MDNIMTc1LjM4M0wxODMuMzkzIDExLjgzMzdIMTY2LjYxVjE5Ljc4MDNaIiBmaWxsPSIjOUI5QjlCIi8+CjxwYXRoIGQ9Ik0xNTcuMDExIDMxLjYwNDdIMTYzLjYyMkwxNzEuNTA1IDIzLjY1ODJIMTU3LjAxMVYzMS42MDQ3WiIgZmlsbD0iIzlCOUI5QiIvPgo8cGF0aCBkPSJNMTkxLjI3NiAzLjk1MDgxTDE4Ny4yMDggOC4wMTk0MUgxNjEuMjdWMC4wNzI4NzZIMTkxLjI3NlYzLjk1MDgxWiIgZmlsbD0iIzlCOUI5QiIvPgo8cGF0aCBkPSJNMjAuODM5MSAzMC4yMDYxSDguMzc4OTJMNi4yMTc0NSAzNS41NDYySDAuNzUwMjQ0TDEyLjI1NjggOC41OTE1NUgxNy4wMjQ3TDI4LjUzMTMgMzUuNTQ2MkgyMy4wMDA1TDIwLjgzOTEgMzAuMjA2MVpNMTkuMTIyNyAyNS45NDY4TDE0LjYwOSAxNC45NDg4TDEwLjA5NTQgMjUuOTQ2OEgxOS4xMjI3WiIgZmlsbD0iIzlCOUI5QiIvPgo8cGF0aCBkPSJNMTA0LjQzNyAxOC41MDg5QzEwNi4wMjYgMTYuMTU2NyAxMTAuMDMxIDE1LjkwMjQgMTExLjY4NCAxNS45MDI0VjIwLjQ3OTZDMTA5LjY1IDIwLjQ3OTYgMTA3LjYxNSAyMC41NDMyIDEwNi40MDcgMjEuNDMzMkMxMDUuMiAyMi4zMjMyIDEwNC41NjQgMjMuNTMxMSAxMDQuNTY0IDI0Ljk5MzJWMzUuNTQ2Mkg5OS42MDUxVjE1LjkwMjRIMTA0LjM3M0wxMDQuNDM3IDE4LjUwODlaIiBmaWxsPSIjOUI5QjlCIi8+CjxwYXRoIGQ9Ik0xMTkuMzc2IDE1LjkwMjRIMTE0LjQxOFYzNS41NDYySDExOS4zNzZWMTUuOTAyNFoiIGZpbGw9IiM5QjlCOUIiLz4KPHBhdGggZD0iTTExOS4zNzYgNy4xMjkzOUgxMTQuNDE4VjEyLjk3OEgxMTkuMzc2VjcuMTI5MzlaIiBmaWxsPSIjOUI5QjlCIi8+CjxwYXRoIGQ9Ik0xNDMuOTc5IDcuMTI5MzlWMzUuNTQ2MkgxMzkuMjExTDEzOS4wODQgMzIuNTU4M0MxMzguMzg0IDMzLjU3NTUgMTM3LjQ5NCAzNC40MDE5IDEzNi40MTQgMzUuMDM3NkMxMzUuMzMzIDM1LjYwOTggMTMzLjk5OCAzNS45Mjc2IDEzMi40NzIgMzUuOTI3NkMxMzEuMTM3IDM1LjkyNzYgMTI5Ljg2NiAzNS42NzMzIDEyOC43ODUgMzUuMjI4M0MxMjcuNjQxIDM0LjcxOTcgMTI2LjYyMyAzNC4wODQgMTI1Ljc5NyAzMy4xOTRDMTI0Ljk3MSAzMi4zMDQgMTI0LjI3MSAzMS4yMjMzIDEyMy44MjYgMzAuMDE1NEMxMjMuMzE4IDI4LjgwNzUgMTIzLjEyNyAyNy40MDkgMTIzLjEyNyAyNS44ODMyQzEyMy4xMjcgMjQuMzU3NSAxMjMuMzgxIDIyLjk1ODkgMTIzLjgyNiAyMS42ODc0QzEyNC4zMzUgMjAuNDE2IDEyNC45NzEgMTkuMzM1MyAxMjUuNzk3IDE4LjQ0NTNDMTI2LjYyMyAxNy41NTUyIDEyNy42NDEgMTYuODU2IDEyOC43ODUgMTYuMzQ3NEMxMjkuOTI5IDE1LjgzODggMTMxLjEzNyAxNS41ODQ1IDEzMi40NzIgMTUuNTg0NUMxMzMuOTk4IDE1LjU4NDUgMTM1LjI2OSAxNS44Mzg4IDEzNi4zNSAxNi40MTA5QzEzNy40MzEgMTYuOTgzMSAxMzguMzIxIDE3Ljc0NTkgMTM5LjAyIDE4LjgyNjdWNy4xOTI5NUgxNDMuOTc5VjcuMTI5MzlaTTEzMy41NTMgMzEuNjY4M0MxMzUuMjA2IDMxLjY2ODMgMTM2LjQ3NyAzMS4wOTYyIDEzNy40OTQgMzAuMDE1NEMxMzguNTExIDI4LjkzNDcgMTM5LjAyIDI3LjQ3MjUgMTM5LjAyIDI1LjY5MjVDMTM5LjAyIDIzLjkxMjUgMTM4LjUxMSAyMi41MTM5IDEzNy40OTQgMjEuMzY5NkMxMzYuNDc3IDIwLjI4ODggMTM1LjIwNiAxOS43MTY3IDEzMy41NTMgMTkuNzE2N0MxMzEuOTYzIDE5LjcxNjcgMTMwLjYyOCAyMC4yODg4IDEyOS42NzUgMjEuMzY5NkMxMjguNjU4IDIyLjQ1MDMgMTI4LjE0OSAyMy45MTI1IDEyOC4xNDkgMjUuNjkyNUMxMjguMTQ5IDI3LjQ3MjUgMTI4LjY1OCAyOC44NzExIDEyOS42NzUgMjkuOTUxOEMxMzAuNjkyIDMxLjA5NjEgMTMxLjk2MyAzMS42NjgzIDEzMy41NTMgMzEuNjY4M1oiIGZpbGw9IiM5QjlCOUIiLz4KPHBhdGggZD0iTTU3LjIwMjQgMjAuMzUyNUg0NC45MzNWMjQuNjExOEg1MS45MjU5QzUxLjczNTIgMjYuNzczMyA1MC45MDg4IDI4LjQyNjEgNDkuNTEwMiAyOS43NjExQzQ4LjExMTYgMzEuMDMyNiA0Ni4zMzE1IDMxLjY2ODMgNDQuMDQyOSAzMS42NjgzQzQyLjc3MTUgMzEuNjY4MyA0MS41NjM2IDMxLjQxNCA0MC41NDY1IDMwLjk2OUMzOS40NjU3IDMwLjUyNCAzOC41NzU3IDI5Ljg4ODMgMzcuODEyOSAyOC45OTgzQzM3LjA1IDI4LjE3MTggMzYuNDc3OCAyNy4xNTQ3IDM2LjAzMjggMjUuOTQ2OEMzNS41ODc4IDI0LjczODkgMzUuMzk3MSAyMy40Njc1IDM1LjM5NzEgMjIuMDA1M0MzNS4zOTcxIDIwLjU0MzIgMzUuNTg3OCAxOS4yNzE3IDM2LjAzMjggMTguMDYzOEMzNi40MTQzIDE2Ljg1NiAzNy4wNSAxNS45MDI0IDM3LjgxMjkgMTUuMDEyNEMzOC41NzU3IDE0LjE4NTkgMzkuNDY1NyAxMy41NTAyIDQwLjU0NjUgMTMuMDQxNkM0MS42MjcyIDEyLjU5NjYgNDIuNzcxNSAxMi4zNDIzIDQ0LjEwNjUgMTIuMzQyM0M0Ni43NzY2IDEyLjM0MjMgNDguODEwOSAxMi45NzggNTAuMjA5NSAxNC4yNDk1TDUzLjUxNTIgMTAuOTQzOEM1MS4wMzU5IDkuMDM2NTkgNDcuODU3MyA4LjAxOTQxIDQ0LjEwNjUgOC4wMTk0MUM0Mi4wMDg2IDguMDE5NDEgNDAuMTAxNSA4LjMzNzI5IDM4LjM4NSA5LjAzNjU5QzM2LjY2ODYgOS43MzU4OCAzNS4yMDY0IDEwLjYyNTkgMzMuOTk4NSAxMS44MzM3QzMyLjc5MDYgMTMuMDQxNiAzMS44MzcxIDE0LjUwMzggMzEuMjAxNCAxNi4yMjAzQzMwLjU2NTYgMTcuOTM2NyAzMC4yNDc4IDE5Ljg0MzggMzAuMjQ3OCAyMS44NzgyQzMwLjI0NzggMjMuOTEyNSAzMC41NjU2IDI1LjgxOTcgMzEuMjY0OSAyNy41MzYxQzMxLjk2NDIgMjkuMjUyNiAzMi44NTQyIDMwLjcxNDcgMzQuMDYyMSAzMS45MjI2QzM1LjI3IDMzLjEzMDUgMzYuNzMyMSAzNC4wODQxIDM4LjQ0ODYgMzQuNzE5OEM0MC4xNjUgMzUuNDE5MSA0Mi4wNzIyIDM1LjczNyA0NC4xMDY1IDM1LjczN0M0Ni4xNDA4IDM1LjczNyA0Ny45ODQ0IDM1LjQxOTEgNDkuNjM3MyAzNC43MTk4QzUxLjI5MDIgMzQuMDIwNSA1Mi42ODg4IDMzLjEzMDUgNTMuODMzMSAzMS45MjI2QzU0Ljk3NzQgMzAuNzE0NyA1NS44Njc0IDI5LjI1MjYgNTYuNTAzMSAyNy41MzYxQzU3LjEzODggMjUuODE5NyA1Ny40NTY3IDIzLjkxMjUgNTcuNDU2NyAyMS44NzgyVjIxLjA1MTdDNTcuMjY2IDIwLjkyNDYgNTcuMjAyNCAyMC42MDY3IDU3LjIwMjQgMjAuMzUyNVoiIGZpbGw9IiM5QjlCOUIiLz4KPHBhdGggZD0iTTk1Ljk4MTUgMjAuMzUyNUg4My43MTIxVjI0LjYxMThIOTAuNzA1QzkwLjUxNDMgMjYuNzczMyA4OS42ODc5IDI4LjQyNjEgODguMjg5MyAyOS43NjExQzg2Ljg5MDcgMzEuMDMyNiA4NS4xMTA2IDMxLjY2ODMgODIuODIyIDMxLjY2ODNDODEuNTUwNiAzMS42NjgzIDgwLjM0MjcgMzEuNDE0IDc5LjMyNTYgMzAuOTY5Qzc4LjI0NDggMzAuNTI0IDc3LjM1NDggMjkuODg4MyA3Ni41OTIgMjguOTk4M0M3NS44MjkxIDI4LjE3MTggNzUuMjU3IDI3LjE1NDcgNzQuODExOSAyNS45NDY4Qzc0LjM2NjkgMjQuNzM4OSA3NC4xNzYyIDIzLjQ2NzUgNzQuMTc2MiAyMi4wMDUzQzc0LjE3NjIgMjAuNTQzMiA3NC4zNjY5IDE5LjI3MTcgNzQuODExOSAxOC4wNjM4Qzc1LjE5MzQgMTYuODU2IDc1LjgyOTEgMTUuOTAyNCA3Ni41OTIgMTUuMDEyNEM3Ny4zNTQ4IDE0LjE4NTkgNzguMjQ0OCAxMy41NTAyIDc5LjMyNTYgMTMuMDQxNkM4MC40MDYzIDEyLjU5NjYgODEuNTUwNiAxMi4zNDIzIDgyLjg4NTYgMTIuMzQyM0M4NS41NTU3IDEyLjM0MjMgODcuNTkgMTIuOTc4IDg4Ljk4ODYgMTQuMjQ5NUw5Mi4yOTQzIDEwLjk0MzhDODkuODE1IDkuMDM2NTkgODYuNjM2NCA4LjAxOTQxIDgyLjg4NTYgOC4wMTk0MUM4MC43ODc4IDguMDE5NDEgNzguODgwNiA4LjMzNzI5IDc3LjE2NDEgOS4wMzY1OUM3NS40NDc3IDkuNzM1ODggNzMuOTg1NSAxMC42MjU5IDcyLjc3NzYgMTEuODMzN0M3MS41Njk4IDEzLjA0MTYgNzAuNjE2MiAxNC41MDM4IDY5Ljk4MDUgMTYuMjIwM0M2OS4zNDQ3IDE3LjkzNjcgNjkuMDI2OSAxOS44NDM4IDY5LjAyNjkgMjEuODc4MkM2OS4wMjY5IDIzLjkxMjUgNjkuMzQ0NyAyNS44MTk3IDcwLjA0NCAyNy41MzYxQzcwLjc0MzMgMjkuMjUyNiA3MS42MzM0IDMwLjcxNDcgNzIuODQxMiAzMS45MjI2Qzc0LjA0OTEgMzMuMTMwNSA3NS41MTEyIDM0LjA4NDEgNzcuMjI3NyAzNC43MTk4Qzc4Ljk0NDEgMzUuNDE5MSA4MC44NTEzIDM1LjczNyA4Mi44ODU2IDM1LjczN0M4NC45MiAzNS43MzcgODYuNzYzNiAzNS40MTkxIDg4LjQxNjQgMzQuNzE5OEM5MC4wNjkzIDM0LjAyMDUgOTEuNDY3OSAzMy4xMzA1IDkyLjYxMjIgMzEuOTIyNkM5My43NTY1IDMwLjcxNDcgOTQuNjQ2NSAyOS4yNTI2IDk1LjI4MjIgMjcuNTM2MUM5NS45MTggMjUuODE5NyA5Ni4yMzU4IDIzLjkxMjUgOTYuMjM1OCAyMS44NzgyVjIxLjA1MTdDOTYuMDQ1MSAyMC45MjQ2IDk1Ljk4MTUgMjAuNjA2NyA5NS45ODE1IDIwLjM1MjVaIiBmaWxsPSIjOUI5QjlCIi8+Cjwvc3ZnPgo=);
            background-repeat: no-repeat;
            background-size: 170px 40px;
            display: block;
            height: 40px;
            width: 170px;
        }
    }

    .ag-watermark-text {
        opacity: 0.5;
        font-weight: bold;
        font-family: Impact, sans-serif;
        font-size: 19px;
        padding-left: 0.7rem;
    }

    .ag-root-wrapper-body {
        display: flex;
        flex-direction: row;

        &.ag-layout-normal {
            flex: 1 1 auto;
            height: 0;
            min-height: 0;
        }
    }

    .ag-root {
        position: relative; // set to relative, so absolute popups appear relative to this
        display: flex;
        flex-direction: column;

        &.ag-layout-normal,
        &.ag-layout-auto-height {
            overflow: hidden; // was getting some 'shouldn't be there' scrolls, this sorts it out
            flex: 1 1 auto;
            width: 0;
        }

        &.ag-layout-normal {
            height: 100%;
        }
    }

    // /**
    //  ****************************
    //  * Viewports
    //  ****************************
    // */
    .ag-header-viewport,
    .ag-floating-top-viewport,
    .ag-body-viewport,
    .ag-center-cols-viewport,
    .ag-floating-bottom-viewport,
    .ag-body-horizontal-scroll-viewport,
    .ag-body-vertical-scroll-viewport,
    .ag-virtual-list-viewport,
    .ag-sticky-top-viewport,
    .ag-sticky-bottom-viewport {
        position: relative;
        height: 100%;
        min-width: 0px;
        overflow: hidden;
        flex: 1 1 auto;
    }

    .ag-body-viewport,
    .ag-center-cols-viewport,
    .ag-header-viewport,
    .ag-floating-top-viewport,
    .ag-floating-bottom-viewport,
    .ag-sticky-top-viewport,
    .ag-sticky-bottom-viewport {
        overflow-x: auto;
        &::-webkit-scrollbar {
            display: none !important;
        }
        -ms-overflow-style: none !important;
        scrollbar-width: none !important;
    }

    .ag-body-viewport {
        display: flex;
        overflow-x: hidden;

        &.ag-layout-normal {
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
        }
    }

    .ag-viewport {
        position: relative;
    }

    .ag-spanning-container {
        position: absolute;
        top: 0;

        // /* z-index: 1 appears over editing rows but under dragging rows */
        z-index: 1;
    }

    // the sticky and pinned containers need at least one 1px height
    // so we can set the scrollLeft position when there are
    // no rows within, otherwise scrollLeft will always be 0
    // and then making a row sticky causes the grid to scroll.
    .ag-sticky-top-container,
    .ag-sticky-bottom-container,
    .ag-floating-top-container,
    .ag-floating-bottom-container {
        min-height: 1px;
    }

    // /* sticky containers need to appear over spanned and edited rows */
    .ag-sticky-top,
    .ag-sticky-bottom {
        z-index: 1;
    }

    .ag-center-cols-viewport {
        min-height: 100%;
        width: 100%;
    }

    .ag-body-horizontal-scroll-viewport {
        overflow-x: scroll;
    }

    .ag-body-vertical-scroll-viewport {
        overflow-y: scroll;
    }

    .ag-virtual-list-viewport {
        overflow: auto;
        width: 100%;
    }

    // /**
    //  ****************************
    //  * Containers
    //  ****************************
    // */
    .ag-header-container,
    .ag-floating-top-container,
    .ag-body-container,
    .ag-pinned-right-cols-container,
    .ag-center-cols-container,
    .ag-pinned-left-cols-container,
    .ag-floating-bottom-container,
    .ag-body-horizontal-scroll-container,
    .ag-body-vertical-scroll-container,
    .ag-full-width-container,
    .ag-floating-bottom-full-width-container,
    .ag-virtual-list-container,
    .ag-sticky-top-container,
    .ag-sticky-bottom-container {
        position: relative;
    }

    // for when auto height is used but there is no row data
    .ag-header-container,
    .ag-floating-top-container,
    .ag-pinned-left-floating-top,
    .ag-pinned-right-floating-top,
    .ag-floating-bottom-container,
    .ag-pinned-left-floating-bottom,
    .ag-pinned-right-floating-bottom,
    .ag-sticky-top-container,
    .ag-sticky-bottom-container {
        height: 100%;
        white-space: nowrap;
    }

    .ag-center-cols-container {
        display: block;
    }

    .ag-pinned-right-cols-container {
        display: block;
    }

    .ag-body-horizontal-scroll-container {
        height: 100%;
    }

    .ag-body-vertical-scroll-container {
        width: 100%;
    }

    .ag-full-width-container,
    .ag-floating-top-full-width-container,
    .ag-floating-bottom-full-width-container,
    .ag-sticky-top-full-width-container,
    .ag-sticky-bottom-full-width-container {
        position: absolute;
        top: 0px;
        @include ag.unthemed-rtl(
            (
                left: 0,
            )
        );
        // turn off pointer events, because this container overlays the main row containers.
        // so when user clicks on space between full width rows, we want the mouse clicks to
        // pass onto the underlying container where the real rows are. eg if using full width
        // for row grouping, the groups will be in the full width container, but when user
        // opens a group the children are shown in the other containers - we want to make sure we
        // don't block mouse clicks to those other containers with the children.
        pointer-events: none;
    }

    .ag-full-width-container {
        width: 100%;
    }

    .ag-floating-bottom-full-width-container,
    .ag-floating-top-full-width-container {
        display: inline-block;
        overflow: hidden;
        height: 100%;
        width: 100%;
    }

    .ag-virtual-list-container {
        overflow: hidden;
    }

    // /**
    //  ****************************
    //  * Scrollers
    //  ****************************
    // */
    .ag-body {
        position: relative;
        display: flex;
        flex: 1 1 auto;
        flex-direction: row !important; // we have to state this for rtl, otherwise row-reverse is inherited
        min-height: 0;
    }

    .ag-body-horizontal-scroll,
    .ag-body-vertical-scroll {
        min-height: 0;
        min-width: 0;
        display: flex;
        position: relative;
        &.ag-scrollbar-invisible {
            position: absolute;
            bottom: 0;
            &.ag-apple-scrollbar {
                opacity: 0;
                transition: opacity 400ms;
                visibility: hidden;
                &.ag-scrollbar-scrolling,
                &.ag-scrollbar-active {
                    visibility: visible;
                    opacity: 1;
                }
            }
        }
    }

    .ag-body-horizontal-scroll {
        width: 100%;
        &.ag-scrollbar-invisible {
            left: 0;
            right: 0;
        }
    }

    .ag-body-vertical-scroll {
        height: 100%;
        &.ag-scrollbar-invisible {
            top: 0;
            z-index: 10;
            @include ag.unthemed-rtl(
                (
                    right: 0,
                )
            );
        }
    }

    .ag-force-vertical-scroll {
        overflow-y: scroll !important;
    }

    .ag-horizontal-left-spacer,
    .ag-horizontal-right-spacer {
        height: 100%;
        min-width: 0;
        overflow-x: scroll;
        &.ag-scroller-corner {
            overflow-x: hidden;
        }
    }

    // /**
    //  ****************************
    //  * Headers
    //  ****************************
    // */
    .ag-header,
    .ag-pinned-left-header,
    .ag-pinned-right-header {
        display: inline-block;
        overflow: hidden;
        position: relative;
    }

    .ag-header-cell-sortable .ag-header-cell-label {
        cursor: pointer;
    }

    .ag-header {
        display: flex;
        width: 100%;
        white-space: nowrap;
    }

    .ag-pinned-left-header {
        height: 100%;
    }

    .ag-pinned-right-header {
        height: 100%;
    }

    .ag-header-row {
        position: absolute;
    }

    .ag-header-row:not(.ag-header-row-column-group) {
        // so when floating filters are height 0px, the contents don't spill out
        overflow: hidden;
    }

    .ag-header.ag-header-allow-overflow .ag-header-row {
        overflow: visible;
    }

    .ag-header-cell {
        display: inline-flex;
        align-items: center;
        position: absolute;
        height: 100%;
        // without this the ag-header-row becomes scrollable
        overflow: hidden;
    }

    .ag-header-cell.ag-header-active .ag-header-cell-menu-button,
    .ag-header-cell-filter-button {
        opacity: 1;
    }

    .ag-header-cell-menu-button:not(.ag-header-menu-always-show) {
        transition: opacity 0.2s;
        opacity: 0;
    }

    .ag-header-group-cell-label,
    .ag-header-cell-label {
        display: flex;
        flex: 1 1 auto;
        align-self: stretch;
        align-items: center;
        overflow: hidden;
    }

    .ag-header-cell-label {
        text-overflow: ellipsis;
    }

    .ag-header-group-cell-label.ag-sticky-label {
        position: sticky;
        flex: none;
        max-width: 100%;
        overflow: visible;
    }

    .ag-header-group-text {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ag-header-cell-text {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .ag-header-group-cell .ag-header-cell-comp-wrapper {
        display: flex;
    }

    .ag-header-cell:not(.ag-header-cell-auto-height) .ag-header-cell-comp-wrapper {
        height: 100%;
        display: flex;
        align-items: center;
    }

    .ag-header-cell-comp-wrapper {
        width: 100%;
    }

    // wrap header height for Headers and Group Headers
    .ag-header-cell-wrap-text .ag-header-cell-comp-wrapper {
        white-space: normal;
    }

    .ag-header-cell-comp-wrapper-limited-height > div {
        overflow: hidden;
    }

    .ag-right-aligned-header .ag-header-cell-label {
        flex-direction: row-reverse;
    }

    .ag-header-cell-resize {
        position: absolute;
        z-index: 2;
        height: 100%;
        width: 8px;
        top: 0;

        cursor: ew-resize;

        // unpinned headers get their rezise handle on the right in normal mode and left in RTL mode
        @include ag.unthemed-rtl(
            (
                right: -3px,
            )
        );
    }

    .ag-pinned-left-header .ag-header-cell-resize {
        right: -3px; // pinned left headers always have their resize on the right, even in RTL mode
    }

    .ag-pinned-right-header .ag-header-cell-resize {
        left: -3px; // pinned right headers always have their resize on the left, even in RTL mode
    }

    .ag-header-select-all {
        display: flex;
    }

    .ag-header-cell-menu-button,
    .ag-header-cell-filter-button,
    .ag-side-button-button,
    .ag-panel-title-bar-button,
    .ag-floating-filter-button-button {
        cursor: pointer;
    }

    // /**
    //  ****************************
    //  * Columns
    //  ****************************
    // */
    .ag-column-moving {
        .ag-cell {
            transition: left 0.2s;
        }

        .ag-header-cell {
            transition: left 0.2s;
        }

        .ag-spanned-cell-wrapper {
            transition: left 0.2s;
        }

        .ag-header-group-cell {
            transition:
                left 0.2s,
                width 0.2s;
        }
    }

    // /**
    //  ****************************
    //  * Column Panel
    //  ****************************
    // */

    .ag-column-panel {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        flex: 1 1 auto;
    }

    .ag-column-select {
        position: relative;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        flex: 3 1 0px;
    }

    .ag-column-select-header {
        position: relative;
        display: flex;
        flex: none;
    }

    .ag-column-select-header-icon {
        position: relative;
    }

    .ag-column-select-header-filter-wrapper {
        flex: 1 1 auto;
    }

    .ag-column-select-header-filter {
        width: 100%;
    }

    .ag-column-select-list {
        flex: 1 1 0px;
        overflow: hidden;
    }

    .ag-column-drop {
        position: relative;
        display: inline-flex;
        align-items: center;
        overflow: auto;
        width: 100%;
    }

    .ag-column-drop-list {
        display: flex;
        align-items: center;
    }

    .ag-column-drop-cell {
        position: relative;
        display: flex;
        align-items: center;
    }

    .ag-column-drop-cell-text {
        overflow: hidden;
        flex: 1 1 auto;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .ag-column-drop-vertical {
        display: flex;
        flex-direction: column;
        overflow: hidden;
        align-items: stretch;
        flex: 1 1 0px;
    }

    .ag-column-drop-vertical-title-bar {
        display: flex;
        align-items: center;
        flex: none;
    }

    .ag-column-drop-vertical-list {
        position: relative;
        align-items: stretch;
        flex-grow: 1;
        flex-direction: column;
        overflow-x: auto;

        > * {
            flex: none;
        }
    }

    .ag-column-drop-empty .ag-column-drop-vertical-list {
        overflow: hidden;
    }

    .ag-column-drop-vertical-empty-message {
        display: block;
    }

    .ag-column-drop.ag-column-drop-horizontal {
        white-space: nowrap;
        overflow: hidden;
    }

    .ag-column-drop-cell-button {
        cursor: pointer;
    }

    .ag-filter-toolpanel {
        flex: 1 1 0px;
        min-width: 0;
    }

    .ag-filter-toolpanel-header {
        position: relative;
    }

    .ag-filter-toolpanel-header,
    .ag-filter-toolpanel-search {
        display: flex;
        align-items: center;

        > * {
            display: flex;
            align-items: center;
        }
    }

    .ag-filter-apply-panel {
        display: flex;
        justify-content: flex-end;
        overflow: hidden;
    }

    // /**
    //  ****************************
    //  * Rows
    //  ****************************
    // */
    // for row animations.
    .ag-row-animation .ag-row {
        transition:
            transform 0.4s,
            top 0.4s,
            opacity 0.2s;
    }
    // for rows older than one second, we also animate the height. we don't include the height
    // initially so we are not animating auto-height rows on initial render.
    .ag-row-animation .ag-row.ag-after-created {
        transition:
            transform 0.4s,
            top 0.4s,
            height 0.4s,
            opacity 0.2s;
    }

    .ag-row-animation.ag-prevent-animation .ag-row,
    .ag-row-animation.ag-prevent-animation .ag-row.ag-after-created {
        transition: none !important;
    }

    .ag-row-no-animation .ag-row {
        transition: none;
    }

    .ag-row {
        white-space: nowrap;
        width: 100%;
    }

    .ag-row-loading {
        display: flex;
        align-items: center;
    }

    .ag-row-position-absolute {
        position: absolute;
    }

    .ag-row-position-relative {
        position: relative;
    }

    .ag-full-width-row {
        overflow: hidden;
        // turn events back on, as we removed them in the parent
        pointer-events: all;
    }

    .ag-row-inline-editing {
        z-index: 1;
    }

    .ag-row-dragging {
        z-index: 2;
    }

    .ag-stub-cell {
        display: flex;
        align-items: center;
    }

    // /**
    //  ****************************
    //  * Cells
    //  ****************************
    // */
    .ag-cell {
        display: inline-block;
        position: absolute;
        white-space: nowrap;
        height: 100%;
    }

    // This is used when using a Cell Wrapper (eg row drag, selection, or auto-height).
    // If not using wrapper, ag-cell-value is on a div, which is 100% width. However when
    // in a wrapper, it's a span (not a div), so we need 100% width to provide consistent
    // behaviour regardless of wrapper used or not. If we did not do this, Cell Renderer's
    // with 100% width wouldn't get the full width when using a wrapper.
    // Instead of just 100% width we use flex, as it's not the only item on the line, so it
    // fills the remaining space.
    .ag-cell-value {
        flex: 1 1 auto;
    }

    .ag-cell-value,
    .ag-group-value {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ag-cell-wrapper {
        display: flex;
        align-items: center;
        // adding overflow: hidden breaks the checkbox focus outline
        // overflow: hidden;
        // adding width: 100% here breaks text-overflow: ellipsis
        // width: 100%;
        &.ag-row-group {
            align-items: flex-start;
        }
    }

    .ag-cell-wrap-text {
        white-space: normal;
        word-break: break-word;
    }

    .ag-cell-wrap-text:not(.ag-cell-auto-height) .ag-cell-wrapper {
        align-items: normal;
        height: 100%;
        .ag-cell-value {
            height: 100%;
        }
    }

    .ag-sparkline-wrapper {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        line-height: initial;
    }

    .ag-full-width-row .ag-cell-wrapper.ag-row-group {
        height: 100%;
        align-items: center;
    }

    .ag-cell-inline-editing {
        z-index: 1;

        .ag-cell-wrapper,
        .ag-cell-edit-wrapper,
        .ag-cell-editor,
        .ag-cell-editor .ag-wrapper,
        // Triple class used to increase specificity and override default text input styles.
        .ag-cell-editor.ag-cell-editor.ag-cell-editor input {
            height: 100%;
            min-height: 100%;
            width: 100%;
            line-height: normal;
        }
    }

    .ag-row.ag-row-editing-invalid .ag-cell-inline-editing {
        opacity: 0.8;
    }

    .ag-cell .ag-icon {
        display: inline-block;
        vertical-align: middle;
    }

    // /**
    //  ****************************
    //  * Filters
    //  ****************************
    // */
    .ag-set-filter-item {
        display: flex;
        align-items: center;
        height: 100%;
    }

    .ag-set-filter-item-checkbox {
        display: flex;
        width: 100%;
        height: 100%;
    }

    .ag-set-filter-group-icons {
        display: block;
        > * {
            cursor: pointer;
        }
    }

    .ag-filter-body-wrapper {
        display: flex;
        flex-direction: column;
    }

    .ag-filter-filter {
        flex: 1 1 0px;
    }

    .ag-filter-condition {
        display: flex;
        justify-content: center;
    }

    // /**
    //  ****************************
    //  * Floating Filter
    //  ****************************
    // */

    .ag-floating-filter-body {
        position: relative;
        display: flex;
        flex: 1 1 auto;
        height: 100%;
    }

    .ag-floating-filter-full-body {
        display: flex;
        flex: 1 1 auto;
        height: 100%;
        width: 100%;
        align-items: center;
        overflow: hidden;
    }

    .ag-floating-filter-full-body > div {
        flex: 1 1 auto;
    }

    .ag-floating-filter-input {
        align-items: center;
        display: flex;
        width: 100%;

        > * {
            flex: 1 1 auto;
        }
    }

    .ag-floating-filter-button {
        display: flex;
        flex: none;
    }

    .ag-date-floating-filter-wrapper {
        display: flex;
    }

    .ag-set-floating-filter-input input[disabled] {
        pointer-events: none;
    }

    // /**
    //  ****************************
    //  * Drag & Drop
    //  ****************************
    // */

    .ag-dnd-ghost {
        display: inline-flex;
        align-items: center;
        cursor: move;
        white-space: nowrap;
    }

    // /**
    //  ****************************
    //  * Overlay
    //  ****************************
    // */
    .ag-overlay {
        height: 100%;
        left: 0;
        pointer-events: none;
        position: absolute;
        top: 0;
        width: 100%;
        z-index: 2;
    }

    .ag-overlay-panel {
        display: flex;
        height: 100%;
        width: 100%;
    }

    .ag-overlay-wrapper {
        display: flex;
        flex: none;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .ag-overlay-loading-wrapper {
        // prevent interaction with grid while it's loading
        pointer-events: all;
    }

    // /**
    //  ****************************
    //  * Popup
    //  ****************************
    // */

    .ag-popup-child {
        z-index: 5;
        top: 0;
    }

    .ag-popup-editor {
        position: absolute;
        @include ag.selectable(none);
    }

    .ag-large-text-input {
        display: block;
    }

    // /**
    //  ****************************
    //  * Virtual Lists
    //  ****************************
    // */
    .ag-virtual-list-item {
        position: absolute;
        width: 100%;
    }

    // /**
    //  ****************************
    //  * Floating Top and Bottom
    //  ****************************
    // */
    .ag-floating-top,
    .ag-floating-bottom {
        overflow: hidden;
        white-space: nowrap;
        width: 100%;
        position: relative;
        display: flex;
    }

    .ag-pinned-left-floating-top,
    .ag-pinned-right-floating-top,
    .ag-pinned-left-floating-bottom,
    .ag-pinned-right-floating-bottom {
        overflow: hidden;
        position: relative;
        min-width: 0px;
    }

    // /**
    //  ****************************
    //  * Sticky Top
    //  ****************************
    // */
    .ag-sticky-top,
    .ag-sticky-bottom {
        position: absolute;
        display: flex;
        width: 100%;
        overflow: hidden;
        height: 0px;
    }

    .ag-sticky-bottom {
        box-sizing: content-box !important;
    }

    .ag-pinned-left-sticky-top,
    .ag-pinned-right-sticky-top {
        position: relative;
        height: 100%;
        overflow: hidden;
    }

    .ag-sticky-top-full-width-container,
    .ag-sticky-bottom-full-width-container {
        overflow: hidden;
        width: 100%;
        height: 100%;
    }

    // /**
    //  ****************************
    //  * Dialog
    //  ****************************
    // */
    .ag-dialog,
    .ag-panel {
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
    }

    .ag-panel-title-bar {
        display: flex;
        flex: none;
        align-items: center;
        cursor: default;
    }

    .ag-panel-title-bar-title {
        flex: 1 1 auto;
    }

    .ag-panel-title-bar-buttons {
        display: flex;
    }

    .ag-panel-title-bar-button {
        cursor: pointer;
    }

    .ag-panel-content-wrapper {
        display: flex;
        flex: 1 1 auto;
        position: relative;
        overflow: hidden;
    }

    .ag-dialog {
        position: absolute;
    }

    .ag-resizer {
        position: absolute;
        pointer-events: none;
        z-index: 1;
        @include ag.selectable(none);
        &.ag-resizer-topLeft {
            top: 0;
            left: 0;
            height: 5px;
            width: 5px;
            cursor: nwse-resize;
        }
        &.ag-resizer-top {
            top: 0;
            left: 5px;
            right: 5px;
            height: 5px;
            cursor: ns-resize;
        }
        &.ag-resizer-topRight {
            top: 0;
            right: 0;
            height: 5px;
            width: 5px;
            cursor: nesw-resize;
        }
        &.ag-resizer-right {
            top: 5px;
            right: 0;
            bottom: 5px;
            width: 5px;
            cursor: ew-resize;
        }
        &.ag-resizer-bottomRight {
            bottom: 0;
            right: 0;
            height: 5px;
            width: 5px;
            cursor: nwse-resize;
        }
        &.ag-resizer-bottom {
            bottom: 0;
            left: 5px;
            right: 5px;
            height: 5px;
            cursor: ns-resize;
        }
        &.ag-resizer-bottomLeft {
            bottom: 0;
            left: 0;
            height: 5px;
            width: 5px;
            cursor: nesw-resize;
        }
        &.ag-resizer-left {
            left: 0;
            top: 5px;
            bottom: 5px;
            width: 5px;
            cursor: ew-resize;
        }
    }

    // /**
    //  ****************************
    //  * Tooltip
    //  ****************************
    // */

    .ag-tooltip {
        position: absolute;
        z-index: 99999;
    }

    .ag-tooltip-custom {
        position: absolute;
        z-index: 99999;
    }

    .ag-tooltip:not(.ag-tooltip-interactive),
    .ag-tooltip-custom:not(.ag-tooltip-interactive) {
        pointer-events: none;
    }

    // /**
    //  ****************************
    //  * Animations
    //  ****************************
    // */

    // this is used by the animateShowChangeCellRenderer. it is arguable that this belongs in the themes,
    // however it is not tied to color, only placement and visiblity, which is behaviour and not style,
    // thus belongs here, besides it doesn't change wih the themes
    .ag-value-slide-out {
        margin-right: 5px;
        opacity: 1;
        transition:
            opacity 3s,
            margin-right 3s; // as value fades, it also moves to the left via the margin setting
        transition-timing-function: linear;
    }

    .ag-value-slide-out-end {
        margin-right: 10px;
        opacity: 0;
    }

    .ag-opacity-zero {
        opacity: 0 !important;
    }

    // /**
    //  ****************************
    //  * Menu
    //  ****************************
    // */
    .ag-menu {
        max-height: 100%;
        overflow-y: auto;
        position: absolute;
        @include ag.selectable(none);
    }

    .ag-menu-column-select-wrapper {
        height: 265px;
        overflow: auto;

        .ag-column-select {
            height: 100%;
        }
    }

    .ag-dialog .ag-panel-content-wrapper .ag-column-select {
        user-select: none;
    }

    .ag-menu-list {
        display: table;
        width: 100%;
    }

    .ag-menu-option,
    .ag-menu-separator {
        display: table-row;
    }

    .ag-menu-option-part,
    .ag-menu-separator-part {
        display: table-cell;
        vertical-align: middle;
    }

    .ag-menu-option-text {
        white-space: nowrap;
    }

    .ag-menu-option-custom {
        display: contents;
    }

    .ag-compact-menu-option {
        width: 100%;
        display: flex;
        flex-wrap: nowrap;
    }

    .ag-compact-menu-option-text {
        white-space: nowrap;
        flex: 1 1 auto;
    }

    // /**
    //  ****************************
    //  * Context Menu
    //  ****************************
    // */
    .ag-context-menu-loading-icon {
        position: absolute;
        pointer-events: none;
    }

    // /**
    //  ****************************
    //  * Pill
    //  ****************************
    // */
    .ag-pill-container {
        display: flex;
        gap: 0.25rem;
        flex-wrap: nowrap;
    }

    .ag-pill {
        display: flex;
        white-space: nowrap;
        padding: 0 0.25rem;
        align-items: center;
    }

    .ag-pill .ag-pill-button {
        border: none;
        padding: 0;
    }

    // /**
    //  ****************************
    //  * Rich Select
    //  ****************************
    // */
    .ag-rich-select {
        cursor: default;
        outline: none;
        height: 100%;
    }

    .ag-rich-select-value {
        display: flex;
        align-items: center;
        height: 100%;
        .ag-picker-field-display {
            overflow: hidden;
            text-overflow: ellipsis;
            &.ag-display-as-placeholder {
                opacity: 0.5;
            }
        }
    }

    .ag-rich-select-list {
        position: relative;
        .ag-loading-text {
            min-height: 2rem;
        }
    }

    .ag-rich-select-row {
        display: flex;
        flex: 1 1 auto;
        align-items: center;
        white-space: nowrap;
        overflow: hidden;
        height: 100%;
    }

    .ag-rich-select-field-input {
        flex: 1 1 auto;
        .ag-input-field-input {
            padding: 0 !important;
            border: none !important;
            box-shadow: none !important;
            text-overflow: ellipsis;
            &::placeholder {
                opacity: 0.8;
            }
        }
    }

    // /**
    //  ****************************
    //  * Autocomplete
    //  ****************************
    // */
    .ag-autocomplete {
        align-items: center;
        display: flex;

        > * {
            flex: 1 1 auto;
        }
    }

    .ag-autocomplete-list-popup {
        position: absolute;
        @include ag.selectable(none);
    }

    .ag-autocomplete-list {
        position: relative;
    }

    .ag-autocomplete-virtual-list-item {
        display: flex;
    }

    .ag-autocomplete-row {
        display: flex;
        flex: 1 1 auto;
        align-items: center;
        overflow: hidden;
    }

    .ag-autocomplete-row-label {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    // /**
    //  ****************************
    //  * Pagination
    //  ****************************
    // */
    .ag-paging-panel {
        align-items: center;
        display: flex;
        justify-content: flex-end;
    }

    .ag-paging-page-summary-panel {
        display: flex;
        align-items: center;
    }

    .ag-paging-button {
        position: relative;
    }

    .ag-disabled .ag-paging-page-summary-panel {
        pointer-events: none;
    }

    // /**
    //  ****************************
    //  * Tool Panel
    //  ****************************
    // */
    .ag-tool-panel-wrapper {
        display: flex;
        overflow-y: auto;
        overflow-x: hidden;
        cursor: default;
        @include ag.selectable(none);
    }

    .ag-column-select-column,
    .ag-column-select-column-group,
    .ag-select-agg-func-item {
        position: relative;
        align-items: center;
        display: flex;
        flex-direction: row;
        flex-wrap: nowrap;
        height: 100%;
        > * {
            flex: none;
        }
    }

    .ag-select-agg-func-item,
    .ag-column-select-column-label {
        flex: 1 1 auto;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .ag-column-select-checkbox {
        display: flex;
    }

    .ag-tool-panel-horizontal-resize {
        cursor: ew-resize;
        height: 100%;
        position: absolute;
        top: 0;
        width: 5px;
        z-index: 1;
    }

    .ag-side-bar-left .ag-tool-panel-horizontal-resize {
        @include ag.unthemed-rtl(
            (
                right: -3px,
            )
        );
    }

    .ag-side-bar-right .ag-tool-panel-horizontal-resize {
        @include ag.unthemed-rtl(
            (
                left: -3px,
            )
        );
    }

    .ag-details-row {
        width: 100%;
    }

    .ag-details-row-fixed-height {
        height: 100%;
    }

    .ag-details-grid {
        width: 100%;
    }

    .ag-details-grid-fixed-height {
        height: 100%;
    }

    .ag-header-group-cell {
        display: flex;
        align-items: center;
        height: 100%;
        position: absolute;
        contain: paint;
    }

    .ag-header-group-cell-no-group.ag-header-span-height {
        display: none;
    }

    .ag-cell-label-container {
        display: flex;
        justify-content: space-between;
        flex-direction: row-reverse;
        align-items: center;
        height: 100%;
        width: 100%;
    }

    .ag-header-group-cell-label,
    .ag-cell-label-container {
        padding: 5px 0px;
    }

    .ag-right-aligned-header {
        .ag-cell-label-container {
            flex-direction: row;
        }

        .ag-header-cell-text {
            text-align: end;
        }
    }

    // /**
    //  ****************************
    //  * Side Bar
    //  ****************************
    // */
    .ag-side-bar {
        display: flex;
        flex-direction: row-reverse;
    }

    .ag-side-bar-left {
        order: -1;
        flex-direction: row;
    }

    .ag-side-button-button {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        flex-wrap: nowrap;
        white-space: nowrap;
        outline: none;
        cursor: pointer;
    }

    .ag-side-button-label {
        writing-mode: vertical-lr;
    }

    // /**
    //  ****************************
    //  * Status Bar
    //  ****************************
    // */
    .ag-status-bar {
        display: flex;
        justify-content: space-between;
        overflow: hidden;
    }

    .ag-status-panel,
    .ag-status-panel.ag-status-panel-aggregations .ag-status-name-value {
        display: inline-flex;
    }

    .ag-status-name-value {
        white-space: nowrap;
    }

    .ag-status-bar-left {
        display: inline-flex;
    }

    .ag-status-bar-center {
        display: inline-flex;
    }

    .ag-status-bar-right {
        display: inline-flex;
    }

    // * Row Number Columns
    // *****************************
    // */
    .ag-row-number-cell {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        user-select: none;
        @include ag.unthemed-rtl(
            (
                text-align: right,
            )
        );
    }

    .ag-row-numbers-resizer {
        position: absolute;
        bottom: -2px;
        left: 0;
        height: 4px;
        width: 100%;
        cursor: ns-resize;
    }

    .ag-floating-bottom .ag-row-numbers-resizer {
        bottom: unset;
        top: -2px;
    }

    // /**
    //  ****************************
    //  * Widgets
    //  ****************************
    // */

    .ag-icon {
        display: block;
        speak: none;
    }

    .ag-group {
        position: relative;
        width: 100%;
    }

    .ag-group-title-bar {
        display: flex;
        align-items: center;
    }

    .ag-group-title {
        display: inline;
        min-width: 0;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .ag-group-title-bar .ag-group-title {
        cursor: default;
    }

    .ag-group-toolbar {
        display: flex;
        align-items: center;
    }

    .ag-group-container {
        display: flex;
    }

    .ag-disabled .ag-group-container {
        pointer-events: none;
    }

    .ag-group-container-horizontal {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .ag-group-container-vertical {
        flex-direction: column;
    }

    .ag-column-group-icons {
        display: block;
        > * {
            cursor: pointer;
        }
    }

    .ag-group-item-alignment-stretch .ag-group-item {
        align-items: stretch;
    }

    .ag-group-item-alignment-start .ag-group-item {
        align-items: flex-start;
    }

    .ag-group-item-alignment-end .ag-group-item {
        align-items: flex-end;
    }

    .ag-toggle-button-icon {
        transition: right 0.3s;
        position: absolute;
        top: -1px;
    }

    .ag-input-field,
    .ag-select {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    .ag-input-field-input {
        flex: 1 1 auto;
    }

    .ag-floating-filter-input {
        .ag-input-field-input[type='date'],
        .ag-input-field-input[type='datetime-local'] {
            // Fix a bug in Blink rendering engine where date input will not shrink from its default size in a
            // flex container, but it will grow. So we give it a very small width and it will grow to the right size
            width: 1px;
        }
    }

    .ag-range-field {
        display: flex;
        align-items: center;
    }

    .ag-angle-select {
        display: flex;
        align-items: center;
    }

    .ag-angle-select-wrapper {
        display: flex;
    }

    .ag-angle-select-parent-circle {
        display: block;
        position: relative;
    }
    .ag-angle-select-child-circle {
        position: absolute;
    }

    .ag-slider-wrapper {
        display: flex;
        .ag-input-field {
            flex: 1 1 auto;
        }
    }

    .ag-picker-field-display {
        flex: 1 1 auto;
    }

    .ag-picker-field {
        display: flex;
        align-items: center;
    }

    .ag-picker-field-icon {
        display: flex;
        border: 0;
        padding: 0;
        margin: 0;
        cursor: pointer;
    }

    .ag-picker-field-wrapper {
        overflow: hidden;
    }

    .ag-label-align-right {
        .ag-label {
            order: 1;
        }
        > * {
            flex: none;
        }
    }

    .ag-label-align-top {
        flex-direction: column;
        align-items: flex-start;
        > * {
            align-self: stretch;
        }
    }

    .ag-label-ellipsis {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
    }

    .ag-color-panel {
        width: 100%;
        display: flex;
        flex-direction: column;
        text-align: center;
    }

    .ag-spectrum-color {
        flex: 1 1 auto;
        position: relative;
        overflow: visible;
        cursor: default;
    }

    .ag-spectrum-fill {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .ag-spectrum-val {
        cursor: pointer;
    }

    .ag-spectrum-dragger {
        position: absolute;
        pointer-events: none;
        cursor: pointer;
    }

    .ag-spectrum-hue,
    .ag-spectrum-alpha {
        cursor: default;
    }

    .ag-spectrum-hue-background {
        background: linear-gradient(
            to left,
            #ff0000 3%,
            #ffff00 17%,
            #00ff00 33%,
            #00ffff 50%,
            #0000ff 67%,
            #ff00ff 83%,
            #ff0000 100%
        );
        width: 100%;
        height: 100%;
    }

    .ag-spectrum-alpha {
        --ag-spectrum-alpha-background-checked: url('data:image/svg+xml;utf8,<svg xmlns=%22http://www.w3.org/2000/svg%22 width=%224%22 height=%224%22><rect x=%220%22 y=%220%22 width=%224%22 height=%224%22 fill=%22%23fff%22/><path d=%22M0 0H2V4H4V2H0Z%22 fill=%22%23b2b2b2%22/></svg>');
    }

    .ag-spectrum-alpha-background {
        background:
            linear-gradient(
                to right,
                var(--ag-internal-spectrum-alpha-color-from),
                var(--ag-internal-spectrum-alpha-color-to)
            ),
            var(--ag-spectrum-alpha-background-checked) top left / 4px 4px;
        width: 100%;
        height: 100%;
    }

    .ag-spectrum-tool {
        cursor: pointer;
        position: relative;
    }

    .ag-spectrum-slider {
        position: absolute;
        pointer-events: none;
    }

    .ag-spectrum-alpha .ag-spectrum-slider {
        background: linear-gradient(
                to bottom,
                var(--ag-internal-spectrum-alpha-color),
                var(--ag-internal-spectrum-alpha-color)
            )
            white;
    }

    .ag-recent-colors {
        display: flex;
    }

    .ag-recent-color {
        cursor: pointer;
    }

    .ag-pill-select {
        display: flex;
        flex-direction: column;

        .ag-column-drop {
            flex: unset;
        }
    }

    .ag-ltr {
        direction: ltr;
        .ag-body,
        .ag-floating-top,
        .ag-floating-bottom,
        .ag-header,
        .ag-sticky-top,
        .ag-sticky-bottom,
        .ag-body-viewport,
        .ag-body-horizontal-scroll {
            flex-direction: row;
        }
    }

    .ag-rtl {
        direction: rtl;
        .ag-body,
        .ag-floating-top,
        .ag-floating-bottom,
        .ag-header,
        .ag-sticky-top,
        .ag-sticky-bottom,
        .ag-body-viewport,
        .ag-body-horizontal-scroll {
            flex-direction: row-reverse;
        }

        .ag-icon-contracted,
        .ag-icon-expanded,
        .ag-icon-tree-closed {
            display: block;
            transform: rotate(180deg);
        }
    }

    .ag-body .ag-body-viewport {
        -webkit-overflow-scrolling: touch;
    }

    .ag-measurement-container {
        width: 0;
        overflow: hidden;
        visibility: hidden;

        div {
            position: absolute;
        }
    }
}
