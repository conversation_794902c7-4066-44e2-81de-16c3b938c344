/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_TR = {
    // Set Filter
    selectAll: '(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)',
    selectAllSearchResults: '(Tüm Arama Sonuçlarını Seç)',
    addCurrentSelectionToFilter: 'Mevcut seçimi filtreye ekle',
    searchOoo: 'Ara...',
    blanks: '(<PERSON>ş)',
    noMatches: 'Eşleşme yok',

    // Number Filter & Text Filter
    filterOoo: 'Filtre...',
    equals: 'Eşittir',
    notEqual: 'Eşit değil',
    blank: 'Boş',
    notBlank: 'Boş değil',
    empty: '<PERSON>irini seçin',

    // Number Filter
    lessThan: 'Daha az',
    greaterThan: 'Daha fazla',
    lessThanOrEqual: '<PERSON><PERSON><PERSON>ük veya eşit',
    greaterThanOrEqual: '<PERSON><PERSON><PERSON>ük veya eşit',
    inRange: 'Arasında',
    inRangeStart: 'Başlangıç',
    inRangeEnd: 'Bitiş',

    // Text Filter
    contains: 'İçerir',
    notContains: 'İçermez',
    startsWith: 'İle başlar',
    endsWith: 'İle biter',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Önce',
    after: 'Sonra',

    // Filter Conditions
    andCondition: 'VE',
    orCondition: 'VEYA',

    // Filter Buttons
    applyFilter: 'Uygula',
    resetFilter: 'Sıfırla',
    clearFilter: 'Temizle',
    cancelFilter: 'İptal',

    // Filter Titles
    textFilter: 'Metin Filtresi',
    numberFilter: 'Sayı Filtresi',
    dateFilter: 'Tarih Filtresi',
    setFilter: 'Ayarlama Filtresi',

    // Group Column Filter
    groupFilterSelect: 'Alan seçiniz:',

    // New Filter Tool Panel
    filterSummaryInactive: '(Tümü)',
    filterSummaryContains: 'içerir',
    filterSummaryNotContains: 'içermez',
    filterSummaryTextEquals: 'eşittir',
    filterSummaryTextNotEqual: 'eşit değildir',
    filterSummaryStartsWith: 'ile başlar',
    filterSummaryEndsWith: 'ile biter',
    filterSummaryBlank: 'boş',
    filterSummaryNotBlank: 'boş değil',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'arasında',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: '(Tümü)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: '(${variable})',
    filterSummaryListLong: '(${variable}) ve ${variable} daha',
    addFilterCard: 'Filtre Ekle',
    agTextColumnFilterDisplayName: 'Basit Filtre',
    agNumberColumnFilterDisplayName: 'Basit Filtre',
    agDateColumnFilterDisplayName: 'Basit Filtre',
    agSetColumnFilterDisplayName: 'Seçim Filtresi',
    agMultiColumnFilterDisplayName: 'Karma Filtre',
    addFilterPlaceholder: 'Kolonlarda ara...',

    // Advanced Filter
    advancedFilterContains: 'içerir',
    advancedFilterNotContains: 'içermez',
    advancedFilterTextEquals: 'eşittir',
    advancedFilterTextNotEqual: 'eşit-değildir',
    advancedFilterStartsWith: 'ile-başlar',
    advancedFilterEndsWith: 'ile-biter',
    advancedFilterBlank: 'boş',
    advancedFilterNotBlank: 'boş-değil',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'doğru',
    advancedFilterFalse: 'yanlış',
    advancedFilterAnd: 'VE',
    advancedFilterOr: 'VEYA',
    advancedFilterApply: 'Uygula',
    advancedFilterBuilder: 'Oluşturucu',
    advancedFilterValidationMissingColumn: 'Sütun eksik',
    advancedFilterValidationMissingOption: 'Seçenek eksik',
    advancedFilterValidationMissingValue: 'Değer eksik',
    advancedFilterValidationInvalidColumn: 'Sütun bulunamadı',
    advancedFilterValidationInvalidOption: 'Seçenek bulunamadı',
    advancedFilterValidationMissingQuote: 'Değerin kapanış tırnağı eksik',
    advancedFilterValidationNotANumber: 'Değer bir sayı değil',
    advancedFilterValidationInvalidDate: 'Değer geçerli bir tarih değil',
    advancedFilterValidationMissingCondition: 'Koşul eksik',
    advancedFilterValidationJoinOperatorMismatch: 'Bir koşul içindeki birleştirici operatörlerin aynı olması gerekir',
    advancedFilterValidationInvalidJoinOperator: 'Birleştirici operatör bulunamadı',
    advancedFilterValidationMissingEndBracket: 'Kapanış parantezi eksik',
    advancedFilterValidationExtraEndBracket: 'Fazla kapanış parantezi',
    advancedFilterValidationMessage: 'İfade bir hata içeriyor. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'İfade bir hata içeriyor. ${variable} ifadenin sonunda.',
    advancedFilterBuilderTitle: 'Gelişmiş Filtre',
    advancedFilterBuilderApply: 'Uygula',
    advancedFilterBuilderCancel: 'İptal',
    advancedFilterBuilderAddButtonTooltip: 'Filtre veya Grup Ekle',
    advancedFilterBuilderRemoveButtonTooltip: 'Kaldır',
    advancedFilterBuilderMoveUpButtonTooltip: 'Yukarı Taşı',
    advancedFilterBuilderMoveDownButtonTooltip: 'Aşağı Taşı',
    advancedFilterBuilderAddJoin: 'Grup Ekle',
    advancedFilterBuilderAddCondition: 'Filtre Ekle',
    advancedFilterBuilderSelectColumn: 'Bir sütun seçin',
    advancedFilterBuilderSelectOption: 'Bir seçenek seçin',
    advancedFilterBuilderEnterValue: 'Bir değer girin...',
    advancedFilterBuilderValidationAlreadyApplied: 'Mevcut filtre zaten uygulanmış.',
    advancedFilterBuilderValidationIncomplete: 'Tüm koşullar tamamlanmamış.',
    advancedFilterBuilderValidationSelectColumn: 'Bir sütun seçmelisiniz.',
    advancedFilterBuilderValidationSelectOption: 'Bir seçenek seçmelisiniz.',
    advancedFilterBuilderValidationEnterValue: 'Bir değer girmelisiniz.',

    // Editor Validation Errors
    minDateValidation: 'Tarih ${variable} tarihinden sonra olmalıdır',
    maxDateValidation: 'Tarih ${variable} tarihinden önce olmalıdır',
    maxLengthValidation: 'En fazla ${variable} karakter olmalıdır.',
    minValueValidation: 'En az ${variable} veya daha büyük olmalıdır',
    maxValueValidation: 'En fazla ${variable} veya daha küçük olmalıdır ',
    invalidSelectionValidation: 'Geçersiz seçim.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Sütunlar',
    filters: 'Filtreler',

    // columns tool panel
    pivotMode: 'Pivot Modu',
    groups: 'Satır Grupları',
    rowGroupColumnsEmptyMessage: 'Satır gruplarını ayarlamak için buraya sürükleyin',
    values: 'Değerler',
    valueColumnsEmptyMessage: 'Toplamak için buraya sürükleyin',
    pivots: 'Sütun Etiketleri',
    pivotColumnsEmptyMessage: 'Sütun etiketlerini ayarlamak için buraya sürükleyin',

    // Header of the Default Group Column
    group: 'Grup',

    // Row Drag
    rowDragRow: 'satır',
    rowDragRows: 'satırlar',

    // Other
    loadingOoo: 'Yükleniyor...',
    loadingError: 'HATA',
    noRowsToShow: 'Gösterilecek Satır Yok',
    enabled: 'Etkin',

    // Menu
    pinColumn: 'Sütunu Sabitle',
    pinLeft: 'Sola Sabitle',
    pinRight: 'Sağa Sabitle',
    noPin: 'Sabitleme Yok',
    valueAggregation: 'Değer Toplama',
    noAggregation: 'Hiçbiri',
    autosizeThisColumn: 'Bu Sütunu Otomatik Boyutlandır',
    autosizeAllColumns: 'Tüm Sütunları Otomatik Boyutlandır',
    groupBy: 'Grupla',
    ungroupBy: 'Grubu Çıkar',
    ungroupAll: 'Tüm Grupları Çıkar',
    addToValues: '${variable} değerlerine ekle',
    removeFromValues: '${variable} değerlerinden çıkar',
    addToLabels: '${variable} etiketlerine ekle',
    removeFromLabels: '${variable} etiketlerinden çıkar',
    resetColumns: 'Sütunları Sıfırla',
    expandAll: 'Tüm Satır Gruplarını Genişlet',
    collapseAll: 'Tüm Satır Gruplarını Daralt',
    copy: 'Kopyala',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Başlıklarla Kopyala',
    copyWithGroupHeaders: 'Grup Başlıklarıyla Kopyala',
    cut: 'Kes',
    paste: 'Yapıştır',
    ctrlV: 'Ctrl+V',
    export: 'Dışa Aktar',
    csvExport: 'CSV Olarak Dışa Aktar',
    excelExport: 'Excel Olarak Dışa Aktar',
    columnFilter: 'Sütun Filtresi',
    columnChooser: 'Sütunları Seç',
    chooseColumns: 'Sütunları Seç',
    sortAscending: 'Artan Sıralı',
    sortDescending: 'Azalan Sıralı',
    sortUnSort: 'Sıralamayı Temizle',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Toplam',
    first: 'İlk',
    last: 'Son',
    min: 'Min',
    max: 'Maks',
    none: 'Yok',
    count: 'Sayım',
    avg: 'Ortalama',
    filteredRows: 'Filtrelenmiş',
    selectedRows: 'Seçili',
    totalRows: 'Toplam Satırlar',
    totalAndFilteredRows: 'Satırlar',
    more: 'Daha fazla',
    to: 'ile',
    of: 'üzerinden',
    page: 'Sayfa',
    pageLastRowUnknown: '?',
    nextPage: 'Sonraki Sayfa',
    lastPage: 'Son Sayfa',
    firstPage: 'İlk Sayfa',
    previousPage: 'Önceki Sayfa',
    pageSizeSelectorLabel: 'Sayfa Boyutu:',
    footerTotal: 'Toplam',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: '${variable} görünümü için kaydır',

    // Pivoting
    pivotColumnGroupTotals: 'Toplam',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivot Grafiği ve Pivot Modu',
    pivotChart: 'Pivot Grafiği',
    chartRange: 'Grafik Aralığı',
    columnChart: 'Sütun',
    groupedColumn: 'Gruplanmış',
    stackedColumn: 'Yığılmış',
    normalizedColumn: '%100 Yığılmış',
    barChart: 'Çubuk',
    groupedBar: 'Gruplanmış',
    stackedBar: 'Yığılmış',
    normalizedBar: '%100 Yığılmış',
    pieChart: 'Pasta',
    pie: 'Pasta',
    donut: 'Halka',
    lineChart: 'Çizgi',
    stackedLine: 'Yığın',
    normalizedLine: '100% Yığın',
    xyChart: 'X Y (Serpme)',
    scatter: 'Serpme',
    bubble: 'Baloncuk',
    areaChart: 'Alan',
    area: 'Alan',
    stackedArea: 'Yığılmış',
    normalizedArea: '%100 Yığılmış',
    histogramChart: 'Histogram',
    polarChart: 'Polar',
    radarLine: 'Radar Çizgisi',
    radarArea: 'Radar Alanı',
    nightingale: 'Nightingale',
    radialColumn: 'Radyal Sütun',
    radialBar: 'Radyal Çubuk',
    statisticalChart: 'İstatistiksel',
    boxPlot: 'Kutu Grafiği',
    rangeBar: 'Aralık Çubuğu',
    rangeArea: 'Aralık Alanı',
    hierarchicalChart: 'Hiyerarşik',
    treemap: 'Ağaç Haritası',
    sunburst: 'Güneşışığı',
    specializedChart: 'Özel',
    waterfall: 'Şelale',
    heatmap: 'Isı Haritası',
    combinationChart: 'Kombinasyon',
    columnLineCombo: 'Sütun ve Çizgi',
    AreaColumnCombo: 'Alan ve Sütun',

    // Charts
    pivotChartTitle: 'Özet Grafik',
    rangeChartTitle: 'Aralık Grafiği',
    settings: 'Grafik',
    data: 'Kurulum',
    format: 'Özelleştir',
    categories: 'Kategoriler',
    defaultCategory: '(Hiçbiri)',
    series: 'Seriler',
    switchCategorySeries: 'Kategori/Seri Değiştir',
    categoryValues: 'Kategori Değerleri',
    seriesLabels: 'Seri Etiketleri',
    aggregate: 'Topla',
    xyValues: 'X Y Değerleri',
    paired: 'Eşleştirilmiş Mod',
    axis: 'Eksen',
    xAxis: 'Yatay Eksen',
    yAxis: 'Dikey Eksen',
    polarAxis: 'Kutup Ekseni',
    radiusAxis: 'Yarıçap Ekseni',
    navigator: 'Gezgin',
    zoom: 'Yakınlaştır',
    animation: 'Animasyon',
    crosshair: 'Kesişme Noktası',
    color: 'Renk',
    thickness: 'Kalınlık',
    preferredLength: 'Tercih Edilen Uzunluk',
    xType: 'X Tipi',
    axisType: 'Eksen Tipi',
    automatic: 'Otomatik',
    category: 'Kategori',
    number: 'Sayı',
    time: 'Zaman',
    timeFormat: 'Zaman Formatı',
    autoRotate: 'Otomatik Döndür',
    labelRotation: 'Döndürme',
    circle: 'Daire',
    polygon: 'Çokgen',
    square: 'Kare',
    cross: 'Çarpı',
    diamond: 'Elmas',
    plus: 'Artı',
    triangle: 'Üçgen',
    heart: 'Kalp',
    orientation: 'Yön',
    fixed: 'Sabit',
    parallel: 'Paralel',
    perpendicular: 'Dik',
    radiusAxisPosition: 'Pozisyon',
    ticks: 'Kene',
    gridLines: 'Izgara Çizgileri',
    width: 'Genişlik',
    height: 'Yükseklik',
    length: 'Uzunluk',
    padding: 'Dolgu',
    spacing: 'Aralık',
    chartStyle: 'Grafik Stili',
    title: 'Başlık',
    chartTitles: 'Başlıklar',
    chartTitle: 'Grafik Başlığı',
    chartSubtitle: 'Alt Başlık',
    horizontalAxisTitle: 'Yatay Eksen Başlığı',
    verticalAxisTitle: 'Dikey Eksen Başlığı',
    polarAxisTitle: 'Kutup Ekseni Başlığı',
    titlePlaceholder: 'Grafik Başlığı',
    background: 'Arka Plan',
    font: 'Yazı Tipi',
    weight: 'Ağırlık',
    top: 'Üst',
    right: 'Sağ',
    bottom: 'Alt',
    left: 'Sol',
    labels: 'Etiketler',
    calloutLabels: 'Çağrı Etiketleri',
    sectorLabels: 'Sektör Etiketleri',
    positionRatio: 'Pozisyon Oranı',
    size: 'Boyut',
    shape: 'Şekil',
    minSize: 'Minimum Boyut',
    maxSize: 'Maksimum Boyut',
    legend: 'Açıklama',
    position: 'Pozisyon',
    markerSize: 'İşaretçi Boyutu',
    markerStroke: 'İşaretçi Çizgisi',
    markerPadding: 'İşaretçi Dolgusu',
    itemSpacing: 'Öğe Aralığı',
    itemPaddingX: 'Öğe Yatay Doldurma',
    itemPaddingY: 'Öğe Dikey Doldurma',
    layoutHorizontalSpacing: 'Yatay Aralık',
    layoutVerticalSpacing: 'Dikey Aralık',
    strokeWidth: 'Çizgi Kalınlığı',
    offset: 'Sapma',
    offsets: 'Sapmalar',
    tooltips: 'Araç İpuçları',
    callout: 'Çağrı',
    markers: 'İşaretçiler',
    shadow: 'Gölge',
    blur: 'Bulanıklık',
    xOffset: 'X Sapma',
    yOffset: 'Y Sapma',
    lineWidth: 'Çizgi Genişliği',
    lineDash: 'Çizgi Kesikli',
    lineDashOffset: 'Kesik Sapma',
    scrollingZoom: 'Kaydırma',
    scrollingStep: 'Kaydırma Adımı',
    selectingZoom: 'Seçim',
    durationMillis: 'Süre (ms)',
    crosshairLabel: 'Etiket',
    crosshairSnap: 'Düğüm Noktasına Yaklaş',
    normal: 'Normal',
    bold: 'Kalın',
    italic: 'İtalik',
    boldItalic: 'Kalın İtalik',
    predefined: 'Önceden Tanımlanmış',
    fillOpacity: 'Doldurma Opaklığı',
    strokeColor: 'Çizgi Rengi',
    strokeOpacity: 'Çizgi Opaklığı',
    miniChart: 'Mini Grafik',
    histogramBinCount: 'Bin sayısı',
    connectorLine: 'Bağlayıcı Çizgi',
    seriesItems: 'Seri Öğeleri',
    seriesItemType: 'Öğe Tipi',
    seriesItemPositive: 'Pozitif',
    seriesItemNegative: 'Negatif',
    seriesItemLabels: 'Öğe Etiketleri',
    columnGroup: 'Sütun',
    barGroup: 'Çubuk',
    pieGroup: 'Pasta',
    lineGroup: 'Çizgi',
    scatterGroup: 'X Y (Dağılım)',
    areaGroup: 'Alan',
    polarGroup: 'Kutup',
    statisticalGroup: 'İstatistiksel',
    hierarchicalGroup: 'Hiyerarşik',
    specializedGroup: 'Uzmanlaşmış',
    combinationGroup: 'Kombinasyon',
    groupedColumnTooltip: 'Gruplandırılmış',
    stackedColumnTooltip: 'Yığılmış',
    normalizedColumnTooltip: '100% Yığılmış',
    groupedBarTooltip: 'Gruplandırılmış',
    stackedBarTooltip: 'Yığılmış',
    normalizedBarTooltip: '100% Yığılmış',
    pieTooltip: 'Pasta',
    donutTooltip: 'Donut',
    lineTooltip: 'Çizgi',
    stackedLineTooltip: 'Yığılmış',
    normalizedLineTooltip: '%100 Yığılmış',
    groupedAreaTooltip: 'Alan',
    stackedAreaTooltip: 'Yığılmış',
    normalizedAreaTooltip: '100% Yığılmış',
    scatterTooltip: 'Dağılım',
    bubbleTooltip: 'Balon',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Radyal Sütun',
    radialBarTooltip: 'Radyal Çubuk',
    radarLineTooltip: 'Radar Çizgisi',
    radarAreaTooltip: 'Radar Alanı',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Aralık Çubuğu',
    rangeAreaTooltip: 'Aralık Alanı',
    boxPlotTooltip: 'Kutu Grafiği',
    treemapTooltip: 'Ağaç Haritası',
    sunburstTooltip: 'Patlama',
    waterfallTooltip: 'Şelale',
    heatmapTooltip: 'Isı Haritası',
    columnLineComboTooltip: 'Sütun & Çizgi',
    areaColumnComboTooltip: 'Alan & Sütun',
    customComboTooltip: 'Özel Kombinasyon',
    innerRadius: 'İç Yarıçap',
    startAngle: 'Başlangıç Açısı',
    endAngle: 'Bitiş Açısı',
    reverseDirection: 'Yönü Ters Çevir',
    groupPadding: 'Grup Doldurma',
    seriesPadding: 'Seri Doldurma',
    tile: 'Döşeme',
    whisker: 'Bıyık',
    cap: 'Şapka',
    capLengthRatio: 'Uzunluk Oranı',
    labelPlacement: 'Etiket Yerleşimi',
    inside: 'İçinde',
    outside: 'Dışında',
    noDataToChart: 'Grafik için veri yok.',
    pivotChartRequiresPivotMode: 'Özet Grafik, Pivot Modun etkin olmasını gerektirir.',
    chartSettingsToolbarTooltip: 'Menü',
    chartLinkToolbarTooltip: 'Grida Bağlı',
    chartUnlinkToolbarTooltip: 'Gridden Ayrık',
    chartDownloadToolbarTooltip: 'Grafiği İndir',
    chartMenuToolbarTooltip: 'Menü',
    chartEdit: 'Grafiği Düzenle',
    chartAdvancedSettings: 'Gelişmiş Ayarlar',
    chartLink: 'Grida Bağla',
    chartUnlink: 'Gridden Ayır',
    chartDownload: 'Grafiği İndir',
    histogramFrequency: 'Frekans',
    seriesChartType: 'Seri Grafik Tipi',
    seriesType: 'Seri Tipi',
    secondaryAxis: 'İkincil Eksen',
    seriesAdd: 'Bir seri ekle',
    categoryAdd: 'Bir kategori ekle',
    bar: 'Çubuk',
    column: 'Sütun',
    histogram: 'Histogram',
    advancedSettings: 'Gelişmiş Ayarlar',
    direction: 'Yön',
    horizontal: 'Yatay',
    vertical: 'Dikey',
    seriesGroupType: 'Grup Tipi',
    groupedSeriesGroupType: 'Gruplandırılmış',
    stackedSeriesGroupType: 'Yığılmış',
    normalizedSeriesGroupType: '100% Yığılmış',
    legendEnabled: 'Etkin',
    invalidColor: 'Renk değeri geçersiz',
    groupedColumnFull: 'Gruplandırılmış Sütun',
    stackedColumnFull: 'Yığılmış Sütun',
    normalizedColumnFull: '100% Yığılmış Sütun',
    groupedBarFull: 'Gruplandırılmış Çubuk',
    stackedBarFull: 'Yığılmış Çubuk',
    normalizedBarFull: '100% Yığılmış Çubuk',
    stackedAreaFull: 'Yığılmış Alan',
    normalizedAreaFull: '100% Yığılmış Alan',
    customCombo: 'Özel Kombinasyon',
    funnel: 'Huni',
    coneFunnel: 'Koni Huni',
    pyramid: 'Piramit',
    funnelGroup: 'Huni',
    funnelTooltip: 'Huni',
    coneFunnelTooltip: 'Koni Huni',
    pyramidTooltip: 'Piramit',
    dropOff: 'Bırakma',
    stageLabels: 'Aşama Etiketleri',
    reverse: 'Ters Çevir',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Seviye ${variable}. Düzenlemek için ENTER tuşuna basın',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Seviye ${variable}. ${variable} Düzenlemek için ENTER tuşuna basın.',
    ariaAdvancedFilterBuilderList: 'Gelişmiş Filtre Oluşturucu Listesi',
    ariaAdvancedFilterBuilderFilterItem: 'Filtre Koşulu',
    ariaAdvancedFilterBuilderGroupItem: 'Filtre Grubu',
    ariaAdvancedFilterBuilderColumn: 'Sütun',
    ariaAdvancedFilterBuilderOption: 'Seçenek',
    ariaAdvancedFilterBuilderValueP: 'Değer',
    ariaAdvancedFilterBuilderJoinOperator: 'Bağlantı Operatörü',
    ariaAdvancedFilterInput: 'Gelişmiş Filtre Girişi',
    ariaChecked: 'işaretli',
    ariaColumn: 'Sütun',
    ariaColumnGroup: 'Sütun Grubu',
    ariaColumnFiltered: 'Sütun Filtrelendi',
    ariaColumnSelectAll: 'Tüm Sütunların Görünürlüğünü Değiştir',
    ariaDateFilterInput: 'Tarih Filtre Girişi',
    ariaDefaultListName: 'Liste',
    ariaFilterColumnsInput: 'Sütunları Filtrele Girişi',
    ariaFilterFromValue: 'Değerden filtrele',
    ariaFilterInput: 'Filtre Girişi',
    ariaFilterList: 'Filtre Listesi',
    ariaFilterToValue: 'Değere filtrele',
    ariaFilterValue: 'Filtre Değeri',
    ariaFilterMenuOpen: 'Filtre Menüsünü Aç',
    ariaFilteringOperator: 'Filtreleme Operatörü',
    ariaHidden: 'gizli',
    ariaIndeterminate: 'belirsiz',
    ariaInputEditor: 'Giriş Düzenleyicisi',
    ariaMenuColumn: 'Sütun menüsünü açmak için ALT AŞAĞI tuşlarına basın',
    ariaFilterColumn: 'Filtreyi açmak için CTRL ENTER tuşlarına basın',
    ariaRowDeselect: 'Bu satırın seçimini kaldırmak için SPACE tuşuna basın',
    ariaHeaderSelection: 'Başlık Seçimi Olan Sütun',
    ariaSelectAllCells: 'Tüm hücreleri seçmek için Boşluk tuşuna basın',
    ariaRowSelectAll: 'Tüm satırların seçimini değiştirmek için Boşluk tuşuna basın',
    ariaRowToggleSelection: 'Satır seçimini değiştirmek için Boşluk tuşuna basın',
    ariaRowSelect: 'Bu satırı seçmek için SPACE tuşuna basın',
    ariaRowSelectionDisabled: 'Bu satır için Satır Seçimi devre dışı bırakılmıştır',
    ariaSearch: 'Ara',
    ariaSortableColumn: 'Sıralamak için ENTER tuşuna basın',
    ariaToggleVisibility: 'Görünürlüğü değiştirmek için SPACE tuşuna basın',
    ariaToggleCellValue: 'Hücre değerini değiştirmek için SPACE tuşuna basın',
    ariaUnchecked: 'işaretli değil',
    ariaVisible: 'görünür',
    ariaSearchFilterValues: 'Filtre değerlerini ara',
    ariaPageSizeSelectorLabel: 'Sayfa Boyutu',
    ariaChartMenuClose: 'Grafik Düzenleme Menüsünü Kapat',
    ariaChartSelected: 'Seçildi',
    ariaSkeletonCellLoadingFailed: 'Satır yüklenemedi',
    ariaSkeletonCellLoading: 'Satır verisi yükleniyor',
    ariaDeferSkeletonCellLoading: 'Hücre yükleniyor',

    // ARIA for Batch Edit
    ariaPendingChange: 'Bekleyen değişiklik',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Satır Grupları',
    ariaValuesDropZonePanelLabel: 'Değerler',
    ariaPivotDropZonePanelLabel: 'Sütun Etiketleri',
    ariaDropZoneColumnComponentDescription: 'Kaldırmak için DELETE tuşuna basın',
    ariaDropZoneColumnValueItemDescription: 'Toplama türünü değiştirmek için ENTER tuşuna basın',
    ariaDropZoneColumnGroupItemDescription: 'Sıralamak için ENTER tuşuna basın',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' of ',
    ariaDropZoneColumnComponentSortAscending: 'artan',
    ariaDropZoneColumnComponentSortDescending: 'azalan',
    ariaLabelDialog: 'Diyalog',
    ariaLabelColumnMenu: 'Sütun Menüsü',
    ariaLabelColumnFilter: 'Sütun Filtresi',
    ariaLabelSelectField: 'Alan Seç',

    // Cell Editor
    ariaLabelCellEditor: 'Hücre Düzenleyici',
    ariaValidationErrorPrefix: 'Hücre Düzenleyici Doğrulaması',
    ariaLabelLoadingContextMenu: 'Bağlam Menüsü Yükleniyor',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Zengin Seçim Alanı',
    ariaLabelRichSelectToggleSelection: 'Seçimi değiştirmek için BOŞLUK tuşuna basın',
    ariaLabelRichSelectDeselectAllItems: 'Tüm öğelerin seçimini kaldırmak için SİL tuşuna basın',
    ariaLabelRichSelectDeleteSelection: 'Öğenin seçimini kaldırmak için SİL tuşuna basın',
    ariaLabelTooltip: 'İpucu',
    ariaLabelContextMenu: 'Bağlam Menüsü',
    ariaLabelSubMenu: 'Alt Menü',
    ariaLabelAggregationFunction: 'Toplama Fonksiyonu',
    ariaLabelAdvancedFilterAutocomplete: 'Gelişmiş Filtre Otomatik Tamamlama',
    ariaLabelAdvancedFilterBuilderAddField: 'Gelişmiş Filtre Oluşturucu Alan Ekle',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Gelişmiş Filtre Oluşturucu Sütun Seçim Alanı',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Gelişmiş Filtre Oluşturucu Seçenek Seçim Alanı',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Gelişmiş Filtre Oluşturucu Birleştirme Operatörü Seçim Alanı',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Kolon Listesi',
    ariaFilterPanelList: 'Filtre Listesi',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Filtre Alanı Ekle',
    ariaLabelFilterCardDelete: 'Filtreyi Sil',
    ariaLabelFilterCardHasEdits: 'Düzenlemeler Var',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Doğru',
    false: 'Yanlış',
    invalidDate: 'Geçersiz Tarih',
    invalidNumber: 'Geçersiz Sayı',
    january: 'Ocak',
    february: 'Şubat',
    march: 'Mart',
    april: 'Nisan',
    may: 'Mayıs',
    june: 'Haziran',
    july: 'Temmuz',
    august: 'Ağustos',
    september: 'Eylül',
    october: 'Ekim',
    november: 'Kasım',
    december: 'Aralık',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'GG/AA/YYYY',
    timeFormatSlashesMMDDYYYY: 'AA/GG/YYYY',
    timeFormatSlashesDDMMYY: 'GG/AA/YY',
    timeFormatSlashesMMDDYY: 'AA/GG/YY',
    timeFormatDotsDDMYY: 'GG.A.YY',
    timeFormatDotsMDDYY: 'A.GG.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-AA-GG',
    timeFormatSpacesDDMMMMYYYY: 'GG AAAA YYYY',
    timeFormatHHMMSS: 'SS:DD:SS',
    timeFormatHHMMSSAmPm: 'SS:DD:SS ÖÖ/ÖS',
};
