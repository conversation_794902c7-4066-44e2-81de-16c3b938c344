/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_IT = {
    // Set Filter
    selectAll: '(Seleziona tutto)',
    selectAllSearchResults: '(Seleziona tutti i risultati della ricerca)',
    addCurrentSelectionToFilter: 'Aggiungi selezione corrente al filtro',
    searchOoo: 'Cerca...',
    blanks: '(<PERSON><PERSON><PERSON>)',
    noMatches: 'Nessuna corrispondenza',

    // Number Filter & Text Filter
    filterOoo: 'Filtra...',
    equals: 'Uguale',
    notEqual: 'Diverso',
    blank: 'Vuoto',
    notBlank: 'Non vuoto',
    empty: 'Scegli uno',

    // Number Filter
    lessThan: 'Meno di',
    greaterThan: 'Più di',
    lessThanOrEqual: 'Minore o uguale a',
    greaterThanOrEqual: 'Maggio<PERSON> o uguale a',
    inRange: 'Tra',
    inRangeStart: 'Da',
    inRangeEnd: 'A',

    // Text Filter
    contains: 'Contiene',
    notContains: 'Non contiene',
    startsWith: 'Inizia con',
    endsWith: 'Finisce con',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Prima',
    after: 'Dopo',

    // Filter Conditions
    andCondition: 'E',
    orCondition: 'O',

    // Filter Buttons
    applyFilter: 'Applica',
    resetFilter: 'Reimposta',
    clearFilter: 'Pulisci',
    cancelFilter: 'Annulla',

    // Filter Titles
    textFilter: 'Filtro di testo',
    numberFilter: 'Filtro numerico',
    dateFilter: 'Filtro data',
    setFilter: 'Filtro set',

    // Group Column Filter
    groupFilterSelect: 'Seleziona campo:',

    // New Filter Tool Panel
    filterSummaryInactive: 'è (Tutti)',
    filterSummaryContains: 'contiene',
    filterSummaryNotContains: 'non contiene',
    filterSummaryTextEquals: 'è uguale a',
    filterSummaryTextNotEqual: 'non è uguale a',
    filterSummaryStartsWith: 'inizia con',
    filterSummaryEndsWith: 'finisce con',
    filterSummaryBlank: 'è vuoto',
    filterSummaryNotBlank: 'non è vuoto',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'tra',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'è (Tutti)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'è (${variable})',
    filterSummaryListLong: 'è (${variable}) e ${variable} in più',
    addFilterCard: 'Aggiungi Filtro',
    agTextColumnFilterDisplayName: 'Filtro Semplice',
    agNumberColumnFilterDisplayName: 'Filtro Semplice',
    agDateColumnFilterDisplayName: 'Filtro Semplice',
    agSetColumnFilterDisplayName: 'Filtro di Selezione',
    agMultiColumnFilterDisplayName: 'Filtro Combinato',
    addFilterPlaceholder: 'Cerca colonne...',

    // Advanced Filter
    advancedFilterContains: 'contiene',
    advancedFilterNotContains: 'non contiene',
    advancedFilterTextEquals: 'è uguale a',
    advancedFilterTextNotEqual: 'non è uguale a',
    advancedFilterStartsWith: 'inizia con',
    advancedFilterEndsWith: 'termina con',
    advancedFilterBlank: 'è vuoto',
    advancedFilterNotBlank: 'non è vuoto',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'è vero',
    advancedFilterFalse: 'è falso',
    advancedFilterAnd: 'E',
    advancedFilterOr: 'O',
    advancedFilterApply: 'Applica',
    advancedFilterBuilder: 'Costruttore',
    advancedFilterValidationMissingColumn: 'Colonna mancante',
    advancedFilterValidationMissingOption: 'Opzione mancante',
    advancedFilterValidationMissingValue: 'Valore mancante',
    advancedFilterValidationInvalidColumn: 'Colonna non trovata',
    advancedFilterValidationInvalidOption: 'Opzione non trovata',
    advancedFilterValidationMissingQuote: 'Il valore manca di una virgolette finale',
    advancedFilterValidationNotANumber: 'Il valore non è un numero',
    advancedFilterValidationInvalidDate: 'Il valore non è una data valida',
    advancedFilterValidationMissingCondition: 'Condizione mancante',
    advancedFilterValidationJoinOperatorMismatch:
        "Gli operatori di unione all'interno di una condizione devono essere uguali",
    advancedFilterValidationInvalidJoinOperator: 'Operatore di unione non trovato',
    advancedFilterValidationMissingEndBracket: 'Parentesi di chiusura mancante',
    advancedFilterValidationExtraEndBracket: 'Troppe parentesi di chiusura',
    advancedFilterValidationMessage: "L'espressione ha un errore. ${variable} - ${variable}.",
    advancedFilterValidationMessageAtEnd: "L'espressione ha un errore. ${variable} alla fine dell'espressione.",
    advancedFilterBuilderTitle: 'Filtro Avanzato',
    advancedFilterBuilderApply: 'Applica',
    advancedFilterBuilderCancel: 'Annulla',
    advancedFilterBuilderAddButtonTooltip: 'Aggiungi Filtro o Gruppo',
    advancedFilterBuilderRemoveButtonTooltip: 'Rimuovi',
    advancedFilterBuilderMoveUpButtonTooltip: 'Sposta Su',
    advancedFilterBuilderMoveDownButtonTooltip: 'Sposta Giù',
    advancedFilterBuilderAddJoin: 'Aggiungi Gruppo',
    advancedFilterBuilderAddCondition: 'Aggiungi Filtro',
    advancedFilterBuilderSelectColumn: 'Seleziona una colonna',
    advancedFilterBuilderSelectOption: "Seleziona un'opzione",
    advancedFilterBuilderEnterValue: 'Inserisci un valore...',
    advancedFilterBuilderValidationAlreadyApplied: 'Filtro corrente già applicato.',
    advancedFilterBuilderValidationIncomplete: 'Non tutte le condizioni sono complete.',
    advancedFilterBuilderValidationSelectColumn: 'Devi selezionare una colonna.',
    advancedFilterBuilderValidationSelectOption: "Devi selezionare un'opzione.",
    advancedFilterBuilderValidationEnterValue: 'Devi inserire un valore.',

    // Editor Validation Errors
    minDateValidation: 'La data deve essere successiva a ${variable}',
    maxDateValidation: 'La data deve essere precedente a ${variable}',
    maxLengthValidation: 'Deve essere di ${variable} caratteri o meno.',
    minValueValidation: 'Deve essere maggiore o uguale a ${variable}',
    maxValueValidation: 'Deve essere minore o uguale a ${variable}',
    invalidSelectionValidation: 'Selezione non valida.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Colonne',
    filters: 'Filtri',

    // columns tool panel
    pivotMode: 'Modalità pivot',
    groups: 'Raggruppamenti di righe',
    rowGroupColumnsEmptyMessage: 'Trascina qui per impostare i gruppi di righe',
    values: 'Valori',
    valueColumnsEmptyMessage: 'Trascina qui per aggregare',
    pivots: 'Etichette di colonna',
    pivotColumnsEmptyMessage: 'Trascina qui per impostare le etichette di colonna',

    // Header of the Default Group Column
    group: 'Gruppo',

    // Row Drag
    rowDragRow: 'riga',
    rowDragRows: 'righe',

    // Other
    loadingOoo: 'Caricamento...',
    loadingError: 'ERR',
    noRowsToShow: 'Nessuna riga da mostrare',
    enabled: 'Abilitato',

    // Menu
    pinColumn: 'Blocca Colonna',
    pinLeft: 'Blocca a Sinistra',
    pinRight: 'Blocca a Destra',
    noPin: 'Non Bloccare',
    valueAggregation: 'Aggregazione Valori',
    noAggregation: 'Nessuna',
    autosizeThisColumn: 'Dimensiona Automaticamente Questa Colonna',
    autosizeAllColumns: 'Dimensiona Automaticamente Tutte le Colonne',
    groupBy: 'Raggruppa per',
    ungroupBy: 'Separa gruppo per',
    ungroupAll: 'Separa Tutti i Gruppi',
    addToValues: 'Aggiungi ${variable} ai valori',
    removeFromValues: 'Rimuovi ${variable} dai valori',
    addToLabels: 'Aggiungi ${variable} alle etichette',
    removeFromLabels: 'Rimuovi ${variable} dalle etichette',
    resetColumns: 'Reimposta Colonne',
    expandAll: 'Espandi Tutti i Gruppi di Righe',
    collapseAll: 'Chiudi Tutti i Gruppi di Righe',
    copy: 'Copia',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Copia con Intestazioni',
    copyWithGroupHeaders: 'Copia con Intestazioni di Gruppo',
    cut: 'Taglia',
    paste: 'Incolla',
    ctrlV: 'Ctrl+V',
    export: 'Esporta',
    csvExport: 'Esporta CSV',
    excelExport: 'Esporta Excel',
    columnFilter: 'Filtro Colonna',
    columnChooser: 'Scegli Colonne',
    chooseColumns: 'Scegli colonne',
    sortAscending: 'Ordina in modo Ascendente',
    sortDescending: 'Ordina in modo Discendente',
    sortUnSort: 'Annulla Ordinamento',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Somma',
    first: 'Primo',
    last: 'Ultimo',
    min: 'Minimo',
    max: 'Massimo',
    none: 'Nessuno',
    count: 'Conteggio',
    avg: 'Media',
    filteredRows: 'Filtrati',
    selectedRows: 'Selezionati',
    totalRows: 'Righe Totali',
    totalAndFilteredRows: 'Righe',
    more: 'Altro',
    to: 'a',
    of: 'di',
    page: 'Pagina',
    pageLastRowUnknown: '?',
    nextPage: 'Pagina Successiva',
    lastPage: 'Ultima Pagina',
    firstPage: 'Prima Pagina',
    previousPage: 'Pagina Precedente',
    pageSizeSelectorLabel: 'Dimensione Pagina:',
    footerTotal: 'Totale',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Scorri ${variable} nella visualizzazione',

    // Pivoting
    pivotColumnGroupTotals: 'Totale',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Grafico Pivot e Modalità Pivot',
    pivotChart: 'Grafico Pivot',
    chartRange: 'Intervallo Grafico',
    columnChart: 'Colonna',
    groupedColumn: 'Raggruppato',
    stackedColumn: 'Impilato',
    normalizedColumn: '100% Impilato',
    barChart: 'Barra',
    groupedBar: 'Raggruppato',
    stackedBar: 'Impilato',
    normalizedBar: '100% Impilato',
    pieChart: 'Torta',
    pie: 'Torta',
    donut: 'Ciambella',
    lineChart: 'Linea',
    stackedLine: 'Impilata',
    normalizedLine: 'Impilata al 100%',
    xyChart: 'X Y (Dispersione)',
    scatter: 'Dispersione',
    bubble: 'Bolla',
    areaChart: 'Area',
    area: 'Area',
    stackedArea: 'Impilata',
    normalizedArea: '100% Impilata',
    histogramChart: 'Istogramma',
    polarChart: 'Polare',
    radarLine: 'Linea Radar',
    radarArea: 'Area Radar',
    nightingale: 'Rosa di Nightingale',
    radialColumn: 'Colonna Radiale',
    radialBar: 'Barra Radiale',
    statisticalChart: 'Statistico',
    boxPlot: 'Box Plot',
    rangeBar: 'Barra Intervallo',
    rangeArea: 'Area Intervallo',
    hierarchicalChart: 'Gerarchico',
    treemap: 'Mappa ad Albero',
    sunburst: 'Raggiera',
    specializedChart: 'Specializzato',
    waterfall: 'Cascata',
    heatmap: 'Mappa di Calore',
    combinationChart: 'Combinazione',
    columnLineCombo: 'Colonna e Linea',
    AreaColumnCombo: 'Area e Colonna',

    // Charts
    pivotChartTitle: 'Grafico Pivot',
    rangeChartTitle: 'Grafico a Intervallo',
    settings: 'Grafico',
    data: 'Impostazioni',
    format: 'Personalizza',
    categories: 'Categorie',
    defaultCategory: '(Nessuno)',
    series: 'Serie',
    switchCategorySeries: 'Scambia Categoria / Serie',
    categoryValues: 'Valori Categorie',
    seriesLabels: 'Etichette Serie',
    aggregate: 'Aggrega',
    xyValues: 'Valori X Y',
    paired: 'Modalità Abbinata',
    axis: 'Asse',
    xAxis: 'Asse Orizzontale',
    yAxis: 'Asse Verticale',
    polarAxis: 'Asse Polare',
    radiusAxis: 'Asse del Raggio',
    navigator: 'Navigatore',
    zoom: 'Zoom',
    animation: 'Animazione',
    crosshair: 'Mirino',
    color: 'Colore',
    thickness: 'Spessore',
    preferredLength: 'Lunghezza Preferita',
    xType: 'Tipo X',
    axisType: 'Tipo di Asse',
    automatic: 'Automatico',
    category: 'Categoria',
    number: 'Numero',
    time: 'Tempo',
    timeFormat: 'Formato Ora',
    autoRotate: 'Rotazione Automatica',
    labelRotation: 'Rotazione',
    circle: 'Cerchio',
    polygon: 'Poligono',
    square: 'Quadrato',
    cross: 'Croce',
    diamond: 'Diamante',
    plus: 'Più',
    triangle: 'Triangolo',
    heart: 'Cuore',
    orientation: 'Orientamento',
    fixed: 'Fisso',
    parallel: 'Parallelo',
    perpendicular: 'Perpendicolare',
    radiusAxisPosition: 'Posizione',
    ticks: 'Ticchettii',
    gridLines: 'Linee della Griglia',
    width: 'Larghezza',
    height: 'Altezza',
    length: 'Lunghezza',
    padding: 'Margine Interno',
    spacing: 'Spaziatura',
    chartStyle: 'Stile del Grafico',
    title: 'Titolo',
    chartTitles: 'Titoli',
    chartTitle: 'Titolo del Grafico',
    chartSubtitle: 'Sottotitolo',
    horizontalAxisTitle: "Titolo dell'Asse Orizzontale",
    verticalAxisTitle: "Titolo dell'Asse Verticale",
    polarAxisTitle: "Titolo dell'Asse Polare",
    titlePlaceholder: 'Titolo del Grafico',
    background: 'Sfondo',
    font: 'Carattere',
    weight: 'Peso',
    top: 'Sopra',
    right: 'Destra',
    bottom: 'Sotto',
    left: 'Sinistra',
    labels: 'Etichette',
    calloutLabels: 'Etichette di Richiamo',
    sectorLabels: 'Etichette Settori',
    positionRatio: 'Rapporto di Posizione',
    size: 'Dimensione',
    shape: 'Forma',
    minSize: 'Dimensione Minima',
    maxSize: 'Dimensione Massima',
    legend: 'Legenda',
    position: 'Posizione',
    markerSize: 'Dimensione Marcatore',
    markerStroke: 'Tratto Marcatore',
    markerPadding: 'Margine Interno Marcatore',
    itemSpacing: 'Spaziatura Elementi',
    itemPaddingX: 'Margine Interno Elemento X',
    itemPaddingY: 'Margine Interno Elemento Y',
    layoutHorizontalSpacing: 'Spaziatura Orizzontale',
    layoutVerticalSpacing: 'Spaziatura Verticale',
    strokeWidth: 'Spessore Linea',
    offset: 'Offset',
    offsets: 'Offsets',
    tooltips: 'Suggerimenti',
    callout: 'Richiamo',
    markers: 'Marcatori',
    shadow: 'Ombra',
    blur: 'Sfocatura',
    xOffset: 'Offset X',
    yOffset: 'Offset Y',
    lineWidth: 'Spessore Linea',
    lineDash: 'Tratteggio Linea',
    lineDashOffset: 'Offset Linea Tratteggiata',
    scrollingZoom: 'Scorrimento',
    scrollingStep: 'Intervallo Scorrimento',
    selectingZoom: 'Selezione',
    durationMillis: 'Durata (ms)',
    crosshairLabel: 'Etichetta',
    crosshairSnap: 'Aggancio al Nodo',
    normal: 'Normale',
    bold: 'Grassetto',
    italic: 'Corsivo',
    boldItalic: 'Grassetto Corsivo',
    predefined: 'Predefinito',
    fillOpacity: 'Opacità Riempimento',
    strokeColor: 'Colore Linea',
    strokeOpacity: 'Opacità Linea',
    miniChart: 'Mini-Grafico',
    histogramBinCount: 'Conteggio Bin',
    connectorLine: 'Linea di Connessione',
    seriesItems: 'Elementi Serie',
    seriesItemType: 'Tipo Elemento',
    seriesItemPositive: 'Positivo',
    seriesItemNegative: 'Negativo',
    seriesItemLabels: 'Etichette degli Elementi',
    columnGroup: 'Colonna',
    barGroup: 'Barra',
    pieGroup: 'Torta',
    lineGroup: 'Linea',
    scatterGroup: 'X Y (Dispersione)',
    areaGroup: 'Area',
    polarGroup: 'Polare',
    statisticalGroup: 'Statistico',
    hierarchicalGroup: 'Gerarchico',
    specializedGroup: 'Specializzato',
    combinationGroup: 'Combinazione',
    groupedColumnTooltip: 'Raggruppato',
    stackedColumnTooltip: 'Impilato',
    normalizedColumnTooltip: '100% Impilato',
    groupedBarTooltip: 'Raggruppato',
    stackedBarTooltip: 'Impilato',
    normalizedBarTooltip: '100% Impilato',
    pieTooltip: 'Torta',
    donutTooltip: 'Ciambella',
    lineTooltip: 'Linea',
    stackedLineTooltip: 'Impilato',
    normalizedLineTooltip: 'Impilato al 100%',
    groupedAreaTooltip: 'Area',
    stackedAreaTooltip: 'Impilato',
    normalizedAreaTooltip: '100% Impilato',
    scatterTooltip: 'Dispersione',
    bubbleTooltip: 'Bolla',
    histogramTooltip: 'Istogramma',
    radialColumnTooltip: 'Colonna Radiale',
    radialBarTooltip: 'Barra Radiale',
    radarLineTooltip: 'Linea Radar',
    radarAreaTooltip: 'Area Radar',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Barra di Intervallo',
    rangeAreaTooltip: 'Area di Intervallo',
    boxPlotTooltip: 'Box Plot',
    treemapTooltip: 'Treemap',
    sunburstTooltip: 'Esplosione Solare',
    waterfallTooltip: 'Cascata',
    heatmapTooltip: 'Heatmap',
    columnLineComboTooltip: 'Colonna & Linea',
    areaColumnComboTooltip: 'Area & Colonna',
    customComboTooltip: 'Combinazione Personalizzata',
    innerRadius: 'Raggio Interno',
    startAngle: 'Angolo di Inizio',
    endAngle: 'Angolo di Fine',
    reverseDirection: 'Inversione Direzione',
    groupPadding: 'Margine Interno di Gruppo',
    seriesPadding: 'Margine Interno Serie',
    tile: 'Mattonella',
    whisker: 'Antennule',
    cap: 'Tappo',
    capLengthRatio: 'Rapporto Lunghezza',
    labelPlacement: 'Posizionamento Etichetta',
    inside: 'Interno',
    outside: 'Esterno',
    noDataToChart: 'Nessun dato disponibile per il grafico.',
    pivotChartRequiresPivotMode: 'Il Grafico Pivot richiede la Modalità Pivot abilitata.',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Collegato alla Griglia',
    chartUnlinkToolbarTooltip: 'Scollegato dalla Griglia',
    chartDownloadToolbarTooltip: 'Scarica Grafico',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Modifica Grafico',
    chartAdvancedSettings: 'Impostazioni Avanzate',
    chartLink: 'Collega alla Griglia',
    chartUnlink: 'Scollega dalla Griglia',
    chartDownload: 'Scarica Grafico',
    histogramFrequency: 'Frequenza',
    seriesChartType: 'Tipo di Grafico Serie',
    seriesType: 'Tipo di Serie',
    secondaryAxis: 'Asse Secondario',
    seriesAdd: 'Aggiungi una serie',
    categoryAdd: 'Aggiungi una categoria',
    bar: 'Barra',
    column: 'Colonna',
    histogram: 'Istogramma',
    advancedSettings: 'Impostazioni Avanzate',
    direction: 'Direzione',
    horizontal: 'Orizzontale',
    vertical: 'Verticale',
    seriesGroupType: 'Tipo di Gruppo',
    groupedSeriesGroupType: 'Raggruppato',
    stackedSeriesGroupType: 'Impilato',
    normalizedSeriesGroupType: '100% Impilato',
    legendEnabled: 'Abilitato',
    invalidColor: 'Valore colore non valido',
    groupedColumnFull: 'Colonna Raggruppata',
    stackedColumnFull: 'Colonna Impilata',
    normalizedColumnFull: 'Colonna 100% Impilata',
    groupedBarFull: 'Barra Raggruppata',
    stackedBarFull: 'Barra Impilata',
    normalizedBarFull: 'Barra 100% Impilata',
    stackedAreaFull: 'Area Impilata',
    normalizedAreaFull: 'Area 100% Impilata',
    customCombo: 'Combinazione Personalizzata',
    funnel: 'Imbuto',
    coneFunnel: 'Imbuto Conico',
    pyramid: 'Piramide',
    funnelGroup: 'Imbuto',
    funnelTooltip: 'Imbuto',
    coneFunnelTooltip: 'Imbuto Conico',
    pyramidTooltip: 'Piramide',
    dropOff: 'Calo',
    stageLabels: 'Etichette delle Fasi',
    reverse: 'Inverti',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Livello ${variable}. Premi INVIO per modificare.',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Livello ${variable}. ${variable} Premi INVIO per modificare.',
    ariaAdvancedFilterBuilderList: 'Lista di Costruzione Filtro Avanzato',
    ariaAdvancedFilterBuilderFilterItem: 'Condizione del Filtro',
    ariaAdvancedFilterBuilderGroupItem: 'Gruppo di Filtri',
    ariaAdvancedFilterBuilderColumn: 'Colonna',
    ariaAdvancedFilterBuilderOption: 'Opzione',
    ariaAdvancedFilterBuilderValueP: 'Valore',
    ariaAdvancedFilterBuilderJoinOperator: 'Operatore di Unione',
    ariaAdvancedFilterInput: 'Inserimento Filtro Avanzato',
    ariaChecked: 'selezionato',
    ariaColumn: 'Colonna',
    ariaColumnGroup: 'Gruppo di Colonne',
    ariaColumnFiltered: 'Colonna Filtrata',
    ariaColumnSelectAll: 'Attiva/disattiva visibilità di tutte le colonne',
    ariaDateFilterInput: 'Inserimento Filtro Data',
    ariaDefaultListName: 'Lista',
    ariaFilterColumnsInput: 'Inserimento Filtro Colonne',
    ariaFilterFromValue: 'Filtra dal valore',
    ariaFilterInput: 'Inserimento Filtro',
    ariaFilterList: 'Lista dei Filtri',
    ariaFilterToValue: 'Filtra al valore',
    ariaFilterValue: 'Valore del Filtro',
    ariaFilterMenuOpen: 'Apri Menu Filtri',
    ariaFilteringOperator: 'Operatore di Filtro',
    ariaHidden: 'nascosto',
    ariaIndeterminate: 'indeterminato',
    ariaInputEditor: 'Editor di Inserimento',
    ariaMenuColumn: 'Premi ALT GIÙ per aprire il menu della colonna',
    ariaFilterColumn: 'Premi CTRL INVIO per aprire il filtro',
    ariaRowDeselect: 'Premi SPAZIO per deselezionare questa riga',
    ariaHeaderSelection: 'Colonna con selezione intestazione',
    ariaSelectAllCells: 'Premi Spazio per selezionare tutte le celle',
    ariaRowSelectAll: 'Premi SPAZIO per attivare/disattivare la selezione di tutte le righe',
    ariaRowToggleSelection: 'Premi SPAZIO per attivare/disattivare la selezione della riga',
    ariaRowSelect: 'Premi SPAZIO per selezionare questa riga',
    ariaRowSelectionDisabled: 'La selezione della riga è disabilitata per questa riga',
    ariaSearch: 'Cerca',
    ariaSortableColumn: 'Premi INVIO per ordinare',
    ariaToggleVisibility: 'Premi SPAZIO per attivare/disattivare la visibilità',
    ariaToggleCellValue: 'Premi SPAZIO per attivare/disattivare il valore della cella',
    ariaUnchecked: 'non selezionato',
    ariaVisible: 'visibile',
    ariaSearchFilterValues: 'Cerca valori del filtro',
    ariaPageSizeSelectorLabel: 'Dimensione della Pagina',
    ariaChartMenuClose: 'Chiudi Menu Modifica Grafico',
    ariaChartSelected: 'Selezionato',
    ariaSkeletonCellLoadingFailed: 'Caricamento della riga fallito',
    ariaSkeletonCellLoading: 'Caricamento dati della riga in corso',
    ariaDeferSkeletonCellLoading: 'Cella in caricamento',

    // ARIA for Batch Edit
    ariaPendingChange: 'Modifica in sospeso',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Gruppi di Righe',
    ariaValuesDropZonePanelLabel: 'Valori',
    ariaPivotDropZonePanelLabel: 'Etichette di Colonna',
    ariaDropZoneColumnComponentDescription: 'Premi CANC per rimuovere',
    ariaDropZoneColumnValueItemDescription: 'Premi INVIO per cambiare il tipo di aggregazione',
    ariaDropZoneColumnGroupItemDescription: 'Premi INVIO per ordinare',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' di ',
    ariaDropZoneColumnComponentSortAscending: 'crescendo',
    ariaDropZoneColumnComponentSortDescending: 'decrescendo',
    ariaLabelDialog: 'Dialogo',
    ariaLabelColumnMenu: 'Menù Colonna',
    ariaLabelColumnFilter: 'Filtro Colonna',
    ariaLabelSelectField: 'Seleziona Campo',

    // Cell Editor
    ariaLabelCellEditor: 'Editor di cella',
    ariaValidationErrorPrefix: 'Validazione di Editor di cella',
    ariaLabelLoadingContextMenu: 'Caricamento del menu contestuale',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Campo di Selezione Ricco',
    ariaLabelRichSelectToggleSelection: 'Premi SPAZIO per alternare la selezione',
    ariaLabelRichSelectDeselectAllItems: 'Premi CANC per deselezionare tutti gli elementi',
    ariaLabelRichSelectDeleteSelection: "Premi CANC per deselezionare l'elemento",
    ariaLabelTooltip: 'Suggerimento',
    ariaLabelContextMenu: 'Menu Contestuale',
    ariaLabelSubMenu: 'Sottomenu',
    ariaLabelAggregationFunction: 'Funzione di Aggregazione',
    ariaLabelAdvancedFilterAutocomplete: 'Completamento Automatico Filtro Avanzato',
    ariaLabelAdvancedFilterBuilderAddField: 'Aggiungi Campo al Costruttore di Filtri Avanzato',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Seleziona Campo della Colonna nel Costruttore di Filtri Avanzato',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Seleziona Opzione del Campo nel Costruttore di Filtri Avanzato',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Seleziona Operatore di Join nel Costruttore di Filtri Avanzato',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Elenco delle colonne',
    ariaFilterPanelList: 'Elenco dei filtri',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Aggiungi campo filtro',
    ariaLabelFilterCardDelete: 'Elimina filtro',
    ariaLabelFilterCardHasEdits: 'Ha modifiche',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'Vero',
    false: 'Falso',
    invalidDate: 'Data non valida',
    invalidNumber: 'Numero non valido',
    january: 'Gennaio',
    february: 'Febbraio',
    march: 'Marzo',
    april: 'Aprile',
    may: 'Maggio',
    june: 'Giugno',
    july: 'Luglio',
    august: 'Agosto',
    september: 'Settembre',
    october: 'Ottobre',
    november: 'Novembre',
    december: 'Dicembre',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'GG/MM/AAAA',
    timeFormatSlashesMMDDYYYY: 'MM/GG/AAAA',
    timeFormatSlashesDDMMYY: 'GG/MM/AA',
    timeFormatSlashesMMDDYY: 'MM/GG/AA',
    timeFormatDotsDDMYY: 'GG.M.AA',
    timeFormatDotsMDDYY: 'M.GG.AA',
    timeFormatDashesYYYYMMDD: 'AAAA-MM-GG',
    timeFormatSpacesDDMMMMYYYY: 'GG MMMM AAAA',
    timeFormatHHMMSS: 'OO:MM:SS',
    timeFormatHHMMSSAmPm: 'OO:MM:SS AM/PM',
};
