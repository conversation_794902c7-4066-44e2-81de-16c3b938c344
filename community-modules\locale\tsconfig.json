{"extends": "../../tsconfig.base.json", "compilerOptions": {"strict": true, "forceConsistentCasingInFileNames": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "noUnusedParameters": true, "noUnusedLocals": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}