[{"release version": "34.0.1", "markdown": "/releases/34_0_1.md"}, {"release version": "34.0.0", "markdown": "/releases/34_0_0.md"}, {"release version": "33.3.0", "markdown": "/releases/33_3_0.md"}, {"release version": "33.2.4", "markdown": "/releases/33_2_4.md"}, {"release version": "33.2.3", "markdown": "/releases/33_2_3.md"}, {"release version": "33.2.2", "markdown": "/releases/33_2_2.md"}, {"release version": "33.2.0", "markdown": "/releases/33_2_0.md"}, {"release version": "33.1.0", "markdown": "/releases/33_1_0.md"}, {"release version": "33.0.4", "markdown": "/releases/33_0_4.md"}, {"release version": "33.0.3", "markdown": "/releases/33_0_3.md"}, {"release version": "33.0.2", "markdown": "/releases/33_0_2.md"}, {"release version": "33.0.1", "markdown": "/releases/33_0_1.md"}, {"release version": "33.0.0", "markdown": "/releases/33_0_0.md"}, {"release version": "32.3.7", "markdown": "/releases/32_3_7.md"}, {"release version": "32.3.6", "markdown": "/releases/32_3_6.md"}, {"release version": "32.3.5", "markdown": "/releases/32_3_5.md"}, {"release version": "32.3.4", "markdown": "/releases/32_3_4.md"}, {"release version": "32.3.3", "markdown": "/releases/32_3_3.md"}, {"release version": "32.3.2", "markdown": "/releases/32_3_2.md"}, {"release version": "32.3.1", "markdown": "/releases/32_3_1.md"}, {"release version": "32.3.0", "markdown": "/releases/32_3_0.md"}, {"release version": "32.2.2", "markdown": "/releases/32_2_2.md"}, {"release version": "32.2.1", "markdown": "/releases/32_2_1.md"}, {"release version": "32.2.0", "markdown": "/releases/32_2_0.md"}, {"release version": "32.1.0", "markdown": "/releases/32_1_0.md"}, {"release version": "32.0.0", "markdown": "/releases/32_0_0.md"}, {"release version": "31.3.3", "markdown": "/releases/31_3_3.md"}, {"release version": "31.3.2", "markdown": "/releases/31_3_2.md"}, {"release version": "31.3.1", "markdown": "/releases/31_3_1.md"}, {"release version": "31.2.0", "markdown": "/releases/31_2_0.md"}, {"release version": "31.1.0", "markdown": "/releases/31_1_0.md"}, {"release version": "31.0.3", "markdown": "/releases/31_0_3.md"}, {"release version": "31.0.2", "markdown": "/releases/31_0_2.md"}, {"release version": "31.0.1", "markdown": "/releases/31_0_1.md"}, {"release version": "31.0.0", "markdown": "/releases/31_0_0.md"}, {"release version": "30.2.1", "markdown": "/releases/30_2_1.md"}, {"release version": "30.2.0", "markdown": "/releases/30_2_0.md"}, {"release version": "30.1.0", "markdown": "/releases/30_1_0.md", "showExpandLink": true}, {"release version": "30.0.6", "markdown": "/releases/30_0_6.md"}, {"release version": "30.0.5", "markdown": "/releases/30_0_5.md"}, {"release version": "30.0.4", "markdown": "/releases/30_0_4.md"}, {"release version": "30.0.3", "markdown": "/releases/30_0_3.md"}, {"release version": "30.0.2", "markdown": "/releases/30_0_2.md"}, {"release version": "30.0.1", "markdown": "/releases/30_0_1.md"}, {"release version": "30.0.0", "markdown": "/releases/30_0_0.md", "showExpandLink": true}, {"release version": "<p><b>20th Apr 2023 - Grid v29.3.0 (Charts v7.3.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>Grid<ul><li>AG-2720 - Allow horizontal sticky column group header labels (see: <a href=\"https://www.ag-grid.com/archive/29.3.0/javascript-data-grid/column-groups/#sticky-label\">Sticky Column Group Labels</a>)</li><li>AG-7013 - Add support for sticky group rows in the server-side row model (see: <a href=\"https://www.ag-grid.com/archive/29.3.0/react-data-grid/server-side-model-grouping/#sticky-groups\">SSRM - Sticky Groups</a>)</li><li>AG-6880 - Add support for sticky group rows in tree data</li><li>AG-7080 - Add support for sticky rows with master rows in master / detail</li><li>AG-4989 - Allow AG Grid to work from inside a shadow DOM</li></ul></li><li>Charts<ul><li>AG-3890 - Improve layout of pie chart callout labels to avoid label collisions (see: <a href=\"https://www.ag-grid.com/archive/29.3.0/javascript-charts/pie-series/#labels\">Pie Series Labels</a>)</li><li>AG-6181 - Allow custom tooltips to remain visible to allow interacting with tooltip content for accessibility users (see: <a href=\"https://www.ag-grid.com/archive/29.3.0/javascript-charts/tooltips/#interaction-with-tooltips\">Interaction with Tooltips</a>)</li><li>AG-7368 - Add node / pointer tooltip position types (see: <a href=\"https://www.ag-grid.com/archive/29.3.0/javascript-charts/tooltips/#tooltip-position\">Tooltip Position</a>)</li><li>AG-3227 - Allow configuring tooltip position offset&nbsp;(see: <a href=\"https://www.ag-grid.com/archive/29.3.0/javascript-charts/tooltips/#tooltip-position\">Tooltip Position</a>)</li><li>AG-6716 - Allow per-column sparkline tooltip instances</li></ul></li></ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>21st Mar 2023 - Grid v29.2.0 (Charts v7.2.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>Filters</li><ul><li>AG-2558 - Allow more than two conditions in the column filters (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/filter-conditions/#number-of-conditions\" rel=\"nofollow\">Filter Conditions</a>)</li><li>AG-7992 - Allow limiting floating filter inputs of numeric filters to numeric values only (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/filter-number/#custom-number-support\" rel=\"nofollow\">Custom Number Support</a>)</li></ul><li>SSRM</li><ul><li>AG-2223 - Allow select all / deselect all via header checkbox selection (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/server-side-model-selection/#header-checkbox-selection\" rel=\"nofollow\">Header Checkbox Selection</a>)</li><li>AG-3354 - Add support for tri-state checkbox group selection (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/server-side-model-selection/#group-selection\" rel=\"nofollow\">Group Selection</a>)</li><li>AG-8103 - Allow row selection across multiple group levels (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/server-side-model-selection/\" rel=\"nofollow\">SSRM Row Selection</a>)</li></ul><li>Charts</li><ul><li>AG-8051 - Add ability to include chart footnotes (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-charts/layout/#footnote\" rel=\"nofollow\">Chart Footnote</a>)</li><li>AG-7937 - Allow option for padding at min and max of all axis types (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-charts/layout/#series-area-padding\" rel=\"nofollow\">Series Area Padding</a>)</li><li>AG-6926 - Allow displaying stacked and unstacked column series together in the same column group in charts (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-charts/bar-series/#grouped-stacks\" rel=\"nofollow\">Grouped Stacks</a>)</li><li>AG-5750 - Allow chart overlays to display \"No Series\" or \"No Data\" messages over the chart area (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-charts/overlays/\" rel=\"nofollow\">Chart Overlays</a>)</li><li>AG-7121 - Add double-click detection for event handlers (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-charts/legend/#series-visibility-toggling\" rel=\"nofollow\">Series Visibility Toggling</a>)</li><li>AG-8058 - Add click near a point detection (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-charts/events/#interaction-ranges\" rel=\"nofollow\">Interaction Ranges</a>)</li></ul><li>Miscellaneous</li><ul><li>AG-3072 - [Column Headers] Allow column group header cell to span multiple rows when other groups have more levels (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/column-groups/#span-header-height\" rel=\"nofollow\">Span Header Height</a>)</li><li>AG-7522 - Provide Generic Type for Context (see <a href=\"https://www.ag-grid.com/archive/29.2.0/javascript-data-grid/typescript-generics/#context-tcontext\" rel=\"nofollow\">Typescript Generics - Context</a>)</li></ul></ul>", "breaking changes": "", "deprecations": "<p><strong>Deprecations:</strong></p><ul><li>The <code>simpleHttpRequest</code> method was only meant for use in internal AG Grid documentation examples but is no longer required for that purpose.&nbsp;Please use the browser fetch API directly (see <a href=\"https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API\" rel=\"nofollow\">Fetch API</a>)</li></ul>"}, {"release version": "<p><b>17th Feb 2023 - Grid v29.1.0 (Charts v7.1.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>Filters</li><ul><li>AG-4958 - Provide a built-in generic way to filter group columns&nbsp;(see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-data-grid/grouping-column-filter/\" rel=\"nofollow\">Row Grouping - Group Column Filter</a>)</li><li>AG-7965 - Allow tree list filter to support complex objects in the tree data group field&nbsp;(see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-data-grid/filter-set-tree-list/#complex-objects\" rel=\"nofollow\">Set Filter Tree List - Complex Objects</a>)</li><li>AG-7664 - Speed up quick filter by optionally ignoring cells in hidden columns&nbsp;(see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-data-grid/filter-quick/#exclude-hidden-columns\" rel=\"nofollow\">Quick Filter - Exclude Hidden Columns</a>)</li></ul><li>Charts</li><ul><li>AG-7909 / AG-6834 / AG-7908 / AG-7910 - Axis Ticks Enhancements (see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-charts/axes/#axis-ticks\" rel=\"nofollow\">Axis Ticks</a>)</li><li>AG-7890 - Add API to download standalone charts&nbsp;(see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-charts/api-download/\" rel=\"nofollow\">Download API</a>)</li></ul><li>Miscellaneous</li><ul><li>AG-7151 / AG-7167 - [Pivoting] Add API methods to expand and collapse pivot columns (see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-data-grid/pivoting/#expandable-pivot-column-groups\" rel=\"nofollow\">Expandable Pivot Column Groups</a>)</li><li>AG-3859 - [Row Selection] Allow HeaderCheckboxSelection to select all the rows shown on the current page after filter and sort (see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-data-grid/row-selection/#example-select-only-the-current-page\" rel=\"nofollow\">Row Selection - Select Current Page</a>)</li><li>AG-6050 - [Undo/Redo] Add Undo / Redo Events (see <a href=\"https://www.ag-grid.com/archive/29.1.0/javascript-data-grid/undo-redo-edits/#undo--redo-events\" rel=\"nofollow\">Undo / Redo Events</a>)</li></ul></ul>", "breaking changes": "", "deprecations": "<p><strong>Deprecations:</strong></p><ul><li>AG-7910 - <code>axes[].tick.count</code> is now deprecated. Use <code>axes[].tick.minSpacing</code>, <code>axes[].tick.maxSpacing</code>, <code>axes[].tick.interval</code> or <code>axes[].tick.values</code> instead.</li><li>AG-7886 - The unused root option <code>type: 'groupedCategory'</code> which was an alias for <code>cartesian</code> has been removed, and <code>type: 'cartesian'</code>, <code>type: 'polar'</code> and <code>type: 'hierarchy'</code> have now been deprecated. Either remove the <code>type</code> option from your configuration or specify a default series type instead.</li></ul>"}, {"release version": "<p><b>13th Jan 2023 - Grid v29.0.0 (Charts v7.0.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><p><u>Grid</u></p><ul><li>AG-6396 - [Column Filters]&nbsp;Allow Set Filter values to be displayed in a hierarchical list (see <a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-data-grid/filter-set-tree-list/\" rel=\"nofollow\">Set Filter - Tree List</a>)</li><li>AG-7394 - [SSRM] Add support for transactions when Infinite Scrolling is enabled (see <a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-data-grid/server-side-model-updating-transactions/\" rel=\"nofollow\">SSRM Transactions</a>)</li><li>AG-145 - [Excel Export] Allow exporting row group footers to Excel (see <a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-data-grid/excel-export-customising-content/#customising-cell-and-row-group-values\" rel=\"nofollow\">Excel Export - Customising Cell and Row Group values</a>)</li><li>AG-3591 - [Column Menu] Allow custom column layouts in the column menu columns (see <a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-data-grid/column-menu/#custom-column-layout\" rel=\"nofollow\">Column Menu - Custom Column Layout</a>)</li><li>AG-129 - [Column Filters] Allow set filter to support complex objects as provided set filter values (see <a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-data-grid/filter-set-filter-list/#filter-value-types\" rel=\"nofollow\">Set Filter - Filter Value Types</a>)</li></ul><p><u>Charts</u></p><p style=\"padding-left: 30px;\"><a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-charts/legend/\" rel=\"nofollow\">Legend Enhancements</a></p><ul><li>AG-4835 - Allow legend items to be a paginated when chart area is too small</li><li>AG-7584 - Allow configuring legend width / height</li><li>AG-6424 - Allow disabling legend item click</li><li>AG-5068 - Allow configuration of the arrangement of the legend items to override smart auto arrangement</li></ul><p style=\"padding-left: 30px;\"><a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-charts/treemap-series/\" rel=\"nofollow\">Treemap Enhancements</a></p><ul><li>AG-7588 - Add Treemap cell value label formatter</li><li>AG-7585 - Treemap layout improvements</li><li>AG-5221 - Allow using color values directly from the data for the treemap series</li></ul>", "breaking changes": "<p><strong>Breaking Changes:</strong></p><p><u>Angular Upgrade</u></p><ul><li>The minimum Angular dependency supported by AG Grid v29 is now Angular v10 via legacy packages. Previous versions of Angular aren't supported by AG Grid v29. See <a href=\"https://www.ag-grid.com/archive/29.0.0/angular-data-grid/angular-compatibility/\" rel=\"nofollow\">Angular Compatibility</a> for more information.</li></ul><p><u>React Change Detection</u></p><ul><li>In previous versions of AG Grid, we introduced a <code>DeepValueCheck</code> change detection strategy that performed an additional deep value comparison above and beyond reference equality. This was to help React users who were unintentionally setting their <code>rowData</code> prop when the row data had not actually changed. This resulted in additional grid re-renders as the grid reapplied the rows despite there being no real change. However, the <code>DeepValueCheck</code> introduced a number of issues, as well as introducing an additional cost for users who correctly manage their grid state. i.e only updating the <code>rowData</code> prop when there are real data changes. When correctly using Hooks, i.e <code>setState()</code> and dependency arrays, <code>DeepValueCheck</code> is not required and so it has been removed to avoid the issues described above. If you now see additional re-renders please check that you are only updating grid state properties when they have actually changed. See <a href=\"https://www.ag-grid.com/archive/29.0.0/react-data-grid/react-hooks/\" rel=\"nofollow\">React Hooks</a> for more information.</li></ul><p><u>SSRM</u></p><ul><li>Infinite scrolling is now enabled by default. To revert to the previous default, i.e. no infinite scrolling, please set <code>suppressServerSideInfiniteScroll=true</code>. As a result of this change, <code>serverSideInfiniteScroll</code> no longer makes sense and has been removed. Similarly <code>ServerSideGroupLevelState.infiniteScroll</code> has also be replaced by <code>ServerSideGroupLevelState.suppressInfiniteScroll</code>.</li><li>The <code>getCacheBlockState()</code> grid API function is no longer supported by the new default store type but will continue to work when <code>suppressServerSideInfiniteScroll=true</code>.</li><li>When using SSRM and filtering or purging a level, only one loading row is displayed instead of all grid rows shown in loading state.</li></ul><p><u>Row Grouping</u></p><ul><li>Unbalanced groups have now been disabled by default. Rows with empty strings / <code>undefined</code> / <code>null</code> values for the group column will now be grouped under a `(Blanks)` group unless unbalanced groups are explicitly enabled by setting <code>groupAllowUnbalanced=true</code>. Also note that ' ' (one singular space) is no longer treated as equivalent to <code>null</code> and <code>undefined</code> when pivoting.</li></ul><p><u>Set Filter</u></p><p>Set Filters now maintain the type of the values, instead of everything being converted to strings:</p><ul><li>Primitive types (e.g. number, boolean, etc.) are strings when used as keys in the Filter Model, but maintain their type otherwise (e.g. for Value Formatters, Comparators, etc.)</li><li>Complex objects support separate keys and display values. Value Formatter is now mandatory. Supplied Values for the Filter List require the full complex object.</li><li>Key Creator is applied after splitting arrays for Multiple Values with complex objects, rather than before.</li><li>The old behaviour of always converting all filter values to strings can be replicated by setting <code>filterParams.convertValuesToStrings=true</code> in the Column Definition.</li></ul><p><u>Layout &amp; Styling</u></p><ul><li>In AG Grid v28, we ported our grid layout system to use CSS variables. AG Grid v27 and earlier used SASS. In order to ease the upgrade to AG Grid v28, we continued to include the legacy Sass API and CSS stylesheets at their old file paths. However, in AG Grid v29 SASS is now removed and you need to change the import paths for CSS and SASS (.scss) files. The v27 import paths are now invalid in v29 and the themes won't work. For more details see <a href=\"https://www.ag-grid.com/archive/29.0.0/javascript-data-grid/global-style-upgrading-to-v28/\" rel=\"nofollow\">Upgrading Styles to v28</a>.</li><li><code>--ag-borders-row</code> has been removed and will no longer work. Use the combination of <code>ag-row-border-style</code> and <code>ag-row-border-width</code> to replace it.</li></ul><p><u>ColDef</u></p><p>The following properties were previously deprecated and have now been removed.</p><ul><li><code>newValueHandler()</code> - removed, use <code>valueSetter</code> instead.</li><li><code>sortedAt</code> - removed, use <code>sortIndex</code> instead.</li><li><code>pinnedRowCellRenderer</code> - removed, use <code>cellRendererSelector</code> instead and use <code>params.node.rowPinned</code> to distinguish.</li><li><code>pinnedRowCellRendererFramework</code> - removed, use <code>cellRendererSelector</code> instead and use <code>params.node.rowPinned</code> to distinguish.</li><li><code>pinnedRowCellRendererParams</code> - removed, use <code>cellRendererSelector</code> instead and use <code>params.node.rowPinned</code> to distinguish.</li><li><code>pinnedRowValueFormatter</code> - removed, use <code>valueFormatter</code> instead and use <code>params.node.rowPinned</code> to distinguish.</li></ul><p><u>GridOptions</u></p><p>The following properties were previously deprecated and have now been removed.</p><p>Grouping</p><ul><li><code>groupRowInnerRenderer</code> - removed, use <code>groupRowRendererParams.innerRenderer</code> instead.</li><li><code>groupRowInnerRendererFramework</code> - removed, use <code>groupRowRendererParams.innerRenderer</code> instead.</li><li><code>groupMultiAutoColumn</code> - removed, use <code>groupDisplayType = 'multipleColumns'</code> instead.</li><li><code>groupUseEntireRow</code> - removed, use <code>groupDisplayType = 'groupRows'</code> instead.</li><li><code>groupSuppressAutoColumn</code> - removed, use <code>groupDisplayType='custom'</code> instead.</li><li><code>defaultGroupSortComparator</code> - removed, use <code>initialGroupOrderComparator</code> instead.</li></ul><p>Column State</p><ul><li><code>deltaColumnMode, immutableColumns, applyColumnDefOrder</code> - removed, the grid now works like this as default. To keep column order maintained, set grid property <code>maintainColumnOrder=true</code>.</li><li><code>suppressEnterpriseResetOnNewColumns</code> - removed as no longer required.</li><li><code>suppressSetColumnStateEvents, suppressColumnStateEvents</code> - removed, use <code>event.source = \"api\"</code> to identify events due to setting column state via the API.</li><li><code>colWidth</code> - removed, use <code>defaultColDef.width</code> instead.</li><li><code>minColWidth</code> - removed, use <code>defaultColDef.minWidth</code> instead.</li><li><code>maxColWidth</code> - removed, use <code>defaultColDef.maxWidth</code> instead.</li></ul><p>Full Width</p><ul><li><code>deprecatedEmbedFullWidthRows</code> - removed, use <code>embedFullWidthRows</code> instead.</li></ul><p>Editing</p><ul><li><code>stopEditingWhenGridLosesFocus</code> - removed, use <code>stopEditingWhenCellsLoseFocus</code> instead.</li></ul><p>Export</p><ul><li><code>defaultExportParams</code> - removed, use <code>defaultCsvExportParams</code> for CSV or <code>defaultExcelExportParams</code> for Excel exports instead.</li></ul><p>Keyboard</p><ul><li><code>suppressKeyboardEvent</code> - removed, use <code>colDef.suppressKeyboardEvent</code> or for every column via <code>defaultColDef.suppressKeyboardEvent</code>.</li></ul><p>Selection</p><ul><li><code>rowDeselection</code> - removed. Now true by default and should be suppressed by using <code>suppressRowDeselection</code>.</li></ul><p>Updating Data</p><ul><li><code>batchUpdateWaitMillis</code> - removed, use <code>asyncTransactionWaitMillis</code> instead.</li><li><code>deltaRowDataMode</code> - removed, use <code>getRowId()</code> callback to enable immutable data mode.</li></ul><p><u>Column Api</u></p><p>The following methods were previously deprecated and have now been removed.</p><p>Column State</p><ul><li><code>setState, setColumnState</code> - removed, use <code>applyColumnState</code> instead.</li><li><code>getState</code> - removed, use <code>getColumnState</code> instead.</li><li><code>resetState</code> - removed, use <code>resetColumnState</code> instead.</li><li><code>hideColumn, hideColumns</code> - removed, use <code>setColumnVisible, setColumnsVisible</code> instead.</li><li><code>columnGroupOpened</code> - removed, use <code>setColumnGroupOpened</code> instead.</li></ul><p>Aggregation</p><ul><li><code>getAggregationColumns</code> - removed, use <code>getValueColumns</code> instead.</li><li><code>addAggregationColumn, addAggregationColumns</code> - removed, use <code>addValueColumn, addValueColumns</code> instead.</li><li><code>removeAggregationColumn, removeAggregationColumns</code> - removed, use <code>removeValueColumn, removeValueColumns</code> instead.</li><li><code>resetState</code> - removed, use <code>resetColumnState</code> instead.</li><li><code>setColumnAggFunction</code> - removed, use <code>setColumnAggFunc</code> instead.</li></ul><p>Display</p><ul><li><code>getDisplayNameForCol</code> - removed, use <code>getDisplayNameForColumn</code> instead.</li></ul><p><u>Grid API</u></p><p>The following methods were previously deprecated and have now been removed.</p><p>Pinned Rows</p><ul><li><code>setFloatingTopRowData, setFloatingBottomRowData</code> - removed, use <code>setPinnedTopRowData, setPinnedBottomRowData</code> instead.</li><li><code>getFloatingTopRowCount, getFloatingBottomRowCount</code> - removed, use <code>getPinnedTopRowCount, getPinnedBottomRowCount</code> instead.</li><li><code>getFloatingTopRow, getFloatingBottomRow</code> - removed, use <code>getPinnedTopRow, getPinnedBottomRow</code> instead.</li></ul><p>Selection</p><ul><li><code>selectIndex, deselectIndex</code> - removed, use <code>rowNode.setSelected(isSelected)</code> instead.</li><li><code>selectNode, deselectNode</code> - removed, use <code>rowNode.setSelected(isSelected)</code> instead.</li><li><code>getSelectedNodesById</code> - removed, use <code>getSelectedNodes</code> instead.</li><li><code>isNodeSelected</code> - removed, use <code>rowNode.isSelected</code> instead.</li><li><code>getRangeSelections</code> - removed, use <code>getCellRanges</code> instead.</li><li><code>addRangeSelection</code> - removed, use <code>addCellRange</code> instead.</li></ul><p>Grouping</p><ul><li><code>onGroupExpandedOrCollapsed(param)</code> - method no longer accepts an optional parameter.</li></ul><p>Display</p><ul><li><code>ensureColIndexVisible</code> - removed, use <code>ensureColumnVisible</code> instead.</li><li><code>doLayout</code> - removed, no longer required as grid now responds to size changes.</li><li><code>checkGridSize</code> - removed as was legacy and no longer required.</li><li><code>getFirstRenderedRow, getLastRenderedRow</code> - removed, use <code>getFirstDisplayedRow, getLastDisplayedRow</code> instead.</li><li><code>addVirtualRowListener</code> - removed, use <code>addRenderedRowListener</code> instead.</li></ul><p>Client Side Row Model</p><ul><li><code>refreshInMemoryRowModel</code> - removed, use <code>refreshClientSideRowModel(step)</code> instead.</li><li><code>recomputeAggregates</code> - removed, use <code>refreshClientSideRowModel('aggregate')</code> instead.</li><li><code>updateRowData</code> - removed, use <code>applyTransaction</code> instead.</li><li><code>batchUpdateRowData, insertItemsAtIndex, addItems, removeItems</code> - removed, use <code>applyTransactionAsync</code> instead.</li></ul><p>Infinite Row Model</p><ul><li><code>refreshVirtualPageCache, refreshInfinitePageCache</code> - removed, use <code>refreshInfiniteCache</code> instead.</li><li><code>purgeVirtualPageCache, purgeInfinitePageCache</code> - removed, use <code>purgeInfiniteCache</code> instead.</li><li><code>getVirtualRowCount</code> - removed, use <code>getInfiniteRowCount</code> instead.</li><li><code>isMaxRowFound</code> - removed, use <code>isLastRowIndexKnown</code> instead.</li><li><code>setVirtualRowCount, setInfiniteRowCount</code> - removed, use <code>setRowCount</code> instead.</li><li><code>getVirtualPageState, getInfinitePageState</code> - removed, use <code>getCacheBlockState</code> instead.</li></ul><p>Server Side Row Model</p><ul><li><code>setEnterpriseDatasource</code> - removed, use <code>setServerSideDatasource</code> instead.</li><li><code>purgeEnterpriseCache, purgeServerSideCache</code> - removed, use <code>refreshServerSide({purge: true})</code> instead.</li></ul><p><u>Column Object</u></p><p>The following Column methods were previously deprecated and have now been removed:</p><ul><li><code>isLockPosition()</code> removed, use <code>col.getColDef().lockPosition</code> instead.</li><li><code>isLockVisible()</code> removed, use <code>col.getColDef().lockVisible</code> instead.</li><li><code>isLockPinned()</code> - removed, use <code>col.getColDef().lockPinned</code> instead.</li></ul><p><u>IDetailCellRendererParams</u></p><ul><li><code>suppressRefresh</code> removed - set <code>refreshStrategy='nothing'</code> instead.</li><li><code>autoHeight</code> removed - set <code>gridOptions.detailRowAutoHeight=true</code> instead.</li></ul><p><u>ICellRendererParams</u></p><ul><li><code>addRowCompListener</code> removed - this method was originally provided as a workaround to know when cells were destroyed in AG Grid before custom Cell Renderers could be provided.</li></ul><p><u>GroupCellRendererParams</u></p><ul><li><code>padding</code> removed - instead use the <code>--ag-row-group-indent-size</code> CSS Variable instead.</li></ul><p><u>ICellEditorParams</u></p><ul><li><code>key</code> removed - use <code>eventKey</code> instead.</li></ul><p><u>IRowNode</u></p><ul><li>Typescript users may get type errors if accessing internal properties of <code>RowNode</code> following the introduction of <code>IRowNode</code>. Please resolve this by casting to <code>RowNode</code> or updating your code to use properties available on <code>IRowNode</code></li></ul><p><u>IFilterParams</u></p><ul><li>The <code>valueGetter</code> parameter is no longer available on any of the filter params interfaces (e.g. <code>ISetFilterParams</code>) used in <code>colDef.filterParams</code>. Please use <code>colDef.filterValueGetter</code> instead</li></ul><p><u>AG Grid Column Components</u></p><ul><li>The framework column components were previously deprecrated and have now been removed. Define your column definitions in code via <code>gridOptions.columnDefs</code> or set directly on the AG Grid Component via the <code>columnDefs</code> property.</li></ul><p><u>Events</u></p><ul><li>Renamed the AG Grid event interface <code>DragEvent</code> to <code>AgDragEvent</code> to stop clashing with the browser <code>DragEvent</code>.</li></ul><p><u>Integrated Charts</u></p><ul><li>Integrated charts display the chart tool button by default instead of the toolbar hamburger button. In order to display the toolbar hamburger button, please set <code>suppressChartToolPanelsButton=true</code>.</li></ul><p><u>Standalone Charts</u></p><ul><li>Treemap tile labels are now disabled by default, enable by specifying a <code>labelKey</code>.</li><li>The Treemap formatter and tooltip renderer <code>datum</code> property will now point to the actual object from the <code>datum</code>, <code>depth</code> and <code>fill</code> can now be accessed from the parameters object for these functions.</li><li><code>AgTreemapSeriesLabelsOptions.color</code> has been replaced by <code>AgTreemapSeriesLabelsOptions.value.key</code>.</li><li><code>AgTreemapSeriesOptions.colorParents</code> has been removed - use a <code>AgTreemapSeriesOptions.formatter</code> instead for finer-grained non-leaf-node rendering control.</li><li><code>AgSeriesHighlightStyle</code> style properties have moved under <code>AgSeriesHighlightStyle.item</code>.</li><li><code>AgScatterSeriesOptions</code> style properties have moved under <code>AgScatterSeriesOptions.marker.</code></li><li><code>AgAreaSeriesOptions</code> - use multiple <code>series</code> entries and singular property names:<ul><li><code>yKeys</code>&nbsp;- use <code>yKey</code> instead.</li><li><code>yNames</code>&nbsp;- use <code>yName</code> instead.</li><li><code>fills</code>&nbsp;- use <code>fill</code> instead.</li><li><code>strokes</code>&nbsp;- use <code>stroke</code> instead.</li></ul></li><li><code>AgBarSeriesOptions</code> - use multiple <code>series</code> entries and singular property names:<ul><li><code>yKeys</code>&nbsp;- use <code>yKey</code> instead.</li><li><code>yNames</code>&nbsp;- use <code>yName</code> instead.</li><li><code>fills</code>&nbsp;- use <code>fill</code> instead.</li><li><code>strokes</code>&nbsp;- use <code>stroke</code> instead.</li></ul></li></ul>", "deprecations": "<p><strong>Deprecations:</strong></p><p><u>React Change Detection</u></p><ul><li>As the <code>DeepValueCheck</code> change detection strategy has been removed the <code>rowDataChangeDetectionStrategy</code> has now been deprecated as it no longer supports a different strategy then the default reference equals comparison.</li></ul><p><u>Filters</u></p><ul><li><code>filterParams.convertValuesToStrings=true</code> in the Column Definition is deprecated. This is a new setting to enable the legacy behaviour for converting values in the Set Filter to strings. Instead, handle the values with the correct/original type, including the processing of complex objects.</li><li><code>ISetFilter.getValues()</code> is deprecated, <code>getFilterValues</code> should be used to get the values in the Set Filter (e.g. complex objects if provided), or <code>getFilterKeys</code> to get the string keys.</li></ul>"}, {"release version": "<p><b>2nd Nov 2022 - Grid v28.2.1 (Charts v6.2.1)</b></p>", "feature highlights": "", "breaking changes": "", "deprecations": "<p><strong>Deprecations:</strong></p><ul><li>AG-7290: <code>pieSeries.label</code> is now deprecated (use <code>pieSeries.calloutLabel</code> instead)</li><li>AG-7290: <code>pieSeries.labelKey</code> is deprecated (use <code>pieSeries.calloutLabelKey</code> instead)</li><li>AG-7290: <code>pieSeries.labelName</code> is deprecated (use <code>pieSeries.calloutLabelName</code> instead)</li><li>AG-7290: <code>pieSeries.callout</code> is deprecated (use <code>pieSeries.calloutLine</code> instead)</li><li>AG-7152: <code>AgChartLegendLabelFormatterParams.id</code> is deprecated (use <code>AgChartLegendLabelFormatterParams.seriesId</code> instead)</li><li>AG-7152: <code>AgNodeClickEvent.series</code> is deprecated (use <code>AgNodeClickEvent.seriesId</code> instead)</li><li>AG-7152: <code>AgSeriesNodeClickParams.series</code> is deprecated (use <code>AgSeriesNodeClickParams.seriesId</code> instead)</li></ul>"}, {"release version": "<p><b>4th Oct 2022 - Grid v28.2.0 (Charts v6.2.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>AG-7217 - Add support for SolidJS (see <a href=\"https://www.ag-grid.com/archive/28.2.0/react-data-grid/solidjs/\" rel=\"nofollow\">SolidJS - Getting Started</a>)</li><li>Charting Enhancements</li><ul><li>AG-5988 - [Standalone] Add the angle and radius value to the pie/doughnut label formatter params (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-charts/pie-series/#sector-labels\" rel=\"nofollow\">Sector Labels</a>)</li><li>AG-5220 - [Standalone] Add a tile formatter to treemap series (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-charts/treemap-series/#alternative-configuration\" rel=\"nofollow\">Treemap - Alternative Configuration</a>)</li><li>AG-5198 - [Standalone] Allow coloring the middle of the doughnut chart and placing text inside (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-charts/pie-series/#text-inside-a-donut\" rel=\"nofollow\">Text Inside a Donut</a>)</li><li>AG-7313 - [Standalone] Allow displaying pie labels inside pie/doughnut chart sectors (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-charts/pie-series/#example-pie-labels-in-sectors\" rel=\"nofollow\">Example - Pie Chart with Labels in Sectors</a>)</li><li>AG-4460 - [Integrated] Allow setting the size of the exported image when saving chart as an image (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/integrated-charts-api-downloading-image/#downloading-charts-via-grid-api\" rel=\"nofollow\">Downloading Charts via Grid API</a>)</li><li>AG-5026 - [Integrated] Allow customizing the list of available series types in the settings tab of the chart toolbar (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/integrated-charts-toolbar/#toolbar-customisation\" rel=\"nofollow\">Chart Tool Panel Customisation</a>)</li><li>AG-6899 - [Integrated] Allow showing chart configuration panel as open by default (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/integrated-charts-toolbar/#toolbar-customisation\" rel=\"nofollow\">Chart Tool Panel Customisation</a>)</li><li>AG-5434 - [Integrated] Add API to call the chart toolbar buttons (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/integrated-charts-api-chart-tool-panel/\" rel=\"nofollow\">Opening and closing the Chart Tool Panel via Grid API</a>)</li></ul><li>Grid Enhancements</li><ul><li>AG-1924 - [Excel Export] Allow providing different styles for different column headers when exporting to Excel (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/excel-export-styles/\" rel=\"nofollow\">Excel Export - Styles</a>)</li><li>AG-302 - [Excel Export] Allow exporting rows in a filtered grid even if they don't match the filter (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/excel-export-rows/#export-all-unprocessed-rows\" rel=\"nofollow\">Export All Unprocessed Rows</a>)</li><li>AG-7294 - [Row Grouping] Allow suppression of sort click event and indicator in row group panel and side bar (see <a href=\"https://www.ag-grid.com/archive/28.2.0/javascript-data-grid/grouping-group-panel/#suppress-sorting\" rel=\"nofollow\">Row Group Panel - Suppress Sorting</a>)</li></ul></ul>", "breaking changes": "", "deprecations": "<p><strong>Deprecations:</strong></p><ul><li>AG-5988: The <code>value</code> property in <code>AgPieSeriesLabelOptions.formatter</code> is now deprecated, use <code>datum</code> instead.</li></ul>"}, {"release version": "<p><b>23rd Aug 2022 - Grid v28.1.1 (Charts v6.1.1)</b></p>", "feature highlights": "", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>3rd Aug 2022 - Grid v28.1.0 (Charts v6.1.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>AG-5105 - [Chart] Allow adding crossing lines (see <a href=\"https://www.ag-grid.com/archive/28.1.0/javascript-charts/axes/#cross-lines\" rel=\"nofollow\">Cross Lines</a>)</li><li>AG-1063 - [SSRM] Allow setting the initial page and scroll position (see <a href=\"https://www.ag-grid.com/archive/28.1.0/javascript-data-grid/server-side-model-infinite-scroll/#initial-scroll-position\" rel=\"nofollow\">Initial Scroll Position</a>)</li><li>AG-6873 - [Row Selection] Allow displaying disabled (read-only) checkboxes when row is not selectable (see <a href=\"https://www.ag-grid.com/archive/28.1.0/javascript-data-grid/row-selection/#example-disabled-checkboxes\" rel=\"nofollow\">Disabled Checkboxes</a>)</li></ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>6th Jul 2022 - Grid v28.0.0 (Charts v6.0.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>AG-6370 - Extend CSS variables in Themes (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/theming-v32-customisation-variables/\" rel=\"nofollow\">CSS Variable Reference</a>)</li><li>AG-6149 - Publish fully compatible Angular Ivy package (see <a href=\"https://www.ag-grid.com/archive/28.0.0/angular-data-grid/angular-compatibility/\" rel=\"nofollow\">Angular Compatibility</a>)</li><li>AG-4807 - Add Generics to row data and cell value (see <a href=\"https://www.ag-grid.com/archive/28.0.0/angular-data-grid/typescript-generics/\" rel=\"nofollow\">Typescript Generics</a>)</li><li>Row Grouping</li><ul><li>AG-6587 - Allow sorting different group columns separately by clicking the grouped column items in the row group panel (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/grouping-group-panel/\" rel=\"nofollow\">Row Group Panel</a>)</li><li>AG-463 - Allow the group row for the expanded row group to float/stick at the top of the viewport while scrolling through its child rows (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/grouping-sticky-groups/\" rel=\"nofollow\">Sticky Groups</a>)</li><li>AG-3069 - Allow exporting row groups and tree data to Excel so that you can expand / collapse them in Excel</li></ul><li>Server-side Row Model</li><ul><li>AG-6848 - Allow Server-Side Sorting &amp; Filtering when using server-side row model without Infinite Scroll (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/server-side-model-sorting/#server-side-row-group-sorting\" rel=\"nofollow\">Server-side Sorting</a>) &amp; (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/server-side-model-filtering/#server-side-row-group-filtering\" rel=\"nofollow\">Server-side Filtering</a>)</li><li>AG-6853 - Allow preserving row group expanded/collapsed state when performing a non-purge refresh with SSRM Infinite Scroll (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/server-side-model-refresh/#example-2-keeping-group-state-with-infinite-scrolling\" rel=\"nofollow\">Keeping Group State with Infinite Scrolling</a>)</li></ul><li>Miscellaneous</li><ul><li>AG-1555 - Allow column headers to support word wrapping for multi-line text and auto-height (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/column-headers/#auto-header-height\" rel=\"nofollow\">Auto Header Height</a>)</li><li>AG-4229 - Allow deselecting cells from the selected cell range by CTRL+Click a cell or SHIFT+Drag to a cell (see <a href=\"https://www.ag-grid.com/archive/28.0.0/javascript-data-grid/range-selection/#range-deselection\" rel=\"nofollow\">Range Deselection</a>)</li><li>AG-3838 - Allow Bar / Column series types to support number and time axis</li></ul></ul>", "breaking changes": "<p><strong>Breaking Changes:</strong></p><ul><li>AG-6149 - <strong>Angular v12+</strong>: Remove <code>.withComponents()</code> from module declaration of <code>AgGridModule</code> as this is no longer required. <strong>Angular v8-11</strong>: Switch to the legacy version of the AG Grid angular libraries. For packages switch to <code>ag-grid-angular-legacy</code>. For modules switch to <code>@ag-grid-community/angular-legacy</code>. Your application will no longer compile with our default Angular libraries which have a minimum dependency of Angular v12. For full details see: <a href=\"https://www.ag-grid.com/archive/28.0.0/angular-data-grid/angular-compatibility/\" rel=\"nofollow\">Angular Compatibility</a>.</li><li>AG-6948 - <code>@ag-grid-community/all-modules</code> and <code>@ag-grid-enterprise/all-modules</code> have been removed. If using <code>@ag-grid-community/all-modules</code> you should switch to use <a href=\"https://www.ag-grid.com/javascript-data-grid/packages/\" rel=\"nofollow\">Packages</a> instead. If you want to use individual modules for a small package size, use <a href=\"https://www.ag-grid.com/javascript-data-grid/modules/\" rel=\"nofollow\">Modules</a>.</li><li>AG-6587 - In CSRM, sorting a group column now sorts the individual columns and vice versa. Sort models saved prior to this change will now behave differently, as sorted grouped columns will now apply the sort to the group column.</li><li>AG-5203 - The length getter and setter as well as the forEach function have been removed from the <code>IAggFuncParams</code>. As a result aggregate functions expecting an array type as the parameter are no longer supported.</li><li>AG-6848 - When using SSRM Infinite Scroll, filtering the grid selectively refreshes only the groups affected by the filters change. If using SSRM with tree data, filtering happens client-side. The old behavior was to purge all rows from the root level on filter change. The old behaviour of always purging all rows is available by enabling the grid option <code>serverSideFilterAllLevels</code>.</li><li>AG-3069 - The properties <code>appendContent</code>, <code>prependContent</code> and <code>getCustomContentBelowRow</code> of the Excel Export now use the <code>ExcelRow[]</code> interface and no longer use <code>ExcelCell[][]</code>.</li><li>AG-6707 - The grid no longer fires the <code>rowDataChanged</code> event, which is now deprecated. Instead it fires <code>rowDataUpdated</code> on all occasions as well as <code>filter.onNewRowsLoaded</code> on all occasions.</li></ul>", "deprecations": "<p><strong>Deprecations:</strong></p><p><u>Grid Options</u></p><ul><li>AG-6848 - <code>serverSideSortingAlwaysResets</code> (use <code>serverSideSortAllLevels</code> instead)</li><li>AG-6848 - <code>serverSideFilteringAlwaysResets</code> (use <code>serverSideFilterAllLevels</code> instead)</li><li>AG-6539 - <code>processSecondaryColDef()</code> (use <code>processPivotResultColDef()</code> instead)</li><li>AG-6539 - <code>processSecondaryColGroupDef()</code> (use <code>processPivotResultColGroupDef()</code> instead)</li><li>AG-6858 - <code>serverSideStoreType=full/partial</code> (use <code>serverSideInfiniteScroll=true/false</code> instead)</li></ul><p><u>Column API</u></p><ul><li>AG-6539 - <code>getSecondaryPivotColumn()</code> (use <code>getPivotResultColumn()</code> instead)</li><li>AG-6539 - <code>setSecondaryColumns()</code> (use <code>setPivotResultColumns()</code> instead)</li><li>AG-6539 - <code>getSecondaryColumns()</code> (use <code>getPivotResultColumns()</code> instead)</li><li>AG-6539 - <code>getPrimaryColumns()</code> (use <code>getColumns()</code> instead)</li></ul><p><u>Grid API</u></p><ul><li>AG-6858 - <code>getServerSideStoreParams(GetServerSideStoreParamsParams) </code> (use <code>getServerSideGroupLevelParams(GetServerSideGroupLevelParamsParams)</code> instead)</li><li>AG-6858 - <code>getServerSideStoreState()</code> (use<code>getServerSideGroupLevelState()</code> instead)</li><li>AG-6858 - <code>refreshServerSideStore()</code> (use <code>refreshServerSide()</code> instead)</li><li>AG-6858 - <code>serverSideSortingAlwaysResets</code> (use <code>serverSideSortAllLevels</code> instead)</li></ul><p><u>IsApplyServerSideTransactionParams </u></p><ul><li>AG-6858 - <code>storeInfo</code> (use <code>groupLevelInfo</code> instead)</li></ul><p><u>LoadSuccessParams </u></p><ul><li>AG-6858 - <code>storeInfo</code> (use <code>groupLevelInfo</code> instead)</li></ul>"}, {"release version": "<p><b>17th May 2022 - Grid v27.3.0 (Charts v5.3.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>New Getting Started videos for <a href=\"https://www.ag-grid.com/archive/27.3.0/angular-data-grid/videos/\" rel=\"nofollow\"> Angular </a>, <a href=\"https://www.ag-grid.com/archive/27.3.0/vue-data-grid/videos/\" rel=\"nofollow\"> Vue </a>, <a href=\"https://www.ag-grid.com/archive/27.3.0/javascript-data-grid/videos/\" rel=\"nofollow\"> Javascript </a>, and <a href=\"https://www.ag-grid.com/archive/27.3.0/react-data-grid/videos/\" rel=\"nofollow\"> React </a></li><li>Accessibility Enhancements</li><ul><li>AG-4392 - Add aria-label on a column header to announce the keyboard shortcuts for column menu / sorting</li><li>AG-4273 - <PERSON><PERSON> announcing the expanded / collapsed state of group and master rows</li><li>AG-4223 - <PERSON><PERSON> keyboard navigation in and out of the detail grid</li><li>AG-2174 - <PERSON><PERSON> pressing CTRL + SHIFT+ ARROW key to select all cells in the direction of the ARROW key press</li></ul><li>Chart Axis Label Improvements</li><ul><li>AG-5243 - Allow auto rotation of axis labels to avoid collisions for category / number / time axes (see <a href=\"https://www.ag-grid.com/archive/27.3.0/javascript-charts/axes/#label-rotation--skipping\" rel=\"nofollow\"> Label Rotation &amp; Skipping </a>)</li><li>AG-6207 - Allow adjusting the number of axis labels displayed for all continuous axis types to prevent axis labels collisions</li><li>AG-3559 - Allow group category axis labels to be dropped to avoid axis label collisions in integrated and standalone charting</li></ul><li>Miscellaneous</li><ul><li>AG-3241 - Allow tri-state checkbox group selection in Tree Data (see <a href=\"https://www.ag-grid.com/archive/27.3.0/javascript-data-grid/tree-data/#group-selection\" rel=\"nofollow\"> Tree Data Group Selection </a>)</li><li>AG-2883 - Add option to remove the value header column row if only adding one value to pivoting (see <a href=\"https://www.ag-grid.com/archive/27.3.0/javascript-data-grid/pivoting/#hiding-repeated-value-column-labels\" rel=\"nofollow\"> Hiding Repeated Value Column Labels </a>)</li><li>AG-6762 - Allow copying selected rows when using range selection and only a single cell is selected(see <a href=\"https://www.ag-grid.com/archive/27.3.0/javascript-data-grid/clipboard/#mixed-copying-cell-ranges--rows\" rel=\"nofollow\"> Mixed Copying Cell Ranges &amp; Rows </a>)</li><li>AG-5263 - Allow defining a defaultAggFunc for a column to be used when the column is used as a value column (see <a href=\"https://www.ag-grid.com/archive/27.3.0/javascript-data-grid/aggregation-other/#default-aggregation-function\" rel=\"nofollow\"> Default Aggregation Function </a>)</li></ul></ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>19th Apr 2022 - Grid v27.2.0 (Charts v5.2.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>AG-1228 - Allow filtering on aggregated group level values (see <a href=\"https://www.ag-grid.com/archive/27.2.0/javascript-data-grid/aggregation-filtering/#filtering-group-aggregations\" rel=\"nofollow\">Filtering Group Aggregations</a>)</li><li>AG-2168 - Allow filtering in secondary columns when pivoting (see <a href=\"https://www.ag-grid.com/archive/27.2.0/javascript-data-grid/pivoting/#filtering-on-secondary-columns\" rel=\"nofollow\">Filtering on Secondary Columns</a>)</li><li>AG-4571 - Allow saving and restoring ColumnState with Primary/Secondary Columns in pivot mode (see <a href=\"https://www.ag-grid.com/archive/27.2.0/javascript-data-grid/pivoting/#saving--restoring-column-state-with-pivot\" rel=\"nofollow\">Saving &amp; Restoring Column State with Pivot</a>)</li></ul>", "breaking changes": "", "deprecations": "<p><strong>Deprecations:</strong></p><p><u>Grid Option Callbacks</u></p><p>AG-3353</p><ul><li><code>groupRowAggNodes</code> (use <code>getGroupRowAgg</code> instead)</li><li><code>defaultGroupOrderComparator</code> (use <code>initialGroupOrderComparator</code> instead)</li><li><code>postSort</code> (use <code>postSortRows</code> instead)</li><li><code>localeTextFunc</code> (use <code>getLocaleText</code> instead)</li><li><code>isFullWidthCell</code> (use <code>isFullWidthRow</code> instead)</li></ul><p>AG-6540</p><ul><li><code>suppressKeyboardEvent</code> is now deprecated. You can set this on a per-column basis via <code>colDef.suppressKeyboardEvent</code>. If you need this to be set for every column, set it via the <code>defaultColDef.suppressKeyboardEvent</code> property.</li></ul>"}, {"release version": "<p><b>15th Mar 2022 - Grid v27.1.0 (Charts v5.1.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>AG-3284 - Integrated Combination Charts (see <a href=\"https://www.ag-grid.com/archive/27.1.0/javascript-data-grid/integrated-charts-api-range-chart/#combination-charts\" rel=\"nofollow\">Combination Charts</a>)</li><li>AG-6440 - Chart Options and Themes Reference Improvements (see <a href=\"https://www.ag-grid.com/archive/27.1.0/javascript-charts/api/\" rel=\"nofollow\">Options Reference</a> and <a href=\"https://www.ag-grid.com/archive/27.1.0/javascript-charts/api-themes/\" rel=\"nofollow\">Themes Reference</a>)</li><li>AG-4030 - Reactive Default Column Definitions (see <a href=\"https://www.ag-grid.com/archive/27.1.0/react-data-grid/column-definitions/#default-column-definitions\" rel=\"nofollow\">Custom Column Types</a>)</li>", "breaking changes": "", "deprecations": "<p><strong>Deprecations:</strong></p><p><u>Grid</u></p><ul><li>AG-6337 - <code>gridOptions.clipboardDeliminator</code> is deprecated, use <code>gridOptions.clipboardDelimiter</code> instead.</li><li>AG-6453 - <code>ICellEditorParams.key</code> is deprecated, use <code>ICellEditorParams.eventKey</code> instead.</li><li>AG-6394 - <code>gridOptions.immutableData</code> and <code>gridOptions.getRowNodeId()</code> are deprecated. Instead implement <code>getRowId()</code>, and the grid will then also treat the data as immutable data automatically.</li><li>AG-6490 - <code>PopupTextCellEditor</code> and <code>PopupSelectCellEditor</code> are deprecated. You can achieve the same behavior by setting <code>cellEditorPopup=true</code> on <code>TextCellEditor</code> (to get <code>PopupTextCellEditor</code>) and <code>SelectCellEditor</code> (to get <code>PopupSelectCellEditor</code>).</code></li></ul><p><u>Standalone Charts</u></p><ul><li>AG-6253 - Series options <code>yKeys</code>, <code>strokes</code> and <code>fills</code> are deprecated, instead use distinct series option objects with singular <code>yKey</code>, <code>stroke</code> and <code>fill</code> properties.</li></ul>"}, {"release version": "<p><b>8th Feb 2022 - Grid v27.0.0 (Charts v5.0.0)</b></p>", "feature highlights": "<p><strong>Feature Highlights:</strong></p><ul><li>AG-6020 - 100% React Rendering (see <a href=\"https://www.ag-grid.com/archive/27.0.0/react-data-grid/reactui/\" rel=\"nofollow\">React Rendering</a>)</li><li>Typescript Example Support</li><ul><li>AG-6004 - Typescript Examples in Example Runner</li><li>AG-6322 - Angular Typescript Examples Strict Compliant</li></ul><li>Accessibility Improvements</li><ul><li>AG-6180 - Allow screen reader announcements of group column items in row group panel and keyboard navigation for managing row group panel items</li><li>AG-6245 - <PERSON><PERSON> announcing the filter operation select element as a combobox (it is currently simply announced as \"clickable\"</li><li>AG-6287 - <PERSON><PERSON> using the keyboard to move columns in the column tool panel to the row groups, values, column labels panels</li></ul><li>Miscellaneous</li><ul><li>AG-2358 - <PERSON><PERSON> changing the title and formatting the number value of the default status bar components</li><li>AG-3565 - Allow group column header values to be considered when performing column autosize</li><li>AG-4839 - Allow setting the horizontal position of the column (start,end,middle) when using ensureColumnVisible</li></ul></ul>", "breaking changes": "<p> <b>Breaking Changes:</b> </p> <p>  <ul><li><b>AG Grid 27 does not support Polymer, Angular 7, AngularJS and Internet Explorer 11.</b> If you are using Polymer, Angular 7, AngularJS or need to support Internet Explorer 11 please use AG Grid 26, which will be a long-term support release. You will still be able to report issues against AG Grid 26 and we will release new minor versions of AG Grid 26 to address these.</p></li></ul> <p><u>React</u></p><ul><li>AG-6321 - <code>getReactContainerClasses()</code> - no longer needed as React Portals is not used anymore.</li><li>AG-6186 - JavaScript Functional Components are no longer supported when using React. Instead use either React Functional Components or JavaScript Class Components.</li></ul><p><u>Server-Side Row Model</u></p><ul><li>AG-6029 - <code>getRowNodeId()</code> now required when Row Selection is enabled in the Server-Side Row Model.</li></ul><p><u>React / Angular / Vue</u></p><ul><li>AG-6019 - <code>get{comp-name}Instance[s]()</code> (e.g. <code>getCellRendererInstances</code> no longer needed). The methods now return the React / Angular / Vue component if one exists.</li></ul><p><u>Clipboard</u></p><ul><li>AG-2824 - The following methods now accept parameterised objects: <code>copySelectedRowsToClipboard(params?: IClipboardCopyRowsParams)</code> <code>copySelectedRangeToClipboard(params?: IClipboardCopyParams)</code>.</li><li>AG-5902 - Cell values copied exclusively from the same row are now pasted in the same row (previously each copied value was pasted on a separate row). Cell values are now copied in the order they appear in the grid (previously cell values were copied in the order of selection).</li></ul><p><u>Integrated Charts</u></p><ul><li>AG-6111 - Typescript users will need to replace the <code>ChartType</code> enum to the corresponding the string literal, i.e. <code>ChartType.GroupedColumn</code> should be changed to <code>'groupedColumn'</code>.</li></ul><p><u>Rendering</u></p><ul><li>AG-5718 - The following methods were previously deprecated and have now been removed:</li><ul><li><code>gridApi.refreshView()</code> - use <code>gridApi.refreshCells()</code> or <code>gridApi.redrawRows()</code> instead.</li><li><code>gridApi.refreshRows()</code> - use <code>gridApi.refreshCells()</code> or <code>gridApi.redrawRows()</code> instead.</li><li><code>gridApi.rowDataChanged</code> - use <code>rowNode.setRowData(newData)</code> to set value on a particular node, or <code>redrawRows</code> to refresh everything.</li><li><code>gridApi.refreshGroupRows()</code> - use <code>gridApi.refreshCells()</code> instead, as <code>gridApi.refreshCells()</code> now does dirty checking, it will only refresh cells that have changed, so it should not be necessary to only refresh the group rows.</li></ul></ul><p><u>Sorting</u></p><ul><li>AG-6141 - <code>gridApi.setSortModel()</code> and <code>gridApi.getSortModel()</code> were previously deprecated and have now been removed. Sort information is now part of Column State, use <code>columnApi.applyColumnState()</code> and <code>columnApi.getColumnState()</code> instead.</li></ul><p><u>Filters</u></p><ul><li>AG-6033, AG-5268, AG-5169 - The following Filter Options / API's were previously deprecated and have now been removed:<p><u>GridOptions</u></p><ul><li><code>floatingFilter</code> - removed, use <code>ColDef.floatingFilter</code> instead</li><li><code>enableOldSetFilterModel</code> - removed, use the current model structure for <code>SetFilter</code> instead</li></ul><p><u>GridApi</u></p><ul><li><code>isAdvancedFilterPresent()</code> - removed, use <code>isColumnFilterPresent()</code> instead</li><li><code>isAdvancedFilterPresent()</code> - removed, use <code>isColumnFilterPresent()</code> instead.</li><li><code>getFilterApiForColDef()</code> - removed, use <code>getFilterInstance()</code> instead.</li><li><code>getFilterApi()</code> - removed, use <code>getFilterInstance()</code> instead.</li></ul><p><u>IFilterParams</u></p><ul><li><code>valueGetter()</code> callback arguments have changed; they are no longer a solitary <code>RowNode</code>, but an object conforming to the <code>ValueGetterFunc</code> interface contract for consistency with other <code>valueGetter</code> parameters across the grid.</li></ul><p><u>IFloatingFilterParams</u></p><ul><li><code>onFloatingFilterChanged()</code> - removed, use&nbsp;<code>parentFilterInstance()</code> callback instead.</li></ul><p><u>IProvidedFilterParams</u></p><ul><li><code>clearButton</code>, <code>resetButton</code> and <code>applyButton</code> - removed, use <code>buttons</code> instead.</li></ul><p><u>IScalarFilterParams</u></p><ul><li><code>nullComparator</code> + <code>NullComparator</code> - removed, use <code>includeBlanksInEquals</code>, <code>includeBlanksInLessThan</code> and <code>includeBlanksInGreaterThan</code> instead.</li></ul><p><u>Filter</u></p><ul><li>Interface completely removed; use IFilter instead.</li></ul><p><u>ProvidedFilter</u></p><ul><li><code>onFilterChanged()</code> - removed, use <code>api.onFilterChanged()</code> instead.</li></ul><p><u>ISetFilterParams</u></p><ul><li><code>syncValuesLikeExcel</code> - removed; this has been the default since deprecation in 22.0.0.</li><li><code>selectAllOnMiniFilter</code> - removed; this has been the default since deprecation in 22.0.0.</li><li><code>suppressSyncValuesAfterDataChange</code> - removed.</li><li><code>suppressRemoveEntries</code> - removed.</li><li><code>newRowsAction</code> has now been removed; the previous <code>'keep'</code> setting is now the default.</li></ul><p><u>SetFilter</u></p><ul><li><code>setLoading</code> is now handled automatically.</li><li><code>selectEverything()</code> - removed, use <code>setModel()</code> instead.</li><li><code>selectNothing()</code> - removed, use <code>setModel()</code> instead.</li><li><code>unselectValue()</code> - removed, use <code>setModel()</code> instead.</li><li><code>selectValue()</code> - removed, use <code>setModel()</code> instead.</li><li><code>isValueSelected()</code> - removed, use <code>getModel()</code> instead.</li><li><code>isEverythingSelected()</code> - removed, use <code>getModel()</code> instead.</li><li><code>isNothingSelected()</code> - removed, use <code>getModel()</code> instead.</li><li><code>getUniqueValueCount()</code> - removed, use <code>getValues()</code> instead.</li><li><code>getUniqueValue()</code> - removed, use <code>getValues()</code> instead.</li></ul></li></ul>", "deprecations": "<p><b>Deprecations:</b></p> <p><u>Components</u></p><ul><li>AG-6186 - <code>{comp-name}Framework</code> (e.g. <code>cellRendererFramework</code>) - no longer required for assigning Custom Framework Components, use <code>{comp-name}</code> (e.g. <code>cellRenderer</code>) instead. Likewise grid property <code>frameworkComponents</code> is deprecated, use grid property <code>components</code> instead.</li></ul><p><u>React</u></p><ul><li>AG-6186 - <code>gridOptions.reactUi</code> is no longer used as React UI is on by default.</li></ul><p><u>Keyboard Navigation</u></p><ul><li>AG-5953 - <code>gridOptions.suppressCellSelection</code> is deprecated, use <code>gridOptions.suppressCellFocus</code> instead.</li><li>AG-4624 - The following interfaces have deprecated properties:</li><ul><li><code>StartEditingCellParams { keyPress: number }</code> use <code>StartEditingCellParams { key: string }</code> instead.</li><li>ICellEditorParams { keyPress: number } use <code>ICellEditorParams { key: string }</code> instead.</li><li><code>IRichCellEditorParams { keyPress: number }</code> use <code>IRichCellEditorParams { key: string }</code> instead.</li><li><code>ILargeTextEditorParams { keyPress: number }</code> use <code>ILargeTextEditorParams { key: string }</code> instead.</li><li><code>ISelectCellEditorParams { keyPress: number }</code> use <code>ISelectCellEditorParams { key: string }</code> instead.</li><li><code>ITextCellEditorParams { keyPress: number }</code> use <code>ITextCellEditorParams { key: string }</code> instead.</li><li><code>NavigateToNextCellParams { key: number }</code> use <code>NavigateToNextCellParams { key: string }</code> instead.</li></ul></ul><p><u>Columns</u></p><ul><li>AG-6022 - <code>gridApi.getOriginalColumnGroup()</code> is deprecated, use <code>gridApi.getProvidedColumnGroup()</code> instead.</li></ul><p><u>Filters</u></p><ul><li>AG-5044 - <code>ITextFilterParams.textCustomComparator</code> is deprecated; use <code>ITextFilterParams.textMatcher</code> instead.</li></ul>"}, {"release version": "<p><b>17th Nov 2021 - Grid v26.2.0 (Charts v4.2.0)</b></p>", "feature highlights": "<p><b>Feature Highlights:</b></p><ul><li> React UI - Add support for all customisations (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/react-data-grid/reactui/#all-customisations-supported\">All Customisations Supported</a>) </li> <li>Filter Enhancements</li> <ul> <li> AG-2093 - Allow fast tabbing through floating filter inputs when focused (try on our <a rel=\"nofollow\" href=\"https://www.ag-grid.com/example.php\">Demo Page</a>) </li> <li> AG-3453 - Add support for range filters (i.e. two values, \"from\" and \"to\") in custom filter options (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/react-data-grid/filter-provided-simple/#custom-filter-options\">Custom Filter Options</a>) </li> <li> AG-5576 - Allow disabling filter inputs so users can see the filter conditions/selection but not modify it (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/react-data-grid/filter-api/#read-only-filter-ui\">Read-only Filter UI</a>) </li> <li> AG-5685 - Allow maxValidYear to be set when using a date filter (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/react-data-grid/filter-date/#example-date-filter\">Date Filter Example</a>) </li> <li> AG-5925 - Display a red border around the date picker filter input when invalid input is provided (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/react-data-grid/filter-date/#example-date-filter\">Date Filter Example</a>) </li> </ul> <li>Accessibility Improvements</li> <ul> <li>AG-5878 - Add ARIA labels to the set filter virtual list container</li> <li>AG-5879 - Allow full-width rows to have valid ARIA fields for static accessibility tests</li> (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/javascript-data-grid/accessibility/\">Accessibility</a>) </ul> <li>Miscellaneous</li> <ul> <li> AG-5870 - Add Bar Sparkline (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/react-data-grid/sparklines-bar-customisation/\">Bar Sparkline</a>) </li> <li> AG-5881 - Add Vue3 support to Standalone Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.2.0/vue-charts/overview/\">Vue Charts</a>) </li> </ul></ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>1st Oct 2021 - Grid v26.1.0 (Charts v4.1.0)</b></p>", "feature highlights": "<p><b>Feature Highlights:</b></p> <ul> <li> Sparklines - Introducing our new built-in Sparkline Cell Renderer (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/javascript-data-grid/sparklines-overview/\">Sparklines Overview</a>) </li> <li>Grid UX Enhancements</li> <ul> <li> AG-4139 - Allow drag &amp; drop to reorder columns inside Columns Tool Panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/javascript-data-grid/tool-panel-columns/\">Columns Tool Panel</a>) </li> <li> AG-3986 - Entire Row Dragging without the need for a drag handle (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/javascript-data-grid/row-dragging/#entire-row-dragging\">Entire Row Dragging</a>) </li> </ul> <li>Accessibility Improvements</li> <ul> <li>AG-4661 - Add ARIA form label to checkboxes in column tool panel</li> <li>AG-5299 - Allow correct announcements for the column menu tabs</li> <li>AG-2932 - Add ARIA label to filter tool panel - filter columns input</li> <li>AG-5727 - Allow column menu to indicate to screen readers it's a dialog</li> <li>AG-5305 - Allow focus to move from the sidebar button into the active tool panel</li> <li>AG-5082 - Allow filter tool panel items to be expanded using the SPACE key and announce their current expanded / collapsed state</li> <li>AG-5747 - Allow column tool panel items to be announced with their index in the columns list and provide labels to checkboxes</li> <li>AG-5731 - Allow announcing number of matching records after column filtering</li> <li>AG-4705 - Allow JAWS screen reader to announce visible records when using pagination after previous/next button clicked</li> <li>AG-5246 - Resolve broken ARIA label reference error for column header cell select-all checkbox</li> (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/javascript-data-grid/accessibility/\">Accessibility</a>) </ul> <li>Miscellaneous</li> <ul> <li> AG-3116 - Add support for Series Highlighting in Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/javascript-charts/series-highlighting/\">Series Highlighting</a>) </li> <li> AG-5706 - Add support for React-based column header components in ReactUI mode (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/react-data-grid/reactui/#example-headers\">ReactUI - Column Header Components</a>) </li> <li> AG-3680 - Allow disabling the Fill Handle on a per-column basis (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.1.0/javascript-data-grid/range-selection-fill-handle/#skipping-columns-in-the-fill-operation\">Skipping Columns in the Fill Operation</a>) </li> </ul> </ul>", "breaking changes": "", "deprecations": "<p><b>Deprecations:</b></p> <p><u>Grid Options</u></p> <ul> <li><code>enableMultiRowDragging</code> (use <code>rowDragMultiRow</code> instead)</li> <li><code>colWidth</code> (use <code>defaultColDef.width</code> instead)</li> <li><code>minColWidth</code> (use <code>defaultColDef.minWidth</code> instead)</li> <li><code>maxColWidth</code> (use <code>defaultColDef.maxWidth</code> instead)</li> </ul>"}, {"release version": "<p><b>18th Aug 2021 - Grid v26.0.0 (Charts v4.0.0)</b></p>", "feature highlights": "<p> <b>Feature Highlights:</b> </p> <ul> <li> React UI - The next generation of AG Grid React with the UI written purely in React (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/react-data-grid/reactui/\" > React UI </a> ) </li> <li>Grid UX Enhancements</li> <ul> <li> AG-3406 - Allow user to resize/collapse the different areas of the Columns Tool Panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/tool-panel-columns/\" > Columns Tool Panel </a> ) </li> <li> AG-1945 - Allow setting Tool Panel width (current / min / max / initial) (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/side-bar/#sidebardef-configuration\" > Sidebar Configuration </a> ) </li> <li> AG-4028 - Allow Set Filter popup to be resized (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/filter-set/\" > Set Filter </a> ) </li> </ul> <li>Row Grouping Enhancements</li> <ul> <li> AG-5484 - Reworked Documentation (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/grouping/\" > Row Grouping </a> ) </li> <li> AG-5524 - Simplified configuration of Row Grouping Display Types (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/grouping-display-types/\" > Row Grouping Display Types </a> ) </li> <li> AG-1153 - Allow sort comparators defined on columns to also sort group columns (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/grouping-sorting/#custom-group-sorting\" > Custom Group Sorting </a> ) </li> <li> AG-2139 - Add option to maintain group order when sorting on non-group columns (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/grouping-sorting/#maintain-group-order\" > Maintain Group Order </a> ) </li> <li> AG-5582 - Allow custom cell renderers to be used with Group Footer Rows (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/grouping-footers/#customising-footer-cells\" > Customising Footer Cells </a> ) </li> </ul> <li>Charts</li> <ul> <li> AG-4880 - Add series marker labels for Bubble / Scatter charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-charts/scatter-series/#example-bubble-chart-labels\" > Bubble Chart Labels </a> ) </li> <li> AG-3634 - Add support for series marker labels in category series (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-charts/line-series/#example-basic-line-labels\" > Line Chart Labels </a> ) </li> </ul> <li>Miscellaneous</li> <ul> <li> AG-5569 - Vue 3 Reactivity and Composition API Support, Improved Vue 3 Documentation </li> <li>AG-5373 - Add Vue 3 examples to the Example Runner</li> <li> AG-5607 - Enhance Row Auto Height Support (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/row-height/#auto-row-height\" > Auto Row Height </a> ) </li> <li>AG-5625 - Update and Improve Types on Angular Interfaces</li> </ul> </ul>", "breaking changes": "<p><strong>Breaking Changes:</strong></p><p><u>Core Grid</u></p><ul><li>AG-5392 - Now when setting / updating Column Definitions, the order of the Columns in the grid will always match the order of the Column Definitions. Prior to v26, <code>applyColumnDefOrder</code> was used to achieve this, however this is now the default behaviour. To turn off this behaviour, i.e. to maintain the order of Columns between updates to Column Definitions, set the grid property <code>maintainColumnOrder=true</code> (see <a href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/column-updating-definitions/#maintain-column-order\" rel=\"nofollow\"> Maintain Column Order </a> )</li><li>AG-5534 - The <code>AnimationQueueEmptyEvent</code> has been removed, the grid API method <code>isAnimationFrameQueueEmpty()</code> can be used instead.</li><li>AG-4952 - <code>.cjs</code> are self-contained units preventing LicenseManager from recognizing the license key, these files previously included all AG Grid related dependencies. These files now only contain the relevant code for that module, which is the correct behaviour</li><li>AG-5607 - The row auto-height behavior has been changed. When a column uses auto height (<code>colDef.autoHeight=true</code>), there is no need to call <code>gridApi.resetRowHeights()</code> in the <code>onColumnResized</code> event handler anymore to force the rows to update their row height after a column resize operation, as the grid will automatically resize the rows when it detects a height change. From v26.0 onward, keeping these calls to <code>gridApi.resetRowHeights()</code> causes flicker as rows render themselves twice instead of once. Please remove calls to <code>gridApi.resetRowHeights()</code> in <code>onColumnResized</code> event handler to avoid flicker in row re-rendering.</li><li>AG-5590 - Cell Renderers are completely destroyed when editing starts. This is in line with how Components work in frameworks (Angular, React etc), that when you take the Component out of the DOM, that is in effect destroying the component. So when editing starts (Cell Editor inserted into the Cell), the Cell Renderer is destroyed.</li></ul><p><u>Integrated Charts</u></p><ul><li>AG-5605 - The previously deprecated <code>processChartOptions()</code> callback has now been removed. Please use <a href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/integrated-charts-customisation/\" rel=\"nofollow\"> Theme Based Configuration </a> instead</li><li>AG-5558 - <code>getChartImageDataURL()</code> has been removed from the <code>ChartModel</code> and it is now available directly through the grid API (see <a href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/integrated-charts-api/#downloading-chart-image\" rel=\"nofollow\"> Downloading Chart Image </a> )</li><li>AG-5447 - The chart instance has been removed from the <code>ChartModel</code> to support serialisation, use <code>gridApi.getChartRef(chartId)</code> instead (see <a href=\"https://www.ag-grid.com/archive/26.0.0/javascript-data-grid/integrated-charts-events/#accessing-chart-instance\" rel=\"nofollow\"> Accessing Chart Instance </a> )</li></ul><p><u>Standalone Charts</u></p><p>The following Standalone Chart options were previously deprecated and have now been removed</p><p><u>Chart</u></p><ul><ul><ul><li><code>tooltipTracking</code> (use <code>tooltip.tracking</code> instead)</li><li><code>tooltipClass</code> (use <code>tooltip.class</code> instead)</li></ul></ul></ul><p><u>Series</u></p><ul><ul><ul><li><code>tooltipEnabled</code> (use <code>tooltip.enabled</code> instead)</li><li><code>tooltipRenderer</code> (use <code>tooltip.renderer</code> instead)</li></ul></ul></ul><p><u>Legend</u></p><ul><ul><li><code>layoutHorizontalSpacing</code> (use <code>item.paddingX</code> instead)</li><li><code>layoutVerticalSpacing</code> (use <code>item.paddingY</code> instead)</li><li><code>itemSpacing</code> (use <code>item.marker.padding</code> instead)</li><li><code>markerShape</code> (use <code>item.marker.shape</code> instead)</li><li><code>markerSize</code> (use <code>item.marker.size</code> instead)</li><li><code>strokeWidth</code> (use <code>item.marker.strokeWidth</code> instead)</li><li><code>color</code> (use <code>item.label.color</code> instead)</li><li><code>fontStyle</code> (use <code>item.label.fontStyle</code> instead)</li><li><code>fontWeight</code> (use <code>item.label.fontWeight</code> instead)</li><li><code>fontSize</code> (use <code>item.label.fontSize</code> instead)</li><li><code>fontFamily</code> (use <code>item.label.fontFamily</code> instead)</li></ul></ul>", "deprecations": "<p> <b>Deprecations:</b> </p> <p> <u>Grid Options</u> </p> <ul> <li> <code>groupMultiAutoColumn</code> (use <code>groupDisplayType='multipleColumns'</code> instead) </li> <li> <code>groupUseEntireRow</code> (use <code>groupDisplayType='groupRows'</code> instead) </li> <li> <code>groupSuppressAutoColumn</code> (use <code>groupDisplayType='custom'</code> instead) </li> <li> <code>defaultGroupSortComparator</code> (use <code>defaultGroupOrderComparator</code> instead) </li> </ul> <p> <u>Column Properties</u> </p> <ul> <li> <code>pinnedRowCellRenderer</code>, <code>pinnedRowCellRendererFramework</code> and <code>pinnedRowCellRendererParams</code> (Please use <code>cellRendererSelector</code> instead if you want a different Cell Renderer / Params for Pinned Rows) </li> </ul>"}, {"release version": "<p><b>27th Apr 2021 - Grid v25.3.0 (Charts v3.3.0)</b></p> <p>Minor release with bug fixes.</p>", "feature highlights": "", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>27th Apr 2021 - Grid v25.2.0 (Charts v3.2.0)</b></p> <p>Minor release with bug fixes.</p>", "feature highlights": "<p><b>Feature Highlights:</b></p> <ul> <li>Excel Export Enhancements</li> <ul> <li>AG-2432 - Allow exporting images to Excel in the exported grid data cells</li> <li>AG-1504 - Add author value to generated XML Excel to avoid edit error</li> <li>AG-2553 - Allow customising of group column headers when exporting to Excel</li> <li>AG-2333 - Allow inserting hyperlinks in the cells of the exported Excel file</li> <li>AG-2303 - Allow exporting formulas to Excel</li> <li>AG-5178 - Allow exporting an image in Excel export above and below the grid</li> <li>AG-3439 - Allow carriage-returns / multiple line formatting when exporting to Excel</li> <li>AG-3833 - Allow column spanning with Excel export</li> <li>AG-5109 - Allow column spanning with ExcelAdd property to change the default font size</li> <li>AG-2370 - Allow exporting multiple grids to a single Excel file by supporting multiple sheets</li> <li>AG-5179 - Allow setting page size and layout orientation for the Excel export file</li> <li>AG-5217 - Add support for Headers and Footers in the Excel export</li> <li>AG-4471 - Allow exporting cell date-type values to Excel date format</li> (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/25.2.0/javascript-grid/excel-export/\">Excel Export</a>) </ul> <li>AG-3379 - Logarithmic Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/25.2.0/javascript-charts/axes/#log-axis\">Log Axis</a>)</li> </ul>", "breaking changes": "", "deprecations": "<p><b>Deprecations:</b></p> <p><u>ExcelExportParams</u></p> <ul> <li>columnGroups (use skipColumnGroupHeaders)</li> <li>skipGroups (use skipRowGroups)</li> <li>skipHeader (use skipColumnHeaders)</li> <li>columnGroups (use prependContent)</li> <li>customFooter (use appendContent)</li> </ul> <p><u>Grid Options</u></p> <ul> <li>suppressColumnStateEvents (no longer required)</li> </ul>"}, {"release version": "<p><b>19th Feb 2021 - Grid v25.1.0 (Charts v3.1.0)</b></p> <p>Minor release with bug fixes.</p>", "feature highlights": "<p><b>Feature Highlights:</b></p> <ul> <li> AG-3028 - Add Treemap Chart Series (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/25.1.0/javascript-charts/treemap-series/\">Treemap Series</a>) </li> </ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p><b>6th Jan 2021 - Grid v25.0.0 (Charts v3.0.0)</b></p>", "feature highlights": "<p><b>Feature Highlights:</b></p> <ul> <li> AG-2837 - Major Server-side Row Model enhancements including: <ul> <li> Full CRUD support via Transactions, including Asynchronous Transactions for High Frequency updates. </li> <li> Option to turn off Infinite Scrolling and load all rows at a particular level. </li> <li> Client side Sorting and Filtering. </li> <li> Silent refreshes (i.e. don't show Loading Spinner). </li> <li> Preserving Group State during Refresh. </li> <li> Retry of failed data loads. </li> <li> Configure different SSRM properties (eg Block Size) for each group level. </li> </ul> As the changes to SSRM are major, we recommend SSRM users to review the <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/25.0.0/documentation/javascript/server-side-model/\">SSRM documentation</a>. </li> <li> AG-4787 - Integrated Charts Cross Filtering (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/25.0.0/documentation/javascript/integrated-charts-cross-filtering/\">Cross Filtering</a>) </li> <li> AG-4600 - Clipboard performance improvement, as grid now uses native Clipboard API. </li> <li> AG-4753 - New property <code>showOpenedGroup</code>, to allow showing the name of the open group in the group column. See <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/25.0.0/documentation/javascript/grouping/#showing-open-groups\">Show Open Groups</a>. </li> </ul>", "breaking changes": "<p><b>Breaking Changes:</b></p> <p><u>Server-side Row Model</u></p> <ul> <li> New property <code>serverSideStoreType</code> must be set to <code>partial</code> for backwards compatibility. If not set, the default <code>full</code> is used, which turns off Infinite Scrolling. Infinite Scrolling is now a feature you must opt into. </li> <li> The undocumented attribute <code>childrenCache</code> property of Row Node has been renamed to <code>childStore</code>. The old name didn't make sense with the refactor of how the SSRM works with different store types. </li> </ul>", "deprecations": "<p><b>Deprecations:</b></p> <p><u>Server-side Row Model</u></p> <ul> <li> Grid API <code>refreshServerSideStore(params)</code> should now be used instead of <code>purgeServerSideCache()</code>. </li> <li> The success callbacks <code>successCallback</code> and <code>failCallback</code> are replaced with the methods <code>success</code> and <code>fail</code>. The new methods serve the same purpose as the old methods, however the new 'success' method uses a <code>params</code> object rather than listing individual parameters as this is more extensible. The old methods will still work and are left in for backwards compatibility, however future releases of AG Grid will deprecated these and then remove the old methods. </li> </ul>"}, {"release version": "<p><b>12th Sep 2020 - Grid v24.1.0 (Charts v2.1.0)</b></p> <p>Minor release with bug fixes and small improvements.</p>", "feature highlights": "<p><b>Feature Highlights:</b></p> <ul> <li> <code>Vue 3</code> Support. </li> </ul>", "breaking changes": "", "deprecations": "<p><b>Deprecations:</b></p> <ul> <li> <code>colDef.suppressHeaderKeyboardEvent</code> should now be used instead of <code>gridOptions.suppressKeyboardEvent</code>. Note the methods: <code>navigateToNextHeader</code> and <code>tabToNextHeader</code> have been added to the grid options to allow custom header navigation. For more details see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.1.0/javascript-grid-keyboard-navigation/#custom-navigation\">Custom Navigation</a>. </li> </ul>"}, {"release version": "<p><b>9th Sep 2020 - Grid v24.0.0 (Charts v2.0.0)</b></p>", "feature highlights": "<p><b>Feature Highlights:</b></p> <ul> <li> AG-1873: Allow more combining multiple column filters on one column (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-filter-multi/\">Multi Filter</a>) </li> <li> AG-4291: Reactive Columns - Enhancements to Column Definitions including the following: <ul> <li> Declarative Column Definitions for React resulting in application code that fits more nicely with the React paradigm. </li> <li> Revamped <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-column-updating-definitions/\">Updating Column Definitions</a> to make it easier to make changes to columns. For example state items now have 'default' values (defaultWidth, defaultSort etc) which means there is no longer any need for 'Immutable Columns' (which are gone). </li> <li> Revamped <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-column-state/\">Column State</a> to allow more powerful and fine grained control of Column State without touching the Column Definitions. For example partial state can be applied to impact certain Columns or to impact only certain Attributes, providing fine grained control over Column State. </li> </ul> </li> <li>Accessibility Enhancements</li> <ul> <li>AG-4254 - Allow screen readers to read column names in the column tool panel</li> <li>AG-4394 - Add ARIA tags in the paging panel</li> <li>AG-4390 - Allow updates to sort order to be announced</li> <li>AG-2629 - Allow screen readers/keyboard navigation to access the column headers sort and filtering elements</li> <li>AG-4279 - Add ARIA label to row selection checkbox</li> <li>AG-4250 - Add role definitions to grouped rows to allow them to be read correctly by screen readers</li> <li>AG-4389 - Allow column menu tabs to be announced correctly in JAWS</li> <li>AG-4322 - Update ARIA role, label, title, sort tags for column headers</li> <li>AG-4314 - Allow passing the WAVE, AXE accessibility audit</li> <li>AG-4363 - Add ARIA labels to cell editors</li> <li>AG-1967 - Add Accessibility attributes across column header Filter/Sorting elements</li> <li>AG-4391 - Add aria-label to provided filter menu inputs</li> <li>AG-4393 - Allow using keyboard navigation to navigate to and access the pagination panel</li> (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-accessibility/\">Accessibility</a>) </ul> <li> AG-2821 - Chart Themes (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-charts-themes/\">Chart Themes</a>, <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-charts-integrated-customisation/\">Integrated Theme Based Customisation</a>) </li> <li> AG-4140 - Allow aggregation without totalling on pivot column groups (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-pivoting/#expandable-pivot-column-groups\">Expandable Pivot Groups</a>) </li> <li> AG-4266 - Add API methods indicating whether the undo/redo stack is empty (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/24.0.0/javascript-grid-undo-redo-edits/#example-undo-redo\">Undo / Redo</a>) </li> </ul>", "breaking changes": "<p><b>Breaking Changes:</b></p> <p><u>Reactive Columns</u></p> <ul> <li>Column stateful items (width, flex, hide, sort, aggFunc, pivot, pivotIndex, rowGroup, rowGroupIndex, initialPinned) always get re-applied when Column Definitions are updated.</li> <li>Grid Property 'immutableColumns' is now gone. Columns are immutable by default.</li> <li>Grid Column API 'getColumnState()' now returns back more information about columns. This is only a breaking change if your application isn't able to work with the extra details.</li> <li>Grid Column API 'setColumnsState()' is replaced with 'applyColumnState()'. The new method is similar but more powerful / flexible.</li> <li>Grid Property 'suppressSetColumnStateEvents' renamed to 'suppressColumnStateEvents'.</li> <li>Column Definition property sortedAt replaced with sortIndex.</li> <li>Grid API's 'getSortModel()' and 'setSortModel()' are deprecated as sort information is now part of Column State. Use get/applyColumnState() for sort information instead.</li> </ul> <p>See 'More Info' on AG-4291 for full details, these changes make more sense in context of the wider changes.</p> <p><u>Custom Aggregation</u></p> <ul> <li> Custom aggregation functions now take a params object, previously they took a list of values. See 'More Info' on AG-4291 for details. </li> </ul> <p><u>Row Deselection</u></p> <ul> <li>rowDeselection no longer has any affect as the grid now allows row deselection by default. To block row deselection set suppressRowDeselection to true..</li> </ul> <p><u>Configuring 'Full Width Group Row Inner Renderer'</u></p> <p> How <code>innerRenderer</code> was configured as a grid option was wrong and has been corrected to the correct way. The old way was using grid properties <code>groupRowInnerRenderer</code> and <code>groupRowInnerRendererParams</code>. The new correct way is to use <code>groupRowRendererParams.innerRenderer</code> and <code>groupRowRendererParams.innerRendererParams</code>. </p>", "deprecations": "<p><b>Removed Deprecations:</b></p> <p>The following have been deprecated for over a year and have now been removed:</p> <p><u>Grid Options</u></p> <ul> <li>pivotTotals (use pivotColumnGroupTotals = 'before' | 'after')</li> <li>gridAutoHeight (use domLayout = 'autoHeight')</li> <li>groupSuppressRow (remove row groups and perform custom sorting)</li> <li>suppressTabbing (use the grid callback suppressKeyboardEvent(params))</li> <li>showToolPanel (use gridOptions.sideBar)</li> <li>toolPanelSuppressRowGroups (use toolPanelParams.suppressRowGroups)</li> <li>toolPanelSuppressValues (use toolPanelParams.suppressValues)</li> <li>toolPanelSuppressPivots (use toolPanelParams.suppressPivots)</li> <li>toolPanelSuppressPivotMode (use toolPanelParams.suppressPivotMode)</li> <li>toolPanelSuppressColumnFilter (use toolPanelParams.suppressColumnFilter)</li> <li>toolPanelSuppressColumnSelectAll (use toolPanelParams.suppressColumnSelectAll)</li> <li>toolPanelSuppressSideButtons (use toolPanelParams.suppressSideButtons)</li> <li>toolPanelSuppressColumnExpandAll (use toolPanelParams.suppressColumnExpandAll)</li> <li>contractColumnSelection (use toolPanelParams.contractColumnSelection)</li> <li>enableSorting / enableServerSideSorting (use sortable=true on the column definition)</li> <li>enableFilter / enableServerSideFilter (use filter=true on the column definition)</li> <li>enableColResize (use resizable = true on the column definition)</li> <li>getNodeChildDetails() (use new tree data)</li> <li>doesDataFlower() (use new master detail)</li> <li>processChartOptions() (use chartThemeOverrides)</li> </ul> <p><u>Column Definitions</u></p> <ul> <li>suppressSorting (use colDef.sortable=false)</li> <li>suppressFilter (use colDef.filter=false)</li> <li>suppressResize (use colDef.resizable=false)</li> <li>suppressToolPanel (use coldDef.suppressColumnsToolPanel)</li> <li>tooltip (use colDef.tooltipValueGetter)</li> </ul> <p><u>Row Node</u></p> <ul> <li>canFlower</li> <li>flower</li> <li>childFlower</li> </ul> <p><u>Events</u></p> <ul> <li>floatingRowDataChanged (use pinnedRowDataChanged)</li> </ul>"}, {"release version": "<p>5th Jun 2020 - Grid v23.2.0 (Charts v1.2.0)</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-3253: Allow keyboard navigation through all parts of the grid (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-keyboard-navigation/\">Keyboard Navigation</a>) </li> <li> AG-4227: Add chart navigator to allowing panning and zooming (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-charts-navigator/\">Chart Navigator</a>) </li> <li>Filter Enhancements</li> <ul> <li> AG-1594: All row values to be expanded to multiple values in set filter (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-filter-set-filter-list/#multiple-values-per-cell\">Set Filter - Multiple Values Per Cell</a>) </li> <li> AG-4220: Create Excel mode for set filter (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-filter-set-excel-mode/\">Set Filter - Excel Mode</a>) </li> <li> AG-1645: When using asynchronous values for set filter, setting filter model should work (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-filter-api/\">Filter API</a>) </li> <li> <div> AG-2216 - Allow filter values to be loaded every time the user opens the set filter </div> <div> AG-3089 - Update all asynchronously-loaded set filter values when any filters change </div> <div> AG-2298 - Allow async set values to be fetched on-demand via an API, not only when filter opened initially </div> (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-filter-set-filter-list/#refreshing-values\">Set Filter - Refreshing Values</a>) </li> </ul> <li>Master Detail Enhancements</li> <ul> <li> AG-2546 - Allow Master / Detail to auto-height as detail data changes (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-master-detail-height/#auto-height\">Master Detail - Auto Height</a>) </li> <li> AG-2651 - Master/Detail refresh isRowMaster when updating data for a row (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-master-detail-master-rows/#dynamic-master-rows\">Dynamic Master Rows</a>) </li> <li> AG-3589 - Allow for dynamically changing master rows into leaf nodes (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-master-detail-master-rows/#changing-dynamic-master-rows\">Changing Dynamic Master Rows</a>) </li> <li> AG-3916 - Allow for the refresh of detail rows when using immutable data (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.2.0/javascript-grid-master-detail-refresh/#refresh-rows\">Master Detail - Refresh Rows</a>) </li> </ul> </ul>", "breaking changes": "", "deprecations": "<p>Deprecations:</p> <ul> <li>SetFilter.setLoading() is deprecated. The loading screen is displayed automatically when the set filter is retrieving values</li> <li>SetFilter.selectEverything() is deprecated. setModel should be used instead</li> <li>SetFilter.selectNothing() is deprecated. setModel should be used instead</li> <li>SetFilter.selectValue() is deprecated. setModel should be used instead</li> <li>SetFilter.unselectValue() is deprecated. setModel should be used instead</li> <li>SetFilter.isValueSelected() is deprecated. getModel should be used instead</li> <li>SetFilter.isEverythingSelected() is deprecated. getModel should be used instead</li> <li>SetFilter.isNothingSelected() is deprecated. getModel should be used instead</li> <li>SetFilter.getUniqueValueCount() is deprecated. getValues should be used instead</li> <li>SetFilter.getUniqueValue() is deprecated. getValues should be used instead</li> <li>Provided filter filterParams.applyButton has been deprecated. Use filterParams.buttons instead</li> <li>Provided filter filterParams.clearButton has been deprecated. Use filterParams.buttons instead</li> <li>Provided filter filterParams.resetButton has been deprecated. Use filterParams.buttons instead</li> </ul>"}, {"release version": "<p>1st May 2020 - Grid v23.1.0 (Charts v1.1.0)</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-3576: Allow dragging multiple columns between grids (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-grid-row-dragging-to-grid/#dragging-multiple-records-between-grids\">Dragging Multiple Records Between Grids</a>) </li> <li> AG-3625: Allow the grid to respond to DnD data based on the row position (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-grid-row-dragging-to-grid/#example-two-grids-with-drop-position\">Row Dragging Drop Position</a>) </li> <li> AG-4066: Allow Grid colours to be changed using CSS variables (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-grid-themes-customising/#setting-parameters-css-variables\">Changing colours with CSS variables</a>) </li> <li> AG-3312: Add Histogram Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-charts-histogram-series/\">Histogram Series</a>) </li> <li> AG-3472: Add the ability to listen to click events on Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-charts-events/\">Chart Events</a>) </li> <li> AG-588: Add tooltips to Set Filter List (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-grid-filter-set-filter-list/#filter-value-tooltips\">Set Filter Tooltips</a>) </li> <li>AG-2162: Add option to close filter popup when Apply button is clicked (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-grid-filter-provided/#providedFilterParams\">'closeOnApply' Filter Param</a>) </li> <li> AG-2187: Allow floating filters to be enabled/disabled on a per-column basis (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.1.0/javascript-grid-floating-filters/#floating-filters\">Floating Filters on a per-column basis</a>) </li> </ul>", "breaking changes": "", "deprecations": "<p>Deprecations:</p> <ul> <li>Grid API updateRowData() deprecated, replaced with applyTransaction()</li> <li>Grid API batchUpdateRowData() deprecated, replaced with applyTransactionAsync()</li> <li>Grid Property 'batchUpdateWaitMillis' deprecated, replaced with 'asyncTransactionWaitMillis'</li> <li>Grid Property 'deltaRowDataMode' deprecated, replaced with 'immutableData'</li> <li>Grid Property 'deltaColumnMode' deprecated, replaced with 'immutableColumns'</li> <li>RowDataTransaction Property 'addIndex' will be removed in a future major release</li> <li>RowDataTransaction Property 'addIndex' will be removed in a future major release</li> <li>Set Filter Param 'suppressSyncValuesAfterDataChange' will be removed in a future major release</li> <li>Set Filter Param 'suppressRemoveEntries' will be removed in a future major release</li> <li>Filter Param 'newRowsAction' will be removed in a future major release (newRowsAction = 'keep' will become the default behaviour)</li> </ul>"}, {"release version": "<p>17th Mar 2020 - Grid v23.0.0 (Charts v1.0.0)</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-3110 - Allow charts to be created outside of grid (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-charts-overview/\">Standalone Charting</a>). </li> <li> AG-2832 - Add new 'Alpine Theme' (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-grid-themes-provided/#themes-summary\">Themes Summary</a>). </li> <li>AG-3872 - Improve Server-Side Row Model docs and examples (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-grid-server-side-model/\">Server-Side Row Model</a>). </li> <li> AG-2025 - Add keyboard navigation to context menu (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-grid-context-menu/\">Context Menu</a>). </li> <li> AG-3203 - Add API to download charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-grid-charts-integrated-chart-range-api/#saving-and-restoring-charts\">Saving and Restoring Charts</a>). </li> <li> AG-3678 - Add additional chart lifecycle events to aid persisting charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-grid-charts-integrated-chart-events/\">Chart Events</a>). </li> </ul>", "breaking changes": "<p>Breaking Changes:</p> <ul> <li> <p> AG-3110 - We have undertaken a major rewrite of the Sass code behind our provided themes, with the goal of making it easier to write custom themes. See <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/23.0.0/javascript-grid-themes-v23-migration/\">Migrating themes to AG Grid 23.x</a> to understand why we've made these changes, and exactly what we've changed. </p> </li> <li> <p> AG-3802 - Migrate <code>ag-grid-angular</code> & <code>@ag-grid-community/angular</code> to use the Angular CLI to build. Angular 6+ is now the minimum supported version of Angular. </p> </li> <li> <p> AG-3110 - Tooltip renderer params: if a series has no `title` set, the tooltip renderer will receive the `title` as it, it won't be set to the value of the `yName` as before. </p> </li> <li> AG-3110 - Legend API changes: <ul> <li>legend.padding -> legend.spacing</li> <li>legend.itemPaddingX -> legend.layoutHorizontalSpacing</li> <li>legend.itemPaddingY -> legend.layoutVerticalSpacing</li> <li>legend.markerPadding -> legend.itemSpacing</li> <li>legend.markerStrokeWidth -> legend.strokeWidth</li> <li>legend.labelColor -> legend.textColor</li> <li>legend.labelFontStyle -> legend.fontStyle</li> <li>legend.labelFontWeight -> legend.fontWeight</li> <li>legend.labelFontSize -> legend.fontSize</li> <li>legend.labelFontFamily -> legend.fontFamily</li> </ul> </li> </ul>", "deprecations": ""}, {"release version": "<p>6th Dec 2019 - Grid v22.1.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-1630 - Add Excel-like Fill Handle (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.1.0/javascript-grid-range-selection-fill-handle/\">Fill Handle</a>). </li> <li> AG-2566 - Allow specifying column width as reminder viewport view (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.1.0/javascript-grid-resizing/#column-flex\">Column Flex</a>). </li> <li> AG-169 - Allow Undo / Redo of Cell Editing (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.1.0/javascript-grid-undo-redo-edits/\">Undo / Redo Edits</a>). </li> <li> AG-3318\t- Allow charts to be saved and restored (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.1.0/javascript-grid-charts-chart-range-api/#saving-and-restoring-charts\">Saving and Restoring Charts</a>). </li> <li> AG-2819 - Add support for Time Series charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.1.0/javascript-grid-charts-customisation-cartesian/#example-time-series-chart\">Time Series Charting</a>). </li> <li> AG-332 - Allow exporting Master Detail to Excel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.1.0/javascript-grid-master-detail/#exporting-master-detail-data\">Exporting Master / Detail Data</a>). </li> </ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p>11th Nov 2019 - Grid v22.0.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> Charts is now out of Beta! (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-charts-overview/\">Charts</a>). </li> <li> AG-1329 - Modularise Grid Features to reduce grid bundle size (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-modules/\">Modularisation</a>). </li> <li> AG-3269 - A new pivotChart API has been added to charts. (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-charts-chart-range-api/#pivot-charts\">Pivot Chart API</a>). </li> <li> AG-2200 - Allow filters to be arranged using column groups in the filters tool panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-tool-panel-filters/\">Filters Tool Panel</a>). </li> <li> AG-2363 - Add filter search to the filters tool panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-tool-panel-filters/\">Filters Tool Panel</a>). </li> <li> AG-1862 - Allow custom column layouts in the Columns Tool Panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-tool-panel-columns/#custom-column-layout\">Custom Column Tool Panel Layout</a>). </li> <li> AG-3131 - Allow custom filter layouts in the Filters Tool Panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-tool-panel-filters/#custom-filters-layout\">Custom Filters Tool Panel Layout</a>). </li> <li> AG-1991 - Allow filters and columns tool panel to have API calls to expand/collapse column groups/filters (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-tool-panel-columns/#expand-collapse-column-groups\">Expand / Collapse Column Groups</a>) (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-tool-panel-filters/#expand-collapse-filter-groups\">Expand / Collapse Filter Groups</a>). </li> <li> AG-1026\t- Allow sidebar to be placed in the left or right position of the grid (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-side-bar/#sidebardef-configuration\">Side Bar Configuration</a>). </li> <li> AG-907 - Rollup Support Added (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/ag-grid-building-rollup/\">Rollup</a>). </li> </ul>", "breaking changes": "<p>Breaking Changes:</p> <ul> <li> In taking Charts out of Beta it was necessary to to make numerous interface / chart option changes (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-charts-chart-range-api/\">Chart API</a> and <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/22.0.0/javascript-grid-charts-customisation/\">Chart Customisation</a>). </li> <li> AG-3316 - agGridReact needs to be updated to use the updated react lifecycle hooks. React 16.3 is now the minimum version supported by AgGridReact. </li> <li> AG-3383 - Property selectAllOnMiniFilter no longer used, its the default behaviour for Set Filter. </li> <li> AG-3369 - syncValuesLikeExcel is enabled by default. </li> <li> AG-3345 / AG-3347 - tool panels are now kept in sync with the column order in the grid. To revert enable the following Tool Panel property: 'suppressSyncLayoutWithGrid'. </li>", "deprecations": ""}, {"release version": "<p>30th Aug 2019 - Grid v21.2.0</p>", "feature highlights": "<p>Feature Highlights:</p> <span style=\"font-weight: bold\"></span> <ul> <li> AG-3215 - Add Pivot Chart (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-charts-pivot-chart/\">Pivot Chart</a>). </li> <li> AG-3036 / AG-3160 - Add Scatter / Bubble Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-charts-customisation-scatter/\">Scatter Charts</a>). </li> <li>AG-2762 - Pagination - prevent separation of children from their parent rows with Master Detail and Row Grouping (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-pagination/#childRows\">Pagination & Child Rows</a>). </li> <li>AG-1643 - RichSelect - Allow typing to automatically scroll to item</li> <li>AG-3165 - Chart API - Add support for extra / custom aggregations (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-charts-chart-range-api/#chart-range-api-1\">Chart API</a>). </li> <li>AG-3154 - Charts - Allow user formatting changes to be saved / restored (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-charts-customisation/#saving-user-preferences\">Saving User Preferences</a>). </li> <li>AG-3184 - Charts - Add ability to unlink / detach charts from grid data (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-charts-chart-toolbar/#unlinking-charts\">Unlinking Charts</a>). </li> <li>AG-2736 - Accessibility - Enhance support for Screen Readers with additional ARIA Roles (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-accessibility/\">Accessibility</a>). </li> <li>AG-3196 - Security - Add Section to Docs for OWASP and CSP (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.2.0/javascript-grid-security/\">Security</a>). </li> </ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p>18th July 2019 - Grid v21.1.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-3002 - Charts: Add Chart Format Panel (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.1.0/javascript-grid-charts-chart-toolbar/#chart-format\">Chart Format Panel</a>). </li> <li> AG-2833 - Charts: Add Area Charts (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.1.0/javascript-grid-charts-customisation-area/\">Area Charts</a>). </li> <li>AG-1708 - Row dragging: Allow dragging between grids or between the grid and a external element (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.1.0/javascript-grid-drag-and-drop/\">Drag & Drop</a>). </li> <li> AG-3012 - Master/Detail: Detail row state now kept when detail row is closed (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.1.0/javascript-grid-master-detail/#keeping-row-details\">Keeping Detail Rows</a>). </li> <li>AG-2912 - Master/Detail: Keep detail state when scrolled out of view.</li> </ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p>4th June 2019 - Grid v21.0.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-3008 / AG-3009 - Integrated Charts - a major new component has been added to AG Grid which provides integrated charting from within the grid (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-charts-overview/#user-created-charts\">User Created Charts</a> and <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-charts-overview/#application-created-charts\">Application Created Charts</a>). </li> <li> AG-2946 - Filters Refactor - The simple provided filters (number, text, date) were part of the first grid release and the design was built on top of as new requirements were catered for. All the additional requirements made the original design difficult to maintain - the old design was coming to it's 'end of life'. For that reason the simple provided filters were rewritten from scratch. The has the benefits of a) implementing floating filters is now simpler; b) all provided filters now work in a more consistent way; c) code is easier to follow for anyone debugging through the AG Grid code. The documentation for column filters was also rewritten from scratch to make it easier to follow. </li> <li> AG-2804 - Scroll Performance Improvements - Now when you scroll vertically the performance is vastly improved over the previous version of the grid. We make better use of debounced scrolling, animation and animation frames. </li> <li> AG-2999 - Change of License Messaging - Now anyone can try out AG Grid Enterprise without needing a license from us. We want everyone to give AG Grid Enterprise a trial. You only need to get in touch with us if you decided to start using AG Grid inside your project. </li> <li> AG-2983 - Improved customisation for icons (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-icons/\">Custom Icons</a>). </li> <li>AG-2663 - React - Declarative Column Definitions Now Reactive.</li> <li>AG-2536 - React - Component Container Configurable (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/react-more-details/#control-react-components-container\">Control React Components Container</a>).</li> <li>AG-2656 - React - Allow React Change Detection to be Configurable (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/react-more-details/#react-row-data-control\">Row Data Control</a>).</li> <li>AG-2257 - All Frameworks - Expand & Improve Testing Documentation (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-testing/\">AG Grid Testing</a>).</li> </ul>", "breaking changes": "<p>Breaking Changes:</p> <ul> <li> AG-2946 - Number and Date Column Filters – Null Comparator is replaced with includeBlanksInEquals, includeBlanksInLessThan and includeBlanksInGreaterThan. (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-filter-provided-simple/#blank-cells-date-and-number-filters\">Blank Cells - Date and Number Filters</a>). </li> <li> AG-2946 - Floating Filters: floatingFilterParams.debounceMs is no longer a property, instead the floating filter uses the property of the same name form filterParams. </li> <li> AG-2946 - Custom Floating Filters – params.onParentModelChanged() is no longer used -instead you call methods on the parent filter directly. Also IFloatingFilter no longer takes generics. (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-floating-filter-component/\">Custom Floating Filters</a>). </li> <li> AG-2984 - Replaced all SVG icons with a WebFont (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/21.0.0/javascript-grid-icons/\">Custom Icons</a>). </li> </ul>", "deprecations": ""}, {"release version": "<p>22nd Mar 2019 - Grid v20.2.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li>AG-2722 - Add ability to create custom filters without input filter fields, ie isNull (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.2.0/javascript-grid-filtering/#adding-custom-filter-options\">Custom Filter Options</a>). </li> <li>AG-2121 - Allow column-spanning across row header groups when they belong to the same column group</li> <li>AG-1936 - Add the ability to change the header checkbox and the drag handle icons</li> <li>AG-2143 - Add new property to load the grid with the sidebar hidden</li> </ul>", "breaking changes": "<p>Breaking Changes:</p> <ul> <li> AG-1707 - Change Tree Data filtering to additionally include child nodes when parent node passes filter (see <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.2.0/javascript-grid-tree-data/#tree-data-filtering\">Tree Data Filtering</a>). </li> </ul>", "deprecations": ""}, {"release version": "<p>22nd Feb 2019 - Grid v20.1.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li>AG-1617 - <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.1.0/javascript-grid-tooltip-component/\"> Allow for Custom Tooltip Components</a></li> <li>AG-2166 - <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.1.0/javascript-grid-filtering/#adding-custom-filter-options\"> Allow for defining Custom Filters that appear in Filter Option List</a></li> <li>AG-1049 - <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.1.0/javascript-grid-loading-cell-renderer/\"> Allow for Custom Loading Renderer Component </a></li> <li>AG-2185 - <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.1.0/nodejs-server-side-operations/\"> New Server-Side Row Model guide for Node.js with MySql</a></li> <li>AG-1972 - Performance improvements for small changes to large datasets</li> <li>AG-2289 - Better management of Column Definitions after grid is created</li> <li>AG-2305 - Lazy row height calculation for dynamic row heights</li> <li>AG-1485 - Raise events for cellKeyPress and cellKeyDown</li> <li>AG-2628 - Provide capability to suppress keyboard actions from the grid</li>", "breaking changes": "", "deprecations": ""}, {"release version": "<p>11th Jan 2019 - Grid v20.0.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-1709 Server-Side Row Model - Add support for Master / Detail <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.0.0/javascript-grid-server-side-model-master-detail/\">Server-Side Master Detail</a>. </li> <li> AG-2448 Add declarative support to vue component (column defs) <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.0.0/best-vuejs-data-grid/#declarative_definition\">Using Markup to Define Grid Definitions</a>. </li> <li> AG-2280 Allow event handler in Vue too support more idiomatic conventions <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.0.0/best-vuejs-data-grid/#configuring-ag-grid-in-vuejs\">Configuring AG Grid in Vue</a>. </li> <li> AG-939 Improve horizontal and vertical scrolling in other browsers. </li> </ul>", "breaking changes": "<p>Breaking Changes:</p> <ul> <li> AG-939 - The structure of Containers and Viewports has changed to improve scroll performance, so custom themes, will most likely need to be updated to reflect these changes. See <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.0.0/javascript-grid-styling/\">Themes</a> for more information. </li> <li> AG-2235 - We have restructured our themes, so If you create custom themes extending our sass files, you will need to update the @import path.<br> See how to customise your themes <a href=\"https://github.com/ag-grid/ag-grid-customise-theme\">here</a> </li> <li> <code>ag-grid-vue</code> now has a dependency on <code>vue-property-decorator</code> </li> <li> <code>ag-grid-vue</code>Event bindings are now bound with <code>@</code> instead of <code>:</code>. Please refer to <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/20.0.0/best-vuejs-data-grid/\">Configuring AG Grid and Vue.js</a>. </li> </ul>", "deprecations": "<p>Deprecation:</p> <ul> <li>AG-644 Refactor of sorting, filtering and resizing properties</li> </ul>"}, {"release version": "<p>30th Oct 2018 - Grid v19.1.1</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-904 <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.1.4/best-polymer-data-grid/\">Polymer 3 Datagrid</a>. </li> <li> AG-726 <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.1.4/javascript-grid-excel/\">Export to XLSX</a>. </li> <li> AG-1591 <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.1.4/javascript-grid-column-definitions/#column-changes/\">Allow Delta Changes to Column Definitions</a>. </li> </ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p>7th Sept 2018 - Grid v19.0.0</p>", "feature highlights": "<p>Feature Highlights:</p> <ul> <li> AG-1201 The Status Bar is now customizable with <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.0.0/javascript-grid-status-bar-component/\">Custom Status Panel Components</a>. </li> <li> AG-1915 The Side Bar is now customizable with <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.0.0/javascript-grid-tool-panel-component/\">Custom Tool Panel Components</a>. </li> <li> AG-1914 A new <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.0.0/javascript-grid-tool-panel-filters/\">Filters Tool Panel</a> has been added to the Side Bar. </li> <li> AG-1881 Lazy load hierarchical data with <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.0.0/javascript-grid-server-side-model-tree-data/\">Server-Side Tree Data</a>. </li> <li> AG-1961 Debounce block loading with Infinite and Server-Side Row Models using the new grid options property: 'blockLoadDebounceMillis'. </li> <li> AG-1363 columnApi.resetColumnState() can now optionally raise column based events. </li> </ul>", "breaking changes": "<p>Breaking Changes:</p> <ul> <li> The NPM package name for the free module of AG Grid is now <code>ag-grid-community</code> instead of <code>ag-grid</code>. This means you install with <code>npm install ag-grid-community</code> and then you reference like <code>import {Grid, GridOptions} from \"ag-grid-community\"</code>. </li> <li> AG Grid received a major overhaul of the Tool Panels in version 19.0.0. The old property 'showToolPanel' is no longer used and the Tool Panel is also not included by default. For more details see: <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/19.0.0/javascript-grid-side-bar/#configuring-the-side-bar\">Configuring the Side Bar</a>. </li> </ul>", "deprecations": ""}, {"release version": "<p>12th Jun 2018 - Grid v18.0.0</p>", "feature highlights": "<ul> <li> AG-1844 Grid is now laid out using CSS Flex. Before this the grid had it's own layout mechanism called Border Layout. This had the following disadvantages: <ul> <li> The grid had a timer (layout interval) where every 500ms it was checking the width and height of the grid and then laying out contents again if the width or height changed. </li> <li> Extra DIV elements were required for the layout. </li> </ul> The new mechanism no longer uses the layout interval so the grid is no longer polling every 500ms. The DOM is also now cleaner as the extra div's associated with the border layout are now gone. </li> <li> AG-1807 New strategy for <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-for-print/\">Printing</a> using auto-height. </li> <li> AG-1350 Added support for <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-row-spanning/\">Row Spanning</a>. </li> <li> AG-1768 Now possible to switch between <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-width-and-height/#auto-height\">Auto Height</a> and Normal Height dynamically. </li> <li> AG-678 <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-grouping/#grouping-footers\">Grouping Footers</a> now provides an option for a 'grand' total across all groups. </li> <li> AG-1793 When in pivot mode you can now include <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-pivoting/#pivotRowTotals\">Pivot Row Totals</a> </li> <li> AG-1569 To help clarify Row Model usage, we have renamed as follows: <ul> <li>In-Memory Row Model -> <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-client-side-model/\">Client-Side Row Model</a></li> <li>Enterprise Row Model -> <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-server-side-model/\">Server-Side Row Model</a></li> </ul> </li> <li> AG-865 The Server-Side Row Model now preserves group state after sorting has been performed. </li> <li> <p>AG-424 Text, Number and Date filters now support two filter conditions instead of just one. The user through the UI can decide which sort of logic to apply: 'AND'/'OR'</p> <p>This also means that the model for the filter changes when two conditions are applied.</p> <p>The ability to add an additional filter condition can be suppressed with <code>filterParams.suppressAndOrCondition = true</code></p> <p>The documentation of each filter has been updated to reflect these changes accordingly: <ul> <li><a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-filter-text/\">text filter</a></li> <li><a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-filter-number/\">number filter</a></li> <li><a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/18.1.0/javascript-grid-filter-date/\">date filter</a></li> </ul></p> </li> </ul>", "breaking changes": "", "deprecations": ""}, {"release version": "<p>13th Apr 2018 - Grid v17.1.0</p>", "feature highlights": "<ul> <li> AG-1730 Deprecate <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/javascript-grid-for-print/\">for print</a>, the same functionality can be achieved with <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/javascript-grid-width-and-height/#autoHeight\">domLayout: 'autoHeight'</a> </li> <li> AG-1626 – <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/javascript-grid-filter-quick/\">Quick filter</a> now filters using multiple words, eg search for “<PERSON>” to bring back all rows with <PERSON><PERSON> in the name column and “Ireland” in the country column. </li> <li> AG-1405 – Support for <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/javascript-grid-row-height/#auto-row-height\"> automatic text wrapping</a>. </li> <li> AG-1682 - New guides for connecting <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/oracle-server-side-operations/\">Enterprise Row Model to Oracle</a> and <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/spark-server-side-operations/\">Enterprise Row Model to Apache Spark</a>. </li> <li> AG-1675 – BREAKING CHANGE - The <a rel=\"nofollow\" href=\"https://www.ag-grid.com/archive/17.1.1/javascript-grid-filter-set/#set-filter-model\">set filter model</a> was changed to be consistent with other filter models. For backwards compatibility, this change can be toggled off using the grid property <code>enableOldSetFilterModel</code>. Both models will be supported for releases for the next 6 months. After this one major AG Grid release will have the old model deprecated and then the following release will have it dropped. </li> </ul>", "breaking changes": "", "deprecations": ""}]