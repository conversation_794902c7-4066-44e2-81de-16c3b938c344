/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_TW = {
    // Set Filter
    selectAll: '(全選)',
    selectAllSearchResults: '(全選搜尋結果)',
    addCurrentSelectionToFilter: '將當前選擇新增到篩選條件中',
    searchOoo: '搜尋...',
    blanks: '(空白)',
    noMatches: '無匹配項',

    // Number Filter & Text Filter
    filterOoo: '篩選...',
    equals: '等於',
    notEqual: '不等於',
    blank: '空白',
    notBlank: '非空白',
    empty: '選擇一個',

    // Number Filter
    lessThan: '小於',
    greaterThan: '大於',
    lessThanOrEqual: '小於或等於',
    greaterThanOrEqual: '大於或等於',
    inRange: '介於',
    inRangeStart: '從',
    inRangeEnd: '到',

    // Text Filter
    contains: '包含',
    notContains: '不包含',
    startsWith: '開始於',
    endsWith: '結束於',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: '之前',
    after: '之後',

    // Filter Conditions
    andCondition: '和',
    orCondition: '或',

    // Filter Buttons
    applyFilter: '應用',
    resetFilter: '重置',
    clearFilter: '清除',
    cancelFilter: '取消',

    // Filter Titles
    textFilter: '文字篩選',
    numberFilter: '數字篩選',
    dateFilter: '日期篩選',
    setFilter: '集合篩選',

    // Group Column Filter
    groupFilterSelect: '選擇字段：',

    // New Filter Tool Panel
    filterSummaryInactive: '是 (全部)',
    filterSummaryContains: '包含',
    filterSummaryNotContains: '不包含',
    filterSummaryTextEquals: '等於',
    filterSummaryTextNotEqual: '不等於',
    filterSummaryStartsWith: '開始於',
    filterSummaryEndsWith: '結束於',
    filterSummaryBlank: '為空',
    filterSummaryNotBlank: '不為空',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: '介於',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: '是 (全部)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: '是 (${variable})',
    filterSummaryListLong: '是 (${variable}) 和其他 ${variable} 項',
    addFilterCard: '添加篩選條件',
    agTextColumnFilterDisplayName: '簡單篩選',
    agNumberColumnFilterDisplayName: '簡單篩選',
    agDateColumnFilterDisplayName: '簡單篩選',
    agSetColumnFilterDisplayName: '選擇篩選',
    agMultiColumnFilterDisplayName: '組合篩選',
    addFilterPlaceholder: '搜尋列...',

    // Advanced Filter
    advancedFilterContains: '包含',
    advancedFilterNotContains: '不包含',
    advancedFilterTextEquals: '等於',
    advancedFilterTextNotEqual: '不等於',
    advancedFilterStartsWith: '開頭是',
    advancedFilterEndsWith: '結尾是',
    advancedFilterBlank: '為空白',
    advancedFilterNotBlank: '不為空白',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: '為真',
    advancedFilterFalse: '為假',
    advancedFilterAnd: '和',
    advancedFilterOr: '或',
    advancedFilterApply: '應用',
    advancedFilterBuilder: '構建器',
    advancedFilterValidationMissingColumn: '缺少欄位',
    advancedFilterValidationMissingOption: '缺少選項',
    advancedFilterValidationMissingValue: '缺少值',
    advancedFilterValidationInvalidColumn: '找不到欄位',
    advancedFilterValidationInvalidOption: '找不到選項',
    advancedFilterValidationMissingQuote: '值缺少結尾引號',
    advancedFilterValidationNotANumber: '值不是數字',
    advancedFilterValidationInvalidDate: '值不是有效的日期',
    advancedFilterValidationMissingCondition: '缺少條件',
    advancedFilterValidationJoinOperatorMismatch: '條件內的連接操作符必須一致',
    advancedFilterValidationInvalidJoinOperator: '找不到連接操作符',
    advancedFilterValidationMissingEndBracket: '缺少結尾括號',
    advancedFilterValidationExtraEndBracket: '結尾括號過多',
    advancedFilterValidationMessage: '表達式有錯誤。${variable} - ${variable}。',
    advancedFilterValidationMessageAtEnd: '表達式有錯誤。${variable} 在表達式結尾。',
    advancedFilterBuilderTitle: '進階篩選',
    advancedFilterBuilderApply: '應用',
    advancedFilterBuilderCancel: '取消',
    advancedFilterBuilderAddButtonTooltip: '添加篩選或群組',
    advancedFilterBuilderRemoveButtonTooltip: '移除',
    advancedFilterBuilderMoveUpButtonTooltip: '上移',
    advancedFilterBuilderMoveDownButtonTooltip: '下移',
    advancedFilterBuilderAddJoin: '添加群組',
    advancedFilterBuilderAddCondition: '添加篩選',
    advancedFilterBuilderSelectColumn: '選擇欄位',
    advancedFilterBuilderSelectOption: '選擇選項',
    advancedFilterBuilderEnterValue: '輸入值...',
    advancedFilterBuilderValidationAlreadyApplied: '當前篩選已應用。',
    advancedFilterBuilderValidationIncomplete: '並非所有條件都已完成。',
    advancedFilterBuilderValidationSelectColumn: '必須選擇一個欄位。',
    advancedFilterBuilderValidationSelectOption: '必須選擇一個選項。',
    advancedFilterBuilderValidationEnterValue: '必須輸入一個值。',

    // Editor Validation Errors
    minDateValidation: '日期必須在 ${variable} 之後',
    maxDateValidation: '日期必須在 ${variable} 之前',
    maxLengthValidation: '必須少於或等於 ${variable} 個字元。',
    minValueValidation: '必須大於或等於 ${variable}',
    maxValueValidation: '必須小於或等於 ${variable}',
    invalidSelectionValidation: '無效的選擇。',
    tooltipValidationErrorSeparator: '。',

    // Side Bar
    columns: '欄位',
    filters: '篩選器',

    // columns tool panel
    pivotMode: '樞紐分析模式',
    groups: '行分組',
    rowGroupColumnsEmptyMessage: '拖動到此處設置行分組',
    values: '數值',
    valueColumnsEmptyMessage: '拖動到此處聚合',
    pivots: '欄位標籤',
    pivotColumnsEmptyMessage: '拖動到此處設置欄位標籤',

    // Header of the Default Group Column
    group: '群組',

    // Row Drag
    rowDragRow: '列',
    rowDragRows: '列',

    // Other
    loadingOoo: '加載中...',
    loadingError: '錯誤',
    noRowsToShow: '無顯示行',
    enabled: '已啟用',

    // Menu
    pinColumn: '固定欄位',
    pinLeft: '固定在左側',
    pinRight: '固定在右側',
    noPin: '取消固定',
    valueAggregation: '值聚合',
    noAggregation: '無',
    autosizeThisColumn: '自適應列寬',
    autosizeAllColumns: '自適應所有列寬',
    groupBy: '依此分組',
    ungroupBy: '取消分組',
    ungroupAll: '取消所有分組',
    addToValues: '添加 ${variable} 到值',
    removeFromValues: '從值中移除 ${variable}',
    addToLabels: '添加 ${variable} 到標籤',
    removeFromLabels: '從標籤中移除 ${variable}',
    resetColumns: '重設列',
    expandAll: '展開所有分組',
    collapseAll: '關閉所有分組',
    copy: '複製',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: '複製包括標題',
    copyWithGroupHeaders: '複製包括分組標題',
    cut: '剪下',
    paste: '貼上',
    ctrlV: 'Ctrl+V',
    export: '匯出',
    csvExport: '匯出 CSV',
    excelExport: '匯出 Excel',
    columnFilter: '列過濾器',
    columnChooser: '選擇列',
    chooseColumns: '選擇欄位',
    sortAscending: '升序排列',
    sortDescending: '降序排列',
    sortUnSort: '清除排列',

    // Enterprise Menu Aggregation and Status Bar
    sum: '總和',
    first: '第一個',
    last: '最後一個',
    min: '最小值',
    max: '最大值',
    none: '無',
    count: '數量',
    avg: '平均值',
    filteredRows: '已篩選',
    selectedRows: '已選擇',
    totalRows: '總行數',
    totalAndFilteredRows: '行',
    more: '更多',
    to: '至',
    of: '的',
    page: '頁',
    pageLastRowUnknown: '?',
    nextPage: '下一頁',
    lastPage: '最後一頁',
    firstPage: '第一頁',
    previousPage: '上一頁',
    pageSizeSelectorLabel: '頁大小：',
    footerTotal: '總數',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: '捲動${variable}到視圖',

    // Pivoting
    pivotColumnGroupTotals: '總計',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: '樞軸圖與樞軸模式',
    pivotChart: '樞軸圖',
    chartRange: '圖表範圍',
    columnChart: '列',
    groupedColumn: '分組',
    stackedColumn: '堆疊',
    normalizedColumn: '100% 堆疊',
    barChart: '條形',
    groupedBar: '分組',
    stackedBar: '堆疊',
    normalizedBar: '100% 堆疊',
    pieChart: '圓餅圖',
    pie: '圓餅圖',
    donut: '甜甜圈圖',
    lineChart: '折線圖',
    stackedLine: '堆疊折線圖',
    normalizedLine: '100% 堆疊折線圖',
    xyChart: 'X Y (散點圖)',
    scatter: '散點圖',
    bubble: '氣泡圖',
    areaChart: '區域圖',
    area: '區域圖',
    stackedArea: '堆疊',
    normalizedArea: '100% 堆疊',
    histogramChart: '直方圖',
    polarChart: '極地圖',
    radarLine: '雷達線',
    radarArea: '雷達面',
    nightingale: '南丁格爾玫瑰圖',
    radialColumn: '輻射列柱',
    radialBar: '輻射條形',
    statisticalChart: '統計圖',
    boxPlot: '箱型圖',
    rangeBar: '範圍條形圖',
    rangeArea: '範圍區域圖',
    hierarchicalChart: '層次圖',
    treemap: '樹狀圖',
    sunburst: '旭日圖',
    specializedChart: '專門圖表',
    waterfall: '瀑布圖',
    heatmap: '熱圖',
    combinationChart: '組合圖',
    columnLineCombo: '列與折線',
    AreaColumnCombo: '區域與列',

    // Charts
    pivotChartTitle: '樞紐圖',
    rangeChartTitle: '範圍圖',
    settings: '圖表',
    data: '設定',
    format: '自訂',
    categories: '類別',
    defaultCategory: '(無)',
    series: '系列',
    switchCategorySeries: '切換類別/系列',
    categoryValues: '類別值',
    seriesLabels: '系列標籤',
    aggregate: '彙總',
    xyValues: 'X Y 值',
    paired: '配對模式',
    axis: '軸',
    xAxis: '水平軸',
    yAxis: '垂直軸',
    polarAxis: '極軸',
    radiusAxis: '半徑軸',
    navigator: '導覽器',
    zoom: '縮放',
    animation: '動畫',
    crosshair: '準星',
    color: '顏色',
    thickness: '厚度',
    preferredLength: '首選長度',
    xType: 'X 類型',
    axisType: '軸類型',
    automatic: '自動',
    category: '類別',
    number: '數字',
    time: '時間',
    timeFormat: '時間格式',
    autoRotate: '自動旋轉',
    labelRotation: '旋轉',
    circle: '圓形',
    polygon: '多邊形',
    square: '方形',
    cross: '十字形',
    diamond: '菱形',
    plus: '加號',
    triangle: '三角形',
    heart: '心形',
    orientation: '方向',
    fixed: '固定',
    parallel: '平行',
    perpendicular: '垂直',
    radiusAxisPosition: '位置',
    ticks: '刻度',
    gridLines: '網格線',
    width: '寬度',
    height: '高度',
    length: '長度',
    padding: '填充',
    spacing: '間距',
    chartStyle: '圖表樣式',
    title: '標題',
    chartTitles: '標題',
    chartTitle: '圖表標題',
    chartSubtitle: '副標題',
    horizontalAxisTitle: '水平軸標題',
    verticalAxisTitle: '垂直軸標題',
    polarAxisTitle: '極軸標題',
    titlePlaceholder: '圖表標題',
    background: '背景',
    font: '字體',
    weight: '粗細',
    top: '上',
    right: '右',
    bottom: '下',
    left: '左',
    labels: '標籤',
    calloutLabels: '突顯標籤',
    sectorLabels: '區段標籤',
    positionRatio: '位置比例',
    size: '大小',
    shape: '形狀',
    minSize: '最小大小',
    maxSize: '最大大小',
    legend: '圖例',
    position: '位置',
    markerSize: '標記大小',
    markerStroke: '標記描邊',
    markerPadding: '標記填充',
    itemSpacing: '項目間距',
    itemPaddingX: '項目填充 X',
    itemPaddingY: '項目填充 Y',
    layoutHorizontalSpacing: '水平間距',
    layoutVerticalSpacing: '垂直間距',
    strokeWidth: '描邊寬度',
    offset: '偏移',
    offsets: '偏移值',
    tooltips: '工具提示',
    callout: '突顯',
    markers: '標記',
    shadow: '陰影',
    blur: '模糊',
    xOffset: 'X 偏移',
    yOffset: 'Y 偏移',
    lineWidth: '線寬',
    lineDash: '虛線',
    lineDashOffset: '虛線偏移',
    scrollingZoom: '滾動',
    scrollingStep: '滾動步伐',
    selectingZoom: '選擇',
    durationMillis: '持續時間 (毫秒)',
    crosshairLabel: '標籤',
    crosshairSnap: '對齊節點',
    normal: '正常',
    bold: '粗體',
    italic: '斜體',
    boldItalic: '粗斜體',
    predefined: '預設',
    fillOpacity: '填充不透明度',
    strokeColor: '線條顏色',
    strokeOpacity: '線條透明度',
    miniChart: '迷你圖表',
    histogramBinCount: '柱數',
    connectorLine: '連接線',
    seriesItems: '系列項目',
    seriesItemType: '項目類型',
    seriesItemPositive: '正數',
    seriesItemNegative: '負數',
    seriesItemLabels: '項目標籤',
    columnGroup: '柱形圖',
    barGroup: '條形圖',
    pieGroup: '圓餅圖',
    lineGroup: '折線圖',
    scatterGroup: 'X Y (散點圖)',
    areaGroup: '區域圖',
    polarGroup: '極地圖',
    statisticalGroup: '統計圖',
    hierarchicalGroup: '層次圖',
    specializedGroup: '專業圖',
    combinationGroup: '組合圖',
    groupedColumnTooltip: '分組',
    stackedColumnTooltip: '堆疊',
    normalizedColumnTooltip: '100% 堆疊',
    groupedBarTooltip: '分組',
    stackedBarTooltip: '堆疊',
    normalizedBarTooltip: '100% 堆疊',
    pieTooltip: '圓餅圖',
    donutTooltip: '甜甜圈圖',
    lineTooltip: '折線圖',
    stackedLineTooltip: '堆疊',
    normalizedLineTooltip: '100% 堆疊',
    groupedAreaTooltip: '面積圖',
    stackedAreaTooltip: '堆積面積圖',
    normalizedAreaTooltip: '100% 堆積面積圖',
    scatterTooltip: '散點圖',
    bubbleTooltip: '氣泡圖',
    histogramTooltip: '直方圖',
    radialColumnTooltip: '雷達柱形圖',
    radialBarTooltip: '雷達條形圖',
    radarLineTooltip: '雷達折線圖',
    radarAreaTooltip: '雷達面積圖',
    nightingaleTooltip: '夜鶯圖',
    rangeBarTooltip: '範圍條形圖',
    rangeAreaTooltip: '範圍區域圖',
    boxPlotTooltip: '箱線圖',
    treemapTooltip: '樹狀圖',
    sunburstTooltip: '旭日圖',
    waterfallTooltip: '瀑布圖',
    heatmapTooltip: '熱圖',
    columnLineComboTooltip: '柱形 & 折線圖',
    areaColumnComboTooltip: '區域 & 柱形圖',
    customComboTooltip: '自訂組合圖',
    innerRadius: '內半徑',
    startAngle: '起始角度',
    endAngle: '終止角度',
    reverseDirection: '逆向',
    groupPadding: '組填充量',
    seriesPadding: '系列填充量',
    tile: '平鋪',
    whisker: '鬚狀圖',
    cap: '帽端',
    capLengthRatio: '帽端長度比',
    labelPlacement: '標籤放置',
    inside: '內部',
    outside: '外部',
    noDataToChart: '沒有可作圖的數據。',
    pivotChartRequiresPivotMode: '樞紐圖需要啟用樞紐模式。',
    chartSettingsToolbarTooltip: '菜單',
    chartLinkToolbarTooltip: '鏈接到格線',
    chartUnlinkToolbarTooltip: '從格線取消鏈接',
    chartDownloadToolbarTooltip: '下載圖表',
    chartMenuToolbarTooltip: '菜單',
    chartEdit: '編輯圖表',
    chartAdvancedSettings: '高級設置',
    chartLink: '鏈接到格線',
    chartUnlink: '從格線取消鏈接',
    chartDownload: '下載圖表',
    histogramFrequency: '頻率',
    seriesChartType: '系列圖表類型',
    seriesType: '系列類型',
    secondaryAxis: '次軸',
    seriesAdd: '添加系列',
    categoryAdd: '添加類別',
    bar: '條形圖',
    column: '柱形圖',
    histogram: '直方圖',
    advancedSettings: '高級設置',
    direction: '方向',
    horizontal: '水平',
    vertical: '垂直',
    seriesGroupType: '組類型',
    groupedSeriesGroupType: '分組',
    stackedSeriesGroupType: '堆疊',
    normalizedSeriesGroupType: '100% 堆疊',
    legendEnabled: '啟用',
    invalidColor: '顏色值無效',
    groupedColumnFull: '分組柱形圖',
    stackedColumnFull: '堆疊柱形圖',
    normalizedColumnFull: '100% 堆疊柱形圖',
    groupedBarFull: '分組條形圖',
    stackedBarFull: '堆疊條形圖',
    normalizedBarFull: '100% 堆疊條形圖',
    stackedAreaFull: '堆積面積圖',
    normalizedAreaFull: '100% 堆積面積圖',
    customCombo: '自訂組合圖',
    funnel: '漏斗',
    coneFunnel: '錐形漏斗',
    pyramid: '金字塔',
    funnelGroup: '漏斗',
    funnelTooltip: '漏斗',
    coneFunnelTooltip: '錐形漏斗',
    pyramidTooltip: '金字塔',
    dropOff: '下降',
    stageLabels: '階段標籤',
    reverse: '反轉',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}。級別 ${variable}。按 ENTER 鍵編輯。',
    ariaAdvancedFilterBuilderItemValidation: '${variable}。級別 ${variable}。${variable} 按 ENTER 鍵編輯。',
    ariaAdvancedFilterBuilderList: '進階篩選器構建器列表',
    ariaAdvancedFilterBuilderFilterItem: '篩選條件',
    ariaAdvancedFilterBuilderGroupItem: '篩選組',
    ariaAdvancedFilterBuilderColumn: '欄',
    ariaAdvancedFilterBuilderOption: '選項',
    ariaAdvancedFilterBuilderValueP: '值',
    ariaAdvancedFilterBuilderJoinOperator: '連接運算子',
    ariaAdvancedFilterInput: '進階篩選輸入',
    ariaChecked: '已勾選',
    ariaColumn: '欄',
    ariaColumnGroup: '欄組',
    ariaColumnFiltered: '欄已篩選',
    ariaColumnSelectAll: '切換所有列的可見性',
    ariaDateFilterInput: '日期篩選輸入',
    ariaDefaultListName: '列表',
    ariaFilterColumnsInput: '篩選欄輸入',
    ariaFilterFromValue: '從值篩選',
    ariaFilterInput: '篩選輸入',
    ariaFilterList: '篩選列表',
    ariaFilterToValue: '篩選到值',
    ariaFilterValue: '篩選值',
    ariaFilterMenuOpen: '打開篩選器選單',
    ariaFilteringOperator: '篩選運算子',
    ariaHidden: '隱藏',
    ariaIndeterminate: '不確定',
    ariaInputEditor: '輸入編輯器',
    ariaMenuColumn: '按 ALT + DOWN 鍵打開欄選單',
    ariaFilterColumn: '按 CTRL + ENTER 鍵打開篩選',
    ariaRowDeselect: '按 SPACE 鍵取消選取此行',
    ariaHeaderSelection: '具有標題選擇的列',
    ariaSelectAllCells: '按空白鍵以選擇所有儲存格',
    ariaRowSelectAll: '按 SPACE 鍵切換所有行的選擇',
    ariaRowToggleSelection: '按 SPACE 鍵切換行的選擇',
    ariaRowSelect: '按 SPACE 鍵選取此行',
    ariaRowSelectionDisabled: '此行已禁用行選擇',
    ariaSearch: '搜尋',
    ariaSortableColumn: '按 ENTER 鍵排序',
    ariaToggleVisibility: '按 SPACE 鍵切換可見性',
    ariaToggleCellValue: '按 SPACE 鍵切換單元格值',
    ariaUnchecked: '未勾選',
    ariaVisible: '可見',
    ariaSearchFilterValues: '搜尋篩選值',
    ariaPageSizeSelectorLabel: '每頁大小',
    ariaChartMenuClose: '關閉圖表編輯選單',
    ariaChartSelected: '已選擇',
    ariaSkeletonCellLoadingFailed: '行加載失敗',
    ariaSkeletonCellLoading: '行數據加載中',
    ariaDeferSkeletonCellLoading: '儲存格正在載入',

    // ARIA for Batch Edit
    ariaPendingChange: '待處理的變更',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: '行群組',
    ariaValuesDropZonePanelLabel: '數值',
    ariaPivotDropZonePanelLabel: '列標籤',
    ariaDropZoneColumnComponentDescription: '按 DELETE 刪除',
    ariaDropZoneColumnValueItemDescription: '按 ENTER 更改聚合類型',
    ariaDropZoneColumnGroupItemDescription: '按 ENTER 排序',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' 的 ',
    ariaDropZoneColumnComponentSortAscending: '升序',
    ariaDropZoneColumnComponentSortDescending: '降序',
    ariaLabelDialog: '對話框',
    ariaLabelColumnMenu: '欄位選單',
    ariaLabelColumnFilter: '欄位篩選器',
    ariaLabelSelectField: '選擇欄位',

    // Cell Editor
    ariaLabelCellEditor: '單元格編輯器',
    ariaValidationErrorPrefix: '單元格編輯器驗證',
    ariaLabelLoadingContextMenu: '正在載入內容選單',

    // aria labels for rich select
    ariaLabelRichSelectField: '豐富選擇字段',
    ariaLabelRichSelectToggleSelection: '按下空格鍵以切換選擇',
    ariaLabelRichSelectDeselectAllItems: '按下刪除鍵以取消選擇所有項目',
    ariaLabelRichSelectDeleteSelection: '按下刪除鍵以取消選擇項目',
    ariaLabelTooltip: '工具提示',
    ariaLabelContextMenu: '上下文菜單',
    ariaLabelSubMenu: '子菜單',
    ariaLabelAggregationFunction: '聚合函數',
    ariaLabelAdvancedFilterAutocomplete: '高級過濾自動完成',
    ariaLabelAdvancedFilterBuilderAddField: '高級過濾構建器添加字段',
    ariaLabelAdvancedFilterBuilderColumnSelectField: '高級過濾構建器列選擇字段',
    ariaLabelAdvancedFilterBuilderOptionSelectField: '高級過濾構建器選項選擇字段',
    ariaLabelAdvancedFilterBuilderJoinSelectField: '高級過濾構建器聯接運算符選擇字段',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: '欄位列表',
    ariaFilterPanelList: '篩選列表',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: '新增篩選欄位',
    ariaLabelFilterCardDelete: '刪除篩選器',
    ariaLabelFilterCardHasEdits: '有編輯',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: '真',
    false: '假',
    invalidDate: '無效日期',
    invalidNumber: '無效數字',
    january: '一月',
    february: '二月',
    march: '三月',
    april: '四月',
    may: '五月',
    june: '六月',
    july: '七月',
    august: '八月',
    september: '九月',
    october: '十月',
    november: '十一月',
    december: '十二月',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS AM/PM',
};
