@use 'ag';

@mixin output {
    .ag-rtl {
        text-align: right;
    }

    .ag-root-wrapper {
        border-radius: var(--ag-wrapper-border-radius);
        border: var(--ag-borders) var(--ag-border-color);
    }

    .ag-row > .ag-cell-wrapper.ag-row-group {
        padding-left: calc(
            var(--ag-cell-horizontal-padding) + var(--ag-row-group-indent-size) * var(--ag-indentation-level)
        );
    }

    .ag-cell-wrapper.ag-row-group,
    .ag-cell-wrapper.ag-row-group-leaf-indent,
    .ag-cell-wrapper.ag-pivot-leaf-group {
        padding-left: calc(var(--ag-indentation-level) * var(--ag-row-group-indent-size));
    }

    .ag-cell-wrapper > .ag-group-checkbox-spacing {
        width: var(--ag-icon-size);
        margin-right: var(--ag-cell-widget-spacing);
    }

    .ag-row-group-leaf-indent {
        @include ag.unthemed-rtl(
            (
                margin-left: var(--ag-row-group-indent-size),
            )
        );
    }

    .ag-row:not(.ag-row-level-0) .ag-pivot-leaf-group {
        margin-left: var(--ag-row-group-indent-size);
    }

    .ag-value-change-delta {
        padding-right: 2px;
    }

    .ag-value-change-delta-up {
        color: var(--ag-value-change-delta-up-color);
    }

    .ag-value-change-delta-down {
        color: var(--ag-value-change-delta-down-color);
    }

    .ag-value-change-value {
        background-color: transparent;
        border-radius: 1px;
        padding-left: 1px;
        padding-right: 1px;
        transition: background-color 1s;
    }

    .ag-value-change-value-highlight {
        background-color: var(--ag-value-change-value-highlight-background-color);
        transition: background-color 0.1s;
    }

    .ag-cell-data-changed {
        background-color: var(--ag-value-change-value-highlight-background-color) !important;
    }

    .ag-cell-data-changed-animation {
        background-color: transparent;
    }

    .ag-cell-highlight {
        background-color: var(--ag-range-selection-highlight-color) !important;
    }

    .ag-row,
    .ag-spanned-row {
        color: var(--ag-data-color);
    }

    .ag-row {
        height: var(--ag-row-height);
        background-color: var(--ag-background-color);
        border-bottom: var(--ag-row-border-style) var(--ag-row-border-color) var(--ag-row-border-width);
        &.ag-row-editing-invalid {
            background-color: var(--ag-full-row-invalid-background-color);
        }
    }

    .ag-spanned-cell-wrapper {
        background-color: var(--ag-background-color);
        position: absolute;
    }

    .ag-spanned-cell-wrapper > .ag-spanned-cell {
        display: block;
        position: relative;
    }

    .ag-row-highlight-above::after,
    .ag-row-highlight-inside::after,
    .ag-row-highlight-below::after {
        content: '';
        position: absolute;
        width: calc(100% - 1px);
        height: 1px;
        background-color: var(--ag-range-selection-border-color);
        left: 1px;
        pointer-events: none;
    }

    .ag-row-highlight-above::after {
        top: 0;
    }

    .ag-row-highlight-below::after {
        bottom: 0;
    }

    .ag-row-highlight-indent::after {
        display: block;
        width: auto;
        left: calc(
            2 * (var(--ag-cell-widget-spacing) + var(--ag-icon-size)) + var(--ag-cell-horizontal-padding) +
                var(--ag-row-highlight-level) * var(--ag-row-group-indent-size)
        );
        right: 1px;
    }

    .ag-row-highlight-inside::after {
        display: block;
        width: auto;
        height: auto;
        inset: 0;
        background-color: var(--ag-selected-row-background-color);
        border: 1px solid var(--ag-range-selection-border-color);
    }

    .ag-row-odd {
        background-color: var(--ag-odd-row-background-color);
    }

    // NOTE: these don't need an RTL version because the "left spacer" is always the one visually on the left
    .ag-body-horizontal-scroll:not(.ag-scrollbar-invisible) {
        .ag-horizontal-left-spacer:not(.ag-scroller-corner) {
            border-right: var(--ag-borders-critical) var(--ag-border-color);
        }

        .ag-horizontal-right-spacer:not(.ag-scroller-corner) {
            border-left: var(--ag-borders-critical) var(--ag-border-color);
        }
    }

    .ag-row-selected::before {
        content: '';
        background-color: var(--ag-selected-row-background-color);
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
    }

    .ag-row-hover:not(.ag-full-width-row)::before,
    .ag-row-hover.ag-full-width-row.ag-row-group::before {
        // add a background on hover - use an absolutely positioned rectangle instead of
        // a `background-color` so that `--ag-row-hover-color: transparent` doesn't remove
        // an existing background color
        content: '';
        background-color: var(--ag-row-hover-color);
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        pointer-events: none;
    }

    // Default the content position to relative so that it doesn't
    // appear under the background
    .ag-row.ag-full-width-row.ag-row-group > * {
        position: relative;
    }

    .ag-row-hover.ag-row-selected::before {
        background-color: var(--ag-row-hover-color);
        // overlay the background color on the hover color when hovering over a selected row,
        // if the background color is semi-transparent it will blend properly
        background-image: linear-gradient(
            var(--ag-selected-row-background-color),
            var(--ag-selected-row-background-color)
        );
    }

    .ag-column-hover {
        background-color: var(--ag-column-hover-color);
    }

    .ag-header-range-highlight {
        background-color: var(--ag-range-header-highlight-color);
    }

    .ag-right-aligned-cell {
        @include ag.unthemed-rtl(
            (
                text-align: right,
            )
        );
    }

    // right-align numeric values in cells with wrappers
    .ag-right-aligned-cell .ag-cell-value,
    .ag-right-aligned-cell .ag-group-value {
        @include ag.unthemed-rtl(
            (
                margin-left: auto,
            )
        );
    }

    .ag-right-aligned-cell .ag-skeleton-effect {
        @include ag.unthemed-rtl(
            (
                margin-left: auto,
            )
        );
    }

    .ag-cell,
    .ag-full-width-row .ag-cell-wrapper.ag-row-group {
        // assign internal variables to simplify the `line-height` value
        --ag-internal-calculated-line-height: var(
            --ag-line-height,
            calc(var(--ag-row-height) - var(--ag-row-border-width))
        );
        --ag-internal-padded-row-height: calc(var(--ag-row-height) - var(--ag-row-border-width) - 2px);
        // draw a transparent border so that the cell size doesn't change when we add a range border
        border: 1px solid transparent;
        // adjust for transparent border
        // Needed to capitalize min, because it clashes with old versions of Sass
        line-height: Min(var(--ag-internal-calculated-line-height), var(--ag-internal-padded-row-height));
        padding-left: calc(
            var(--ag-cell-horizontal-padding) - 1px + var(--ag-row-group-indent-size) * var(--ag-indentation-level)
        );
        padding-right: calc(var(--ag-cell-horizontal-padding) - 1px);
        -webkit-font-smoothing: subpixel-antialiased;
    }

    // in full width rows, a cell renderer is rendered directly into a row with no cell in between,
    // in which case we need to apply the padding to the cell renderer's wrapper.
    .ag-row > .ag-cell-wrapper {
        padding-left: calc(var(--ag-cell-horizontal-padding) - 1px);
        padding-right: calc(var(--ag-cell-horizontal-padding) - 1px);
    }

    .ag-row-dragging {
        cursor: move;
        opacity: 0.5;
    }

    .ag-cell-inline-editing {
        border: 1px solid var(--ag-border-color);
        border-radius: var(--ag-card-radius);
        box-shadow: var(--ag-card-shadow);
        padding: 0;
        // do not add `height` here.
        // height: (--ag-row-height) breaks gridOptions.rowHeight for the cellEditor
        background-color: var(--ag-control-panel-background-color);
    }

    .ag-popup-editor .ag-large-text,
    .ag-autocomplete-list-popup {
        @include ag.card();
        background-color: var(--ag-control-panel-background-color);
        padding: 0;
    }

    .ag-large-text-input {
        height: auto;
        padding: var(--ag-cell-horizontal-padding);
    }

    .ag-rtl .ag-large-text-input textarea {
        resize: none;
    }

    .ag-details-row {
        padding: calc(var(--ag-grid-size) * 5);
        background-color: var(--ag-background-color);
    }

    .ag-layout-auto-height,
    .ag-layout-print {
        .ag-center-cols-viewport,
        .ag-center-cols-container {
            min-height: 50px;
        }
    }

    .ag-overlay-loading-wrapper {
        background-color: var(--ag-modal-overlay-background-color);
    }

    .ag-overlay-loading-center {
        @include ag.card();
    }

    .ag-skeleton-container {
        width: 100%;
        height: 100%;
        align-content: center;
    }

    .ag-skeleton-effect {
        background-color: var(--ag-row-loading-skeleton-effect-color);
        width: 100%;
        height: 1em;
        border-radius: 0.25rem;
        animation: ag-skeleton-loading 1.5s ease-in-out 0.5s infinite;
    }

    @keyframes ag-skeleton-loading {
        0% {
            opacity: 1;
        }
        50% {
            opacity: 0.4;
        }
        100% {
            opacity: 1;
        }
    }

    .ag-loading {
        @include ag.unthemed-rtl(
            (
                padding-left: var(--ag-cell-horizontal-padding),
            )
        );
        display: flex;
        height: 100%;
        align-items: center;
    }

    .ag-loading-icon {
        @include ag.unthemed-rtl(
            (
                padding-right: var(--ag-cell-widget-spacing),
            )
        );
    }

    .ag-icon-loading {
        animation-name: spin;
        animation-duration: 1000ms;
        animation-iteration-count: infinite;
        animation-timing-function: linear;
    }

    @keyframes spin {
        from {
            transform: rotate(0deg);
        }
        to {
            transform: rotate(360deg);
        }
    }

    .ag-floating-top:not(.ag-invisible) {
        border-bottom: var(--ag-borders-critical) var(--ag-border-color);
    }
    .ag-floating-bottom:not(.ag-invisible) {
        border-top: var(--ag-borders-critical) var(--ag-border-color);
    }

    .ag-find-cell {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ag-find-match {
        color: var(--ag-find-match-color);
        background-color: var(--ag-find-match-background-color);
    }

    .ag-find-active-match {
        color: var(--ag-find-active-match-color);
        background-color: var(--ag-find-active-match-background-color);
    }
}
