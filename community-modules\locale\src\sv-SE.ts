/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_SE = {
    // Set Filter
    selectAll: '(Välj alla)',
    selectAllSearchResults: '(Välj alla sökresultat)',
    addCurrentSelectionToFilter: 'Lägg till nuvarande val till filter',
    searchOoo: 'Sök...',
    blanks: '(<PERSON><PERSON>)',
    noMatches: 'Inga matchningar',

    // Number Filter & Text Filter
    filterOoo: 'Filter...',
    equals: 'Lika med',
    notEqual: 'Inte lika med',
    blank: 'Tom',
    notBlank: 'Inte tom',
    empty: 'Välj en',

    // Number Filter
    lessThan: 'Mindre än',
    greaterThan: 'Större än',
    lessThanOrEqual: '<PERSON>re än eller lika med',
    greaterThanOrEqual: '<PERSON><PERSON>rre än eller lika med',
    inRange: '<PERSON>lan',
    inRangeStart: '<PERSON>ån',
    inRangeEnd: 'Till',

    // Text Filter
    contains: 'Innehåller',
    notContains: 'Innehåller inte',
    startsWith: 'Börjar med',
    endsWith: 'Slutar med',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Före',
    after: 'Efter',

    // Filter Conditions
    andCondition: 'OCH',
    orCondition: 'ELLER',

    // Filter Buttons
    applyFilter: 'Verkställ',
    resetFilter: 'Återställ',
    clearFilter: 'Rensa',
    cancelFilter: 'Avbryt',

    // Filter Titles
    textFilter: 'Textfilter',
    numberFilter: 'Nummerfilter',
    dateFilter: 'Datumfilter',
    setFilter: 'Setfilter',

    // Group Column Filter
    groupFilterSelect: 'Välj fält:',

    // New Filter Tool Panel
    filterSummaryInactive: 'är (Alla)',
    filterSummaryContains: 'innehåller',
    filterSummaryNotContains: 'innehåller inte',
    filterSummaryTextEquals: 'är lika med',
    filterSummaryTextNotEqual: 'är inte lika med',
    filterSummaryStartsWith: 'börjar med',
    filterSummaryEndsWith: 'slutar med',
    filterSummaryBlank: 'är tom',
    filterSummaryNotBlank: 'är inte tom',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'mellan',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'är (Alla)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'är (${variable})',
    filterSummaryListLong: 'är (${variable}) och ${variable} mer',
    addFilterCard: 'Lägg till filter',
    agTextColumnFilterDisplayName: 'Enkel filter',
    agNumberColumnFilterDisplayName: 'Enkel filter',
    agDateColumnFilterDisplayName: 'Enkel filter',
    agSetColumnFilterDisplayName: 'Valfilter',
    agMultiColumnFilterDisplayName: 'Kombinationsfilter',
    addFilterPlaceholder: 'Sök kolumner...',

    // Advanced Filter
    advancedFilterContains: 'innehåller',
    advancedFilterNotContains: 'innehåller-inte',
    advancedFilterTextEquals: 'är-lika-med',
    advancedFilterTextNotEqual: 'är-inte-lika-med',
    advancedFilterStartsWith: 'börjar-med',
    advancedFilterEndsWith: 'slutar-med',
    advancedFilterBlank: 'är-tom',
    advancedFilterNotBlank: 'är-inte-tom',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'är-sann',
    advancedFilterFalse: 'är-falsk',
    advancedFilterAnd: 'OCH',
    advancedFilterOr: 'ELLER',
    advancedFilterApply: 'Tillämpa',
    advancedFilterBuilder: 'Byggare',
    advancedFilterValidationMissingColumn: 'Kolumn saknas',
    advancedFilterValidationMissingOption: 'Alternativ saknas',
    advancedFilterValidationMissingValue: 'Värde saknas',
    advancedFilterValidationInvalidColumn: 'Kolumn hittades inte',
    advancedFilterValidationInvalidOption: 'Alternativ hittades inte',
    advancedFilterValidationMissingQuote: 'Värdet saknar slutcitat',
    advancedFilterValidationNotANumber: 'Värdet är inte ett nummer',
    advancedFilterValidationInvalidDate: 'Värdet är inte ett giltigt datum',
    advancedFilterValidationMissingCondition: 'Villkor saknas',
    advancedFilterValidationJoinOperatorMismatch: 'Kombinatorer inom ett villkor måste vara desamma',
    advancedFilterValidationInvalidJoinOperator: 'Kombinator hittades inte',
    advancedFilterValidationMissingEndBracket: 'Saknar slutparentes',
    advancedFilterValidationExtraEndBracket: 'För många slutparenteser',
    advancedFilterValidationMessage: 'Uttrycket har ett fel. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Uttrycket har ett fel. ${variable} i slutet av uttrycket.',
    advancedFilterBuilderTitle: 'Avancerad Filter',
    advancedFilterBuilderApply: 'Tillämpa',
    advancedFilterBuilderCancel: 'Avbryt',
    advancedFilterBuilderAddButtonTooltip: 'Lägg till Filter eller Grupp',
    advancedFilterBuilderRemoveButtonTooltip: 'Ta bort',
    advancedFilterBuilderMoveUpButtonTooltip: 'Flytta upp',
    advancedFilterBuilderMoveDownButtonTooltip: 'Flytta ner',
    advancedFilterBuilderAddJoin: 'Lägg till Grupp',
    advancedFilterBuilderAddCondition: 'Lägg till Filter',
    advancedFilterBuilderSelectColumn: 'Välj en kolumn',
    advancedFilterBuilderSelectOption: 'Välj ett alternativ',
    advancedFilterBuilderEnterValue: 'Ange ett värde...',
    advancedFilterBuilderValidationAlreadyApplied: 'Nuvarande filter redan tillämpat.',
    advancedFilterBuilderValidationIncomplete: 'Inte alla villkor är fullständiga.',
    advancedFilterBuilderValidationSelectColumn: 'Måste välja en kolumn.',
    advancedFilterBuilderValidationSelectOption: 'Måste välja ett alternativ.',
    advancedFilterBuilderValidationEnterValue: 'Måste ange ett värde.',

    // Editor Validation Errors
    minDateValidation: 'Datum måste vara efter ${variable}',
    maxDateValidation: 'Datum måste vara före ${variable}',
    maxLengthValidation: 'Måste vara ${variable} tecken eller färre.',
    minValueValidation: 'Måste vara större än eller lika med ${variable}',
    maxValueValidation: 'Måste vara mindre än eller lika med ${variable}',
    invalidSelectionValidation: 'Ogiltigt val.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Kolumner',
    filters: 'Filter',

    // columns tool panel
    pivotMode: 'Pivotläge',
    groups: 'Radgrupper',
    rowGroupColumnsEmptyMessage: 'Dra hit för att ställa in radgrupper',
    values: 'Värden',
    valueColumnsEmptyMessage: 'Dra hit för att aggregera',
    pivots: 'Kolumnetiketter',
    pivotColumnsEmptyMessage: 'Dra hit för att ställa in kolumnetiketter',

    // Header of the Default Group Column
    group: 'Grupp',

    // Row Drag
    rowDragRow: 'raden',
    rowDragRows: 'rader',

    // Other
    loadingOoo: 'Laddar...',
    loadingError: 'FEL',
    noRowsToShow: 'Inga rader att visa',
    enabled: 'Aktiverad',

    // Menu
    pinColumn: 'Fäst Kolumn',
    pinLeft: 'Fäst Vänster',
    pinRight: 'Fäst Höger',
    noPin: 'Ingen Fästning',
    valueAggregation: 'Värdeaggregering',
    noAggregation: 'Ingen',
    autosizeThisColumn: 'Autosize Denna Kolumn',
    autosizeAllColumns: 'Autosize Alla Kolumner',
    groupBy: 'Gruppera efter',
    ungroupBy: 'Avgruppera efter',
    ungroupAll: 'Avgruppera Alla',
    addToValues: 'Lägg till ${variable} till värden',
    removeFromValues: 'Ta bort ${variable} från värden',
    addToLabels: 'Lägg till ${variable} till etiketter',
    removeFromLabels: 'Ta bort ${variable} från etiketter',
    resetColumns: 'Återställ Kolumner',
    expandAll: 'Expandera Alla Grupp-Rader',
    collapseAll: 'Stäng Alla Grupp-Rader',
    copy: 'Kopiera',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopiera Med Rubriker',
    copyWithGroupHeaders: 'Kopiera med Grupp-Rubriker',
    cut: 'Klipp ut',
    paste: 'Klistra in',
    ctrlV: 'Ctrl+V',
    export: 'Exportera',
    csvExport: 'CSV Export',
    excelExport: 'Excel Export',
    columnFilter: 'Kolumnfilter',
    columnChooser: 'Välj Kolumner',
    chooseColumns: 'Välj kolumner',
    sortAscending: 'Sortera Stigande',
    sortDescending: 'Sortera Fallande',
    sortUnSort: 'Rensa Sortering',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Summa',
    first: 'Första',
    last: 'Sista',
    min: 'Min',
    max: 'Max',
    none: 'Ingen',
    count: 'Antal',
    avg: 'Genomsnitt',
    filteredRows: 'Filtrerade',
    selectedRows: 'Valda',
    totalRows: 'Totala Rader',
    totalAndFilteredRows: 'Rader',
    more: 'Mer',
    to: 'till',
    of: 'av',
    page: 'Sida',
    pageLastRowUnknown: '?',
    nextPage: 'Nästa Sida',
    lastPage: 'Sista Sida',
    firstPage: 'Första Sida',
    previousPage: 'Föregående Sida',
    pageSizeSelectorLabel: 'Sidstorlek:',
    footerTotal: 'Totalt',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Rulla ${variable} i vy',

    // Pivoting
    pivotColumnGroupTotals: 'Summa',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivot Diagram & Pivotläge',
    pivotChart: 'Pivot Diagram',
    chartRange: 'Diagramområde',
    columnChart: 'Kolumn',
    groupedColumn: 'Grupperad',
    stackedColumn: 'Staplad',
    normalizedColumn: '100% Staplad',
    barChart: 'Staplad',
    groupedBar: 'Grupperad',
    stackedBar: 'Staplad',
    normalizedBar: '100% Staplad',
    pieChart: 'Cirkeldiagram',
    pie: 'Cirkeldiagram',
    donut: 'Ringdiagram',
    lineChart: 'Linje',
    stackedLine: 'Staplad',
    normalizedLine: '100% Staplad',
    xyChart: 'X Y (Spridning)',
    scatter: 'Spridningsdiagram',
    bubble: 'Bubbeldiagram',
    areaChart: 'Ytdiagram',
    area: 'Ytdiagram',
    stackedArea: 'Staplad',
    normalizedArea: '100% Staplad',
    histogramChart: 'Histogram',
    polarChart: 'Polardiagram',
    radarLine: 'Radarlinje',
    radarArea: 'Radaryta',
    nightingale: 'Nightingale',
    radialColumn: 'Radiell Kolumn',
    radialBar: 'Radiell Stapel',
    statisticalChart: 'Statistisk',
    boxPlot: 'Låddiagram',
    rangeBar: 'Områdesstapel',
    rangeArea: 'Områdesyta',
    hierarchicalChart: 'Hierarkisk',
    treemap: 'Träddiagram',
    sunburst: 'Solburst Diagram',
    specializedChart: 'Specialiserad',
    waterfall: 'Vattenfall',
    heatmap: 'Värmekarta',
    combinationChart: 'Kombinationsdiagram',
    columnLineCombo: 'Kolumn & Linje',
    AreaColumnCombo: 'Yta & Kolumn',

    // Charts
    pivotChartTitle: 'Pivotdiagram',
    rangeChartTitle: 'Omfångsdiagram',
    settings: 'Diagram',
    data: 'Inställningar',
    format: 'Anpassa',
    categories: 'Kategorier',
    defaultCategory: '(Ingen)',
    series: 'Serier',
    switchCategorySeries: 'Växla Kategori / Serie',
    categoryValues: 'Kategori Värden',
    seriesLabels: 'Serieetiketter',
    aggregate: 'Sammanfoga',
    xyValues: 'X Y Värden',
    paired: 'Parat Läge',
    axis: 'Axel',
    xAxis: 'Horisontell Axel',
    yAxis: 'Vertikal Axel',
    polarAxis: 'Polar Axel',
    radiusAxis: 'Radie Axel',
    navigator: 'Navigering',
    zoom: 'Zooma',
    animation: 'Animation',
    crosshair: 'Hårkors',
    color: 'Färg',
    thickness: 'Tjocklek',
    preferredLength: 'Föredragen Längd',
    xType: 'X Typ',
    axisType: 'Axel Typ',
    automatic: 'Automatisk',
    category: 'Kategori',
    number: 'Nummer',
    time: 'Tid',
    timeFormat: 'Tidsformat',
    autoRotate: 'Auto Rotera',
    labelRotation: 'Rotation',
    circle: 'Cirkel',
    polygon: 'Polygon',
    square: 'Fyrkant',
    cross: 'Kors',
    diamond: 'Diamant',
    plus: 'Plus',
    triangle: 'Triangel',
    heart: 'Hjärta',
    orientation: 'Orientering',
    fixed: 'Fast',
    parallel: 'Parallell',
    perpendicular: 'Vinkelrät',
    radiusAxisPosition: 'Position',
    ticks: 'Tidsintervall',
    gridLines: 'Rutnät',
    width: 'Bredd',
    height: 'Höjd',
    length: 'Längd',
    padding: 'Utfyllnad',
    spacing: 'Avstånd',
    chartStyle: 'Diagramstil',
    title: 'Titel',
    chartTitles: 'Titlar',
    chartTitle: 'Diagramtitel',
    chartSubtitle: 'Undertitel',
    horizontalAxisTitle: 'Titel Horisontell Axel',
    verticalAxisTitle: 'Titel Vertikal Axel',
    polarAxisTitle: 'Polar Axel Titel',
    titlePlaceholder: 'Diagramtitel',
    background: 'Bakgrund',
    font: 'Teckensnitt',
    weight: 'Vikt',
    top: 'Topp',
    right: 'Höger',
    bottom: 'Botten',
    left: 'Vänster',
    labels: 'Etiketter',
    calloutLabels: 'Upplysningsetiketter',
    sectorLabels: 'Sektoretiketter',
    positionRatio: 'Positionskvot',
    size: 'Storlek',
    shape: 'Form',
    minSize: 'Minsta Storlek',
    maxSize: 'Största Storlek',
    legend: 'Förklaring',
    position: 'Position',
    markerSize: 'Markörstorlek',
    markerStroke: 'Markörlinje',
    markerPadding: 'Markörutfyllnad',
    itemSpacing: 'Avstånd mellan objekt',
    itemPaddingX: 'Objektutfyllnad X',
    itemPaddingY: 'Objektutfyllnad Y',
    layoutHorizontalSpacing: 'Horisontellt Avstånd',
    layoutVerticalSpacing: 'Vertikalt Avstånd',
    strokeWidth: 'Linjetjocklek',
    offset: 'Förskjutning',
    offsets: 'Förskjutningar',
    tooltips: 'Verktygstips',
    callout: 'Upplysning',
    markers: 'Markörer',
    shadow: 'Skugga',
    blur: 'Oskärpa',
    xOffset: 'X Förskjutning',
    yOffset: 'Y Förskjutning',
    lineWidth: 'Linjebredd',
    lineDash: 'Linjedasningar',
    lineDashOffset: 'Das förskjutning',
    scrollingZoom: 'Scrolla',
    scrollingStep: 'Scrollsteg',
    selectingZoom: 'Markera',
    durationMillis: 'Varaktighet (ms)',
    crosshairLabel: 'Etikett',
    crosshairSnap: 'Fäst vid Nod',
    normal: 'Normal',
    bold: 'Fet',
    italic: 'Kursiv',
    boldItalic: 'Fet Kursiv',
    predefined: 'Fördefinierad',
    fillOpacity: 'Fyllnadsopacitet',
    strokeColor: 'Linjefärg',
    strokeOpacity: 'Linjeopacitet',
    miniChart: 'Mini-Diagram',
    histogramBinCount: 'Binantal',
    connectorLine: 'Kopplingslinje',
    seriesItems: 'Serieobjekt',
    seriesItemType: 'Objekttyp',
    seriesItemPositive: 'Positiv',
    seriesItemNegative: 'Negativ',
    seriesItemLabels: 'Objektetiketter',
    columnGroup: 'Kolumn',
    barGroup: 'Stapeldiagram',
    pieGroup: 'Cirkeldiagram',
    lineGroup: 'Linjediagram',
    scatterGroup: 'X Y (Spridning)',
    areaGroup: 'Yta',
    polarGroup: 'Polär',
    statisticalGroup: 'Statistisk',
    hierarchicalGroup: 'Hierarkisk',
    specializedGroup: 'Specialiserad',
    combinationGroup: 'Kombinerad',
    groupedColumnTooltip: 'Grupperade',
    stackedColumnTooltip: 'Staplade',
    normalizedColumnTooltip: '100% Staplade',
    groupedBarTooltip: 'Grupperade',
    stackedBarTooltip: 'Staplade',
    normalizedBarTooltip: '100% Staplade',
    pieTooltip: 'Cirkeldiagram',
    donutTooltip: 'Donut',
    lineTooltip: 'Linjediagram',
    stackedLineTooltip: 'Staplat',
    normalizedLineTooltip: '100% Staplat',
    groupedAreaTooltip: 'Yta',
    stackedAreaTooltip: 'Staplad Yta',
    normalizedAreaTooltip: '100% Staplad Yta',
    scatterTooltip: 'Spridning',
    bubbleTooltip: 'Bubblor',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Radiell Kolumn',
    radialBarTooltip: 'Radiell Stapel',
    radarLineTooltip: 'Radardiagram',
    radarAreaTooltip: 'Radardiagramyta',
    nightingaleTooltip: 'Näktergal',
    rangeBarTooltip: 'Omfång Stapel',
    rangeAreaTooltip: 'Omfång Yta',
    boxPlotTooltip: 'Låddiagram',
    treemapTooltip: 'Träddiagram',
    sunburstTooltip: 'Solstrålediagram',
    waterfallTooltip: 'Vattenfall',
    heatmapTooltip: 'Värmekarta',
    columnLineComboTooltip: 'Kolumn & Linje',
    areaColumnComboTooltip: 'Yta & Kolumn',
    customComboTooltip: 'Anpassad Kombination',
    innerRadius: 'Inre Radie',
    startAngle: 'Startvinkel',
    endAngle: 'Slutvinkel',
    reverseDirection: 'Omvänd Riktning',
    groupPadding: 'Grupp Padding',
    seriesPadding: 'Serie Padding',
    tile: 'Platta',
    whisker: 'Visp',
    cap: 'Topp',
    capLengthRatio: 'Längdförhållande',
    labelPlacement: 'Etikettplacering',
    inside: 'Inuti',
    outside: 'Utanför',
    noDataToChart: 'Ingen data tillgänglig för diagram.',
    pivotChartRequiresPivotMode: 'Pivotdiagram kräver Pivåläge aktiverat.',
    chartSettingsToolbarTooltip: 'Meny',
    chartLinkToolbarTooltip: 'Länkad till Rutnät',
    chartUnlinkToolbarTooltip: 'Olinkad från Rutnät',
    chartDownloadToolbarTooltip: 'Ladda Ned Diagram',
    chartMenuToolbarTooltip: 'Meny',
    chartEdit: 'Redigera Diagram',
    chartAdvancedSettings: 'Avancerade inställningar',
    chartLink: 'Länk till Rutnät',
    chartUnlink: 'Avlänk från Rutnät',
    chartDownload: 'Ladda Ned Diagram',
    histogramFrequency: 'Frekvens',
    seriesChartType: 'Seriediagram Typ',
    seriesType: 'Serietyp',
    secondaryAxis: 'Sekundär Axel',
    seriesAdd: 'Lägg till en serie',
    categoryAdd: 'Lägg till en kategori',
    bar: 'Stapeldiagram',
    column: 'Kolumn',
    histogram: 'Histogram',
    advancedSettings: 'Avancerade inställningar',
    direction: 'Riktning',
    horizontal: 'Horisontell',
    vertical: 'Vertikal',
    seriesGroupType: 'Grupptyp',
    groupedSeriesGroupType: 'Grupperad',
    stackedSeriesGroupType: 'Staplad',
    normalizedSeriesGroupType: '100% Staplad',
    legendEnabled: 'Aktiverad',
    invalidColor: 'Färgkoden är ogiltig',
    groupedColumnFull: 'Grupperad Kolumn',
    stackedColumnFull: 'Staplad Kolumn',
    normalizedColumnFull: '100% Staplad Kolumn',
    groupedBarFull: 'Grupperad Stapel',
    stackedBarFull: 'Staplad Stapel',
    normalizedBarFull: '100% Staplad Stapel',
    stackedAreaFull: 'Staplad Yta',
    normalizedAreaFull: '100% Staplad Yta',
    customCombo: 'Anpassad Kombination',
    funnel: 'Tratt',
    coneFunnel: 'Kontratt',
    pyramid: 'Pyramid',
    funnelGroup: 'Tratt',
    funnelTooltip: 'Tratt',
    coneFunnelTooltip: 'Kontratt',
    pyramidTooltip: 'Pyramid',
    dropOff: 'Avhopp',
    stageLabels: 'Stegetiketter',
    reverse: 'Omvänd',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Nivå ${variable}. Tryck ENTER för att redigera.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Nivå ${variable}. ${variable} Tryck ENTER för att redigera.',
    ariaAdvancedFilterBuilderList: 'Avancerad filterbyggarlista',
    ariaAdvancedFilterBuilderFilterItem: 'Filtervillkor',
    ariaAdvancedFilterBuilderGroupItem: 'Filtergrupp',
    ariaAdvancedFilterBuilderColumn: 'Kolumn',
    ariaAdvancedFilterBuilderOption: 'Alternativ',
    ariaAdvancedFilterBuilderValueP: 'Värde',
    ariaAdvancedFilterBuilderJoinOperator: 'Kombinationsoperator',
    ariaAdvancedFilterInput: 'Avancerad filterinmatning',
    ariaChecked: 'markerad',
    ariaColumn: 'Kolumn',
    ariaColumnGroup: 'Kolumngrupp',
    ariaColumnFiltered: 'Kolumn filtrerad',
    ariaColumnSelectAll: 'Växla alla kolumners synlighet',
    ariaDateFilterInput: 'Datumfilterinmatning',
    ariaDefaultListName: 'Lista',
    ariaFilterColumnsInput: 'Filterkolumnsinmatning',
    ariaFilterFromValue: 'Filtrera från värde',
    ariaFilterInput: 'Filterinmatning',
    ariaFilterList: 'Filterlista',
    ariaFilterToValue: 'Filtrera till värde',
    ariaFilterValue: 'Filtervärde',
    ariaFilterMenuOpen: 'Öppna filtermeny',
    ariaFilteringOperator: 'Filtreringsoperator',
    ariaHidden: 'dold',
    ariaIndeterminate: 'obestämd',
    ariaInputEditor: 'Inmatningsredigerare',
    ariaMenuColumn: 'Tryck ALT NEDÅT för att öppna kolumnmeny',
    ariaFilterColumn: 'Tryck CTRL ENTER för att öppna filter',
    ariaRowDeselect: 'Tryck SPACE för att avmarkera denna rad',
    ariaHeaderSelection: 'Kolumn med rubrikval',
    ariaSelectAllCells: 'Tryck på mellanslag för att markera alla celler',
    ariaRowSelectAll: 'Tryck SPACE för att växla markering av alla rader',
    ariaRowToggleSelection: 'Tryck SPACE för att växla radmarkering',
    ariaRowSelect: 'Tryck SPACE för att markera denna rad',
    ariaRowSelectionDisabled: 'Radmarkering är inaktiverad för denna rad',
    ariaSearch: 'Sök',
    ariaSortableColumn: 'Tryck ENTER för att sortera',
    ariaToggleVisibility: 'Tryck SPACE för att växla synlighet',
    ariaToggleCellValue: 'Tryck SPACE för att växla cellvärde',
    ariaUnchecked: 'omarkerad',
    ariaVisible: 'synlig',
    ariaSearchFilterValues: 'Sök filtervärden',
    ariaPageSizeSelectorLabel: 'Sidstorlek',
    ariaChartMenuClose: 'Stäng diagramredigeringsmeny',
    ariaChartSelected: 'Vald',
    ariaSkeletonCellLoadingFailed: 'Raden kunde inte laddas',
    ariaSkeletonCellLoading: 'Raddata laddas',
    ariaDeferSkeletonCellLoading: 'Cellen laddas',

    // ARIA for Batch Edit
    ariaPendingChange: 'Väntande ändring',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Radgrupper',
    ariaValuesDropZonePanelLabel: 'Värden',
    ariaPivotDropZonePanelLabel: 'Kolumnetiketter',
    ariaDropZoneColumnComponentDescription: 'Tryck på DELETE för att ta bort',
    ariaDropZoneColumnValueItemDescription: 'Tryck på ENTER för att ändra aggregeringstyp',
    ariaDropZoneColumnGroupItemDescription: 'Tryck på ENTER för att sortera',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' av ',
    ariaDropZoneColumnComponentSortAscending: 'stigande',
    ariaDropZoneColumnComponentSortDescending: 'fallande',
    ariaLabelDialog: 'Dialogruta',
    ariaLabelColumnMenu: 'Kolumnmeny',
    ariaLabelColumnFilter: 'Kolumnfilter',
    ariaLabelSelectField: 'Välj fält',

    // Cell Editor
    ariaLabelCellEditor: 'Celleditor',
    ariaValidationErrorPrefix: 'Celleditorvalidering',
    ariaLabelLoadingContextMenu: 'Laddar kontextmeny',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Rich Selectfält',
    ariaLabelRichSelectToggleSelection: 'Tryck på MELLANSLAG för att växla markering',
    ariaLabelRichSelectDeselectAllItems: 'Tryck på DELETE för att avmarkera alla objekt',
    ariaLabelRichSelectDeleteSelection: 'Tryck på DELETE för att avmarkera objekt',
    ariaLabelTooltip: 'Verktygstips',
    ariaLabelContextMenu: 'Kontekstmeny',
    ariaLabelSubMenu: 'Undermeny',
    ariaLabelAggregationFunction: 'Aggregeringsfunktion',
    ariaLabelAdvancedFilterAutocomplete: 'Avancerad Filter Autocomplete',
    ariaLabelAdvancedFilterBuilderAddField: 'Avancerad Filter Builder Lägg till fält',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Avancerad Filter Builder Kolumn Välj fält',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Avancerad Filter Builder Alternativ Välj fält',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Avancerad Filter Builder Join Operator Välj fält',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Kolumnlista',
    ariaFilterPanelList: 'Filterlista',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Lägg till filterfält',
    ariaLabelFilterCardDelete: 'Radera filter',
    ariaLabelFilterCardHasEdits: 'Har ändringar',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'Sann',
    false: 'Falsk',
    invalidDate: 'Ogiltigt datum',
    invalidNumber: 'Ogiltigt nummer',
    january: 'Januari',
    february: 'Februari',
    march: 'Mars',
    april: 'April',
    may: 'Maj',
    june: 'Juni',
    july: 'Juli',
    august: 'Augusti',
    september: 'September',
    october: 'Oktober',
    november: 'November',
    december: 'December',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS FM/EM',
};
