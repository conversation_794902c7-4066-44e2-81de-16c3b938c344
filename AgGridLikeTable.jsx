import React, { useRef, useState, useCallback, useEffect, useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
} from '@tanstack/react-table';

/**
 * 模拟 AG Grid 滚动体验的 TanStack Table
 */
function AgGridLikeTable({
  data,
  columns,
  height = 500,
  // AG Grid 风格的选项
  rowHeight = 48,
  rowBuffer = 30,
  debounceVerticalScrollbar = false,
  alwaysShowVerticalScroll = false,
  alwaysShowHorizontalScroll = false,
  suppressMaxRenderedRowRestriction = false,
}) {
  const tableContainerRef = useRef(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const [scrollDirection, setScrollDirection] = useState({ vertical: 0, horizontal: 0 });
  const [scrollVelocity, setScrollVelocity] = useState({ vertical: 0, horizontal: 0 });
  
  // 滚动状态引用
  const scrollingTimerRef = useRef(null);
  const rafIdRef = useRef(null);
  const lastScrollPositionRef = useRef({ top: 0, left: 0 });
  const lastScrollTimeRef = useRef(0);
  const scrollSamplesRef = useRef({ vertical: [], horizontal: [] });
  const isScrollingRef = useRef(false);
  
  // 行渲染状态
  const [renderedRowsCount, setRenderedRowsCount] = useState(0);
  const maxRenderedRows = suppressMaxRenderedRowRestriction ? Infinity : 500;
  
  // 使用 TanStack Table
  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });
  
  // 更新滚动速度和方向
  const updateScrollMetrics = useCallback((newScrollTop, newScrollLeft) => {
    const now = performance.now();
    const timeDelta = now - lastScrollTimeRef.current;
    
    if (timeDelta > 0 && lastScrollTimeRef.current > 0) {
      // 计算垂直和水平滚动速度
      const verticalVelocity = (newScrollTop - lastScrollPositionRef.current.top) / timeDelta;
      const horizontalVelocity = (newScrollLeft - lastScrollPositionRef.current.left) / timeDelta;
      
      // 保存滚动样本
      scrollSamplesRef.current.vertical.push(verticalVelocity);
      scrollSamplesRef.current.horizontal.push(horizontalVelocity);
      
      // 限制样本数量
      if (scrollSamplesRef.current.vertical.length > 5) {
        scrollSamplesRef.current.vertical.shift();
      }
      if (scrollSamplesRef.current.horizontal.length > 5) {
        scrollSamplesRef.current.horizontal.shift();
      }
      
      // 计算平均滚动速度
      const avgVerticalVelocity = scrollSamplesRef.current.vertical.reduce((sum, v) => sum + v, 0) / 
                                 scrollSamplesRef.current.vertical.length;
      const avgHorizontalVelocity = scrollSamplesRef.current.horizontal.reduce((sum, v) => sum + v, 0) / 
                                   scrollSamplesRef.current.horizontal.length;
      
      setScrollVelocity({
        vertical: avgVerticalVelocity,
        horizontal: avgHorizontalVelocity
      });
      
      // 确定滚动方向
      setScrollDirection({
        vertical: Math.sign(newScrollTop - lastScrollPositionRef.current.top),
        horizontal: Math.sign(newScrollLeft - lastScrollPositionRef.current.left)
      });
    }
    
    lastScrollTimeRef.current = now;
  }, []);
  
  // 预测滚动位置
  const predictedScrollPosition = useMemo(() => {
    if (!isScrolling) {
      return { top: scrollTop, left: scrollLeft };
    }
    
    // 基于当前速度预测位置
    const predictTime = 150; // 预测 150ms 后的位置
    
    const predictedTop = scrollTop + scrollVelocity.vertical * predictTime;
    const predictedLeft = scrollLeft + scrollVelocity.horizontal * predictTime;
    
    // 确保预测位置在有效范围内
    const maxScrollTop = data.length * rowHeight - (tableContainerRef.current?.clientHeight || 0);
    const maxScrollLeft = tableContainerRef.current?.scrollWidth - (tableContainerRef.current?.clientWidth || 0);
    
    return {
      top: Math.max(0, Math.min(predictedTop, maxScrollTop)),
      left: Math.max(0, Math.min(predictedLeft, maxScrollLeft))
    };
  }, [scrollTop, scrollLeft, scrollVelocity, isScrolling, data.length, rowHeight]);
  
  // 计算渲染范围
  const renderRange = useMemo(() => {
    if (!tableContainerRef.current) {
      return { start: 0, end: Math.min(data.length - 1, 50) };
    }
    
    const containerHeight = tableContainerRef.current.clientHeight;
    
    // 当前可见范围
    const visibleStart = Math.floor(scrollTop / rowHeight);
    const visibleEnd = Math.min(
      data.length - 1,
      Math.ceil((scrollTop + containerHeight) / rowHeight)
    );
    
    // 预测位置的可见范围
    const predictedStart = Math.floor(predictedScrollPosition.top / rowHeight);
    const predictedEnd = Math.min(
      data.length - 1,
      Math.ceil((predictedScrollPosition.top + containerHeight) / rowHeight)
    );
    
    // 合并当前和预测范围，并添加缓冲区
    let start = Math.max(0, Math.min(visibleStart, predictedStart) - rowBuffer);
    let end = Math.min(data.length - 1, Math.max(visibleEnd, predictedEnd) + rowBuffer);
    
    // 如果滚动速度很快，增加缓冲区
    if (Math.abs(scrollVelocity.vertical) > 1) {
      const extraBuffer = Math.ceil(Math.abs(scrollVelocity.