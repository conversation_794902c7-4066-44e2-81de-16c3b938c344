/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_PT = {
    // Set Filter
    selectAll: '(Selecionar Todos)',
    selectAllSearchResults: '(Selecionar Todos os Resultados da Pesquisa)',
    addCurrentSelectionToFilter: 'Adicionar seleção atual ao filtro',
    searchOoo: 'Pesquisar...',
    blanks: '(Em Branco)',
    noMatches: 'Sem correspondências',

    // Number Filter & Text Filter
    filterOoo: 'Filtrar...',
    equals: 'Igual',
    notEqual: 'Diferente',
    blank: 'Vazio',
    notBlank: 'Não vazio',
    empty: 'Escolher um',

    // Number Filter
    lessThan: 'Menor que',
    greaterThan: 'Maior que',
    lessThanOrEqual: 'Menor ou igual a',
    greaterThanOrEqual: '<PERSON><PERSON> ou igual a',
    inRange: 'Entre',
    inRangeStart: 'De',
    inRangeEnd: 'Até',

    // Text Filter
    contains: 'Contém',
    notContains: 'Não contém',
    startsWith: 'Começa com',
    endsWith: 'Termina com',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Antes',
    after: 'Depois',

    // Filter Conditions
    andCondition: 'E',
    orCondition: 'OU',

    // Filter Buttons
    applyFilter: 'Aplicar',
    resetFilter: 'Redefinir',
    clearFilter: 'Limpar',
    cancelFilter: 'Cancelar',

    // Filter Titles
    textFilter: 'Filtro de Texto',
    numberFilter: 'Filtro de Número',
    dateFilter: 'Filtro de Data',
    setFilter: 'Filtro de Conjunto',

    // Group Column Filter
    groupFilterSelect: 'Selecionar campo:',

    // New Filter Tool Panel
    filterSummaryInactive: 'é (Todos)',
    filterSummaryContains: 'contém',
    filterSummaryNotContains: 'não contém',
    filterSummaryTextEquals: 'é igual a',
    filterSummaryTextNotEqual: 'não é igual a',
    filterSummaryStartsWith: 'começa com',
    filterSummaryEndsWith: 'termina com',
    filterSummaryBlank: 'está em branco',
    filterSummaryNotBlank: 'não está em branco',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'entre',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'é (Todos)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'é (${variable})',
    filterSummaryListLong: 'é (${variable}) e ${variable} mais',
    addFilterCard: 'Adicionar Filtro',
    agTextColumnFilterDisplayName: 'Filtro Simples',
    agNumberColumnFilterDisplayName: 'Filtro Simples',
    agDateColumnFilterDisplayName: 'Filtro Simples',
    agSetColumnFilterDisplayName: 'Filtro de Seleção',
    agMultiColumnFilterDisplayName: 'Filtro Combinado',
    addFilterPlaceholder: 'Pesquisar colunas...',

    // Advanced Filter
    advancedFilterContains: 'contém',
    advancedFilterNotContains: 'não contém',
    advancedFilterTextEquals: 'igual a',
    advancedFilterTextNotEqual: 'diferente de',
    advancedFilterStartsWith: 'começa com',
    advancedFilterEndsWith: 'termina com',
    advancedFilterBlank: 'está em branco',
    advancedFilterNotBlank: 'não está em branco',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'é verdadeiro',
    advancedFilterFalse: 'é falso',
    advancedFilterAnd: 'E',
    advancedFilterOr: 'OU',
    advancedFilterApply: 'Aplicar',
    advancedFilterBuilder: 'Construtor',
    advancedFilterValidationMissingColumn: 'Coluna ausente',
    advancedFilterValidationMissingOption: 'Opção ausente',
    advancedFilterValidationMissingValue: 'Valor ausente',
    advancedFilterValidationInvalidColumn: 'Coluna não encontrada',
    advancedFilterValidationInvalidOption: 'Opção não encontrada',
    advancedFilterValidationMissingQuote: 'Valor está sem a aspa final',
    advancedFilterValidationNotANumber: 'Valor não é um número',
    advancedFilterValidationInvalidDate: 'Valor não é uma data válida',
    advancedFilterValidationMissingCondition: 'Condição ausente',
    advancedFilterValidationJoinOperatorMismatch: 'Operadores de junção dentro de uma condição devem ser iguais',
    advancedFilterValidationInvalidJoinOperator: 'Operador de junção não encontrado',
    advancedFilterValidationMissingEndBracket: 'Falta parêntese de fechamento',
    advancedFilterValidationExtraEndBracket: 'Parênteses de fechamento a mais',
    advancedFilterValidationMessage: 'Expressão contém um erro. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Expressão contém um erro. ${variable} no final da expressão.',
    advancedFilterBuilderTitle: 'Filtro Avançado',
    advancedFilterBuilderApply: 'Aplicar',
    advancedFilterBuilderCancel: 'Cancelar',
    advancedFilterBuilderAddButtonTooltip: 'Adicionar Filtro ou Grupo',
    advancedFilterBuilderRemoveButtonTooltip: 'Remover',
    advancedFilterBuilderMoveUpButtonTooltip: 'Mover para Cima',
    advancedFilterBuilderMoveDownButtonTooltip: 'Mover para Baixo',
    advancedFilterBuilderAddJoin: 'Adicionar Grupo',
    advancedFilterBuilderAddCondition: 'Adicionar Filtro',
    advancedFilterBuilderSelectColumn: 'Selecione uma coluna',
    advancedFilterBuilderSelectOption: 'Selecione uma opção',
    advancedFilterBuilderEnterValue: 'Insira um valor...',
    advancedFilterBuilderValidationAlreadyApplied: 'Filtro atual já aplicado.',
    advancedFilterBuilderValidationIncomplete: 'Nem todas as condições estão completas.',
    advancedFilterBuilderValidationSelectColumn: 'Deve selecionar uma coluna.',
    advancedFilterBuilderValidationSelectOption: 'Deve selecionar uma opção.',
    advancedFilterBuilderValidationEnterValue: 'Deve inserir um valor.',

    // Editor Validation Errors
    minDateValidation: 'A data deve ser após ${variable}',
    maxDateValidation: 'A data deve ser antes de ${variable}',
    maxLengthValidation: 'Deve ter ${variable} caracteres ou menos.',
    minValueValidation: 'Deve ser maior ou igual a ${variable}',
    maxValueValidation: 'Deve ser menor ou igual a ${variable}',
    invalidSelectionValidation: 'Seleção inválida.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Colunas',
    filters: 'Filtros',

    // columns tool panel
    pivotMode: 'Modo de Pivot',
    groups: 'Grupos de Linhas',
    rowGroupColumnsEmptyMessage: 'Arraste aqui para definir grupos de linhas',
    values: 'Valores',
    valueColumnsEmptyMessage: 'Arraste aqui para agregar',
    pivots: 'Rótulos de Coluna',
    pivotColumnsEmptyMessage: 'Arraste aqui para definir rótulos de coluna',

    // Header of the Default Group Column
    group: 'Grupo',

    // Row Drag
    rowDragRow: 'linha',
    rowDragRows: 'linhas',

    // Other
    loadingOoo: 'Carregando...',
    loadingError: 'ERR',
    noRowsToShow: 'Sem linhas para mostrar',
    enabled: 'Ativado',

    // Menu
    pinColumn: 'Fixar Coluna',
    pinLeft: 'Fixar à Esquerda',
    pinRight: 'Fixar à Direita',
    noPin: 'Não Fixar',
    valueAggregation: 'Agregação de Valores',
    noAggregation: 'Nenhum',
    autosizeThisColumn: 'Ajustar Tamanho desta Coluna',
    autosizeAllColumns: 'Ajustar Tamanho de Todas as Colunas',
    groupBy: 'Agrupar por',
    ungroupBy: 'Desagrupar por',
    ungroupAll: 'Desagrupar Tudo',
    addToValues: 'Adicionar ${variable} aos valores',
    removeFromValues: 'Remover ${variable} dos valores',
    addToLabels: 'Adicionar ${variable} aos rótulos',
    removeFromLabels: 'Remover ${variable} dos rótulos',
    resetColumns: 'Redefinir Colunas',
    expandAll: 'Expandir Todos os Grupos de Linhas',
    collapseAll: 'Fechar Todos os Grupos de Linhas',
    copy: 'Copiar',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Copiar Com Cabeçalhos',
    copyWithGroupHeaders: 'Copiar com Cabeçalhos de Grupo',
    cut: 'Cortar',
    paste: 'Colar',
    ctrlV: 'Ctrl+V',
    export: 'Exportar',
    csvExport: 'Exportação CSV',
    excelExport: 'Exportação Excel',
    columnFilter: 'Filtro de Coluna',
    columnChooser: 'Escolher Colunas',
    chooseColumns: 'Escolher Colunas',
    sortAscending: 'Ordenar Ascendente',
    sortDescending: 'Ordenar Descendente',
    sortUnSort: 'Limpar Ordenação',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Soma',
    first: 'Primeiro',
    last: 'Último',
    min: 'Min',
    max: 'Máx',
    none: 'Nenhum',
    count: 'Contagem',
    avg: 'Média',
    filteredRows: 'Filtrados',
    selectedRows: 'Selecionados',
    totalRows: 'Total de Linhas',
    totalAndFilteredRows: 'Linhas',
    more: 'Mais',
    to: 'para',
    of: 'de',
    page: 'Página',
    pageLastRowUnknown: '?',
    nextPage: 'Próxima Página',
    lastPage: 'Última Página',
    firstPage: 'Primeira Página',
    previousPage: 'Página Anterior',
    pageSizeSelectorLabel: 'Tamanho da Página:',
    footerTotal: 'Total',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Deslocar ${variable} para a vista',

    // Pivoting
    pivotColumnGroupTotals: 'Total',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Gráfico de Pivô e Modo Pivô',
    pivotChart: 'Gráfico de Pivô',
    chartRange: 'Intervalo do Gráfico',
    columnChart: 'Coluna',
    groupedColumn: 'Agrupadas',
    stackedColumn: 'Empilhadas',
    normalizedColumn: '100% Empilhadas',
    barChart: 'Barra',
    groupedBar: 'Agrupadas',
    stackedBar: 'Empilhadas',
    normalizedBar: '100% Empilhadas',
    pieChart: 'Pizza',
    pie: 'Pizza',
    donut: 'Rosca',
    lineChart: 'Linha',
    stackedLine: 'Empilhada',
    normalizedLine: '100% Empilhada',
    xyChart: 'X Y (Dispersão)',
    scatter: 'Dispersão',
    bubble: 'Bolhas',
    areaChart: 'Área',
    area: 'Área',
    stackedArea: 'Empilhadas',
    normalizedArea: '100% Empilhadas',
    histogramChart: 'Histograma',
    polarChart: 'Polar',
    radarLine: 'Linha de Radar',
    radarArea: 'Área de Radar',
    nightingale: 'Nightingale',
    radialColumn: 'Coluna Radial',
    radialBar: 'Barra Radial',
    statisticalChart: 'Estatístico',
    boxPlot: 'Gráfico de Caixa',
    rangeBar: 'Barra de Intervalo',
    rangeArea: 'Área de Intervalo',
    hierarchicalChart: 'Hierárquico',
    treemap: 'Mapa de Árvore',
    sunburst: 'Explosão Solar',
    specializedChart: 'Especializado',
    waterfall: 'Cascata',
    heatmap: 'Mapa de Calor',
    combinationChart: 'Combinação',
    columnLineCombo: 'Coluna e Linha',
    AreaColumnCombo: 'Área e Coluna',

    // Charts
    pivotChartTitle: 'Gráfico Pivô',
    rangeChartTitle: 'Gráfico de Intervalo',
    settings: 'Gráfico',
    data: 'Configuração',
    format: 'Personalizar',
    categories: 'Categorias',
    defaultCategory: '(Nenhum)',
    series: 'Séries',
    switchCategorySeries: 'Alternar Categoria / Série',
    categoryValues: 'Valores de Categoria',
    seriesLabels: 'Rótulos de Série',
    aggregate: 'Agrupar',
    xyValues: 'Valores X Y',
    paired: 'Modo Pareado',
    axis: 'Eixo',
    xAxis: 'Eixo Horizontal',
    yAxis: 'Eixo Vertical',
    polarAxis: 'Eixo Polar',
    radiusAxis: 'Eixo de Raio',
    navigator: 'Navegador',
    zoom: 'Zoom',
    animation: 'Animação',
    crosshair: 'Mira',
    color: 'Cor',
    thickness: 'Espessura',
    preferredLength: 'Comprimento Preferido',
    xType: 'Tipo X',
    axisType: 'Tipo de Eixo',
    automatic: 'Automático',
    category: 'Categoria',
    number: 'Número',
    time: 'Tempo',
    timeFormat: 'Formato de Tempo',
    autoRotate: 'Rotação Automática',
    labelRotation: 'Rotação',
    circle: 'Círculo',
    polygon: 'Polígono',
    square: 'Quadrado',
    cross: 'Cruz',
    diamond: 'Diamante',
    plus: 'Mais',
    triangle: 'Triângulo',
    heart: 'Coração',
    orientation: 'Orientação',
    fixed: 'Fixo',
    parallel: 'Paralelo',
    perpendicular: 'Perpendicular',
    radiusAxisPosition: 'Posição',
    ticks: 'Marcas',
    gridLines: 'Linhas de Grade',
    width: 'Largura',
    height: 'Altura',
    length: 'Comprimento',
    padding: 'Preenchimento',
    spacing: 'Espaçamento',
    chartStyle: 'Estilo do Gráfico',
    title: 'Título',
    chartTitles: 'Títulos',
    chartTitle: 'Título do Gráfico',
    chartSubtitle: 'Subtítulo',
    horizontalAxisTitle: 'Título do Eixo Horizontal',
    verticalAxisTitle: 'Título do Eixo Vertical',
    polarAxisTitle: 'Título do Eixo Polar',
    titlePlaceholder: 'Título do Gráfico',
    background: 'Fundo',
    font: 'Fonte',
    weight: 'Espessura',
    top: 'Topo',
    right: 'Direita',
    bottom: 'Inferior',
    left: 'Esquerda',
    labels: 'Rótulos',
    calloutLabels: 'Rótulos de Destaque',
    sectorLabels: 'Rótulos de Setor',
    positionRatio: 'Relação de Posição',
    size: 'Tamanho',
    shape: 'Forma',
    minSize: 'Tamanho Mínimo',
    maxSize: 'Tamanho Máximo',
    legend: 'Legenda',
    position: 'Posição',
    markerSize: 'Tamanho do Marcador',
    markerStroke: 'Contorno do Marcador',
    markerPadding: 'Preenchimento do Marcador',
    itemSpacing: 'Espaçamento de Itens',
    itemPaddingX: 'Preenchimento do Item X',
    itemPaddingY: 'Preenchimento do Item Y',
    layoutHorizontalSpacing: 'Espaçamento Horizontal',
    layoutVerticalSpacing: 'Espaçamento Vertical',
    strokeWidth: 'Espessura da Linha',
    offset: 'Deslocamento',
    offsets: 'Deslocamentos',
    tooltips: 'Dicas de Ferramenta',
    callout: 'Destaque',
    markers: 'Marcadores',
    shadow: 'Sombra',
    blur: 'Desfoque',
    xOffset: 'Deslocamento X',
    yOffset: 'Deslocamento Y',
    lineWidth: 'Largura da Linha',
    lineDash: 'Tracejado da Linha',
    lineDashOffset: 'Deslocamento do Tracejado',
    scrollingZoom: 'Rolagem',
    scrollingStep: 'Passo da Rolagem',
    selectingZoom: 'Selecionando',
    durationMillis: 'Duração (ms)',
    crosshairLabel: 'Rótulo',
    crosshairSnap: 'Ajustar ao Nó',
    normal: 'Normal',
    bold: 'Negrito',
    italic: 'Itálico',
    boldItalic: 'Negrito Itálico',
    predefined: 'Predefinido',
    fillOpacity: 'Opacidade do Preenchimento',
    strokeColor: 'Cor da Linha',
    strokeOpacity: 'Opacidade da Linha',
    miniChart: 'Mini Gráfico',
    histogramBinCount: 'Contagem de Blocos',
    connectorLine: 'Linha Conectora',
    seriesItems: 'Itens da Série',
    seriesItemType: 'Tipo de Item',
    seriesItemPositive: 'Positivo',
    seriesItemNegative: 'Negativo',
    seriesItemLabels: 'Rótulos de Itens',
    columnGroup: 'Coluna',
    barGroup: 'Barra',
    pieGroup: 'Pizza',
    lineGroup: 'Linha',
    scatterGroup: 'X Y (Dispersão)',
    areaGroup: 'Área',
    polarGroup: 'Polar',
    statisticalGroup: 'Estatístico',
    hierarchicalGroup: 'Hierárquico',
    specializedGroup: 'Especializado',
    combinationGroup: 'Combinação',
    groupedColumnTooltip: 'Agrupado',
    stackedColumnTooltip: 'Empilhado',
    normalizedColumnTooltip: '100% Empilhado',
    groupedBarTooltip: 'Agrupado',
    stackedBarTooltip: 'Empilhado',
    normalizedBarTooltip: '100% Empilhado',
    pieTooltip: 'Pizza',
    donutTooltip: 'Rosquinha',
    lineTooltip: 'Linha',
    stackedLineTooltip: 'Empilhado',
    normalizedLineTooltip: '100% Empilhado',
    groupedAreaTooltip: 'Área',
    stackedAreaTooltip: 'Empilhada',
    normalizedAreaTooltip: '100% Empilhada',
    scatterTooltip: 'Dispersão',
    bubbleTooltip: 'Bolha',
    histogramTooltip: 'Histograma',
    radialColumnTooltip: 'Coluna Radial',
    radialBarTooltip: 'Barra Radial',
    radarLineTooltip: 'Linha de Radar',
    radarAreaTooltip: 'Área de Radar',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Barra de Intervalo',
    rangeAreaTooltip: 'Área de Intervalo',
    boxPlotTooltip: 'Diagrama de Caixa',
    treemapTooltip: 'Mapa de Árvore',
    sunburstTooltip: 'Explosão Solar',
    waterfallTooltip: 'Cascata',
    heatmapTooltip: 'Mapa de Calor',
    columnLineComboTooltip: 'Coluna & Linha',
    areaColumnComboTooltip: 'Área & Coluna',
    customComboTooltip: 'Combinação Personalizada',
    innerRadius: 'Raio Interno',
    startAngle: 'Ângulo Inicial',
    endAngle: 'Ângulo Final',
    reverseDirection: 'Reverter Direção',
    groupPadding: 'Preenchimento do Grupo',
    seriesPadding: 'Preenchimento da Série',
    tile: 'Ladrilho',
    whisker: 'Bigode',
    cap: 'Tampa',
    capLengthRatio: 'Relação de Comprimento',
    labelPlacement: 'Posicionamento',
    inside: 'Dentro',
    outside: 'Fora',
    noDataToChart: 'Sem dados disponíveis para gráfico.',
    pivotChartRequiresPivotMode: 'Gráfico Pivô requer o Modo Pivô habilitado.',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Vinculado à Grade',
    chartUnlinkToolbarTooltip: 'Desvinculado da Grade',
    chartDownloadToolbarTooltip: 'Baixar Gráfico',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Editar Gráfico',
    chartAdvancedSettings: 'Configurações Avançadas',
    chartLink: 'Vincular à Grade',
    chartUnlink: 'Desvincular da Grade',
    chartDownload: 'Baixar Gráfico',
    histogramFrequency: 'Frequência',
    seriesChartType: 'Tipo de Gráfico da Série',
    seriesType: 'Tipo de Série',
    secondaryAxis: 'Eixo Secundário',
    seriesAdd: 'Adicionar uma série',
    categoryAdd: 'Adicionar uma categoria',
    bar: 'Barra',
    column: 'Coluna',
    histogram: 'Histograma',
    advancedSettings: 'Configurações Avançadas',
    direction: 'Direção',
    horizontal: 'Horizontal',
    vertical: 'Vertical',
    seriesGroupType: 'Tipo de Grupo',
    groupedSeriesGroupType: 'Agrupado',
    stackedSeriesGroupType: 'Empilhado',
    normalizedSeriesGroupType: '100% Empilhado',
    legendEnabled: 'Habilitado',
    invalidColor: 'O valor da cor é inválido',
    groupedColumnFull: 'Coluna Agrupada',
    stackedColumnFull: 'Coluna Empilhada',
    normalizedColumnFull: 'Coluna 100% Empilhada',
    groupedBarFull: 'Barra Agrupada',
    stackedBarFull: 'Barra Empilhada',
    normalizedBarFull: 'Barra 100% Empilhada',
    stackedAreaFull: 'Área Empilhada',
    normalizedAreaFull: 'Área 100% Empilhada',
    customCombo: 'Combinação Personalizada',
    funnel: 'Funil',
    coneFunnel: 'Funil de Cone',
    pyramid: 'Pirâmide',
    funnelGroup: 'Funil',
    funnelTooltip: 'Funil',
    coneFunnelTooltip: 'Funil de Cone',
    pyramidTooltip: 'Pirâmide',
    dropOff: 'Desistência',
    stageLabels: 'Rótulos de Etapa',
    reverse: 'Reverter',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Nível ${variable}. Pressione ENTER para editar.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Nível ${variable}. ${variable} Pressione ENTER para editar.',
    ariaAdvancedFilterBuilderList: 'Lista de Construtores de Filtro Avançado',
    ariaAdvancedFilterBuilderFilterItem: 'Condição do Filtro',
    ariaAdvancedFilterBuilderGroupItem: 'Grupo de Filtro',
    ariaAdvancedFilterBuilderColumn: 'Coluna',
    ariaAdvancedFilterBuilderOption: 'Opção',
    ariaAdvancedFilterBuilderValueP: 'Valor',
    ariaAdvancedFilterBuilderJoinOperator: 'Operador de Junção',
    ariaAdvancedFilterInput: 'Entrada de Filtro Avançado',
    ariaChecked: 'marcado',
    ariaColumn: 'Coluna',
    ariaColumnGroup: 'Grupo de Colunas',
    ariaColumnFiltered: 'Coluna Filtrada',
    ariaColumnSelectAll: 'Alternar visibilidade de todas as colunas',
    ariaDateFilterInput: 'Entrada de Filtro de Data',
    ariaDefaultListName: 'Lista',
    ariaFilterColumnsInput: 'Entrada de Filtro de Colunas',
    ariaFilterFromValue: 'Filtrar a partir do valor',
    ariaFilterInput: 'Entrada de Filtro',
    ariaFilterList: 'Lista de Filtros',
    ariaFilterToValue: 'Filtrar até o valor',
    ariaFilterValue: 'Valor do Filtro',
    ariaFilterMenuOpen: 'Abrir Menu de Filtro',
    ariaFilteringOperator: 'Operador de Filtragem',
    ariaHidden: 'oculto',
    ariaIndeterminate: 'indeterminado',
    ariaInputEditor: 'Editor de Entrada',
    ariaMenuColumn: 'Pressione ALT para baixo para abrir o menu da coluna',
    ariaFilterColumn: 'Pressione CTRL ENTER para abrir o filtro',
    ariaRowDeselect: 'Pressione ESPAÇO para desmarcar esta linha',
    ariaHeaderSelection: 'Coluna com seleção de cabeçalho',
    ariaSelectAllCells: 'Pressione Espaço para selecionar todas as células',
    ariaRowSelectAll: 'Pressione Espaço para alternar a seleção de todas as linhas',
    ariaRowToggleSelection: 'Pressione Espaço para alternar a seleção da linha',
    ariaRowSelect: 'Pressione ESPAÇO para selecionar esta linha',
    ariaRowSelectionDisabled: 'A seleção de linhas está desativada para esta linha',
    ariaSearch: 'Buscar',
    ariaSortableColumn: 'Pressione ENTER para ordenar',
    ariaToggleVisibility: 'Pressione ESPAÇO para alternar visibilidade',
    ariaToggleCellValue: 'Pressione ESPAÇO para alternar o valor da célula',
    ariaUnchecked: 'desmarcado',
    ariaVisible: 'visível',
    ariaSearchFilterValues: 'Buscar valores do filtro',
    ariaPageSizeSelectorLabel: 'Tamanho da Página',
    ariaChartMenuClose: 'Fechar Menu de Edição de Gráfico',
    ariaChartSelected: 'Selecionado',
    ariaSkeletonCellLoadingFailed: 'Falha ao carregar a linha',
    ariaSkeletonCellLoading: 'Os dados da linha estão carregando',
    ariaDeferSkeletonCellLoading: 'Célula está carregando',

    // ARIA for Batch Edit
    ariaPendingChange: 'Alteração pendente',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Grupos de Linhas',
    ariaValuesDropZonePanelLabel: 'Valores',
    ariaPivotDropZonePanelLabel: 'Rótulos de Colunas',
    ariaDropZoneColumnComponentDescription: 'Pressione DELETE para remover',
    ariaDropZoneColumnValueItemDescription: 'Pressione ENTER para mudar o tipo de agregação',
    ariaDropZoneColumnGroupItemDescription: 'Pressione ENTER para classificar',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' de ',
    ariaDropZoneColumnComponentSortAscending: 'ascendente',
    ariaDropZoneColumnComponentSortDescending: 'descendente',
    ariaLabelDialog: 'Diálogo',
    ariaLabelColumnMenu: 'Menu da Coluna',
    ariaLabelColumnFilter: 'Filtro da Coluna',
    ariaLabelSelectField: 'Selecionar Campo',

    // Cell Editor
    ariaLabelCellEditor: 'Editor de Célula',
    ariaValidationErrorPrefix: 'Validação do Editor de Célula',
    ariaLabelLoadingContextMenu: 'Carregando Menu de Contexto',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Campo de Seleção Rico',
    ariaLabelRichSelectToggleSelection: 'Pressione ESPAÇO para alternar a seleção',
    ariaLabelRichSelectDeselectAllItems: 'Pressione DELETE para desmarcar todos os itens',
    ariaLabelRichSelectDeleteSelection: 'Pressione DELETE para desmarcar item',
    ariaLabelTooltip: 'Dica',
    ariaLabelContextMenu: 'Menu de Contexto',
    ariaLabelSubMenu: 'Submenu',
    ariaLabelAggregationFunction: 'Função de Agregação',
    ariaLabelAdvancedFilterAutocomplete: 'Autocompletar Filtro Avançado',
    ariaLabelAdvancedFilterBuilderAddField: 'Construtor de Filtro Avançado Adicionar Campo',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Construtor de Filtro Avançado Selecionar Campo da Coluna',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Construtor de Filtro Avançado Selecionar Campo da Opção',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Construtor de Filtro Avançado Selecionar Operador de Junção',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Lista de Colunas',
    ariaFilterPanelList: 'Lista de Filtros',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Adicionar Campo de Filtro',
    ariaLabelFilterCardDelete: 'Excluir Filtro',
    ariaLabelFilterCardHasEdits: 'Possui Edições',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Verdadeiro',
    false: 'Falso',
    invalidDate: 'Data Inválida',
    invalidNumber: 'Número Inválido',
    january: 'Janeiro',
    february: 'Fevereiro',
    march: 'Março',
    april: 'Abril',
    may: 'Maio',
    june: 'Junho',
    july: 'Julho',
    august: 'Agosto',
    september: 'Setembro',
    october: 'Outubro',
    november: 'Novembro',
    december: 'Dezembro',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.AA',
    timeFormatDotsMDDYY: 'M.DD.AA',
    timeFormatDashesYYYYMMDD: 'AAAA-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM AAAA',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS AM/PM',
};
