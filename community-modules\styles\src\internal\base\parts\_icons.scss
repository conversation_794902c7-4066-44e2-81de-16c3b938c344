@use 'sass:string';
@use 'sass:map';
@use 'sass:meta';

@use 'ag';

@mixin output() {
    .ag-icon {
        @include ag.icon();

        width: var(--ag-icon-size);
        height: var(--ag-icon-size);
        position: relative;

        // ::before used for icon font glyph, content is overridden by
        // .ag-icon-iconname::before styles below
        &::before {
            content: '';
            font-family: inherit;
        }

        // ::after used for icon images
        &::after {
            background: transparent var(--ag-icon-image, none) center/contain no-repeat;
            display: var(--ag-icon-image-display);
            opacity: var(--ag-icon-image-opacity, 0.9);
            position: absolute;
            inset: 0;
            content: '';
        }
    }

    @each $icon-name, $font-code in ag.$icon-font-codes {
        .ag-icon-#{$icon-name} {
            font-family: var(--ag-icon-font-family-#{$icon-name}, var(--ag-icon-font-family));
            font-weight: var(--ag-icon-font-weight-#{$icon-name}, var(--ag-icon-font-weight));
            color: var(--ag-icon-font-color-#{$icon-name}, var(--ag-icon-font-color));
        }
        .ag-icon-#{$icon-name}::before {
            content: ag.icon-content($icon-name);
            display: var(--ag-icon-font-display-#{$icon-name}, var(--ag-icon-font-display));
        }
        .ag-icon-#{$icon-name}::after {
            background-image: var(--ag-icon-image-#{$icon-name}, var(--ag-icon-image));
            display: var(--ag-icon-image-display-#{$icon-name}, var(--ag-icon-image-display));
            opacity: var(--ag-icon-image-opacity-#{$icon-name}, var(--ag-icon-image-opacity, 0.9));
        }
    }

    .ag-icon-row-drag::before {
        content: var(--ag-icon-font-code-grip);
    }

    .ag-left-arrow::before {
        content: var(--ag-icon-font-code-left);
    }

    .ag-right-arrow::before {
        content: var(--ag-icon-font-code-right);
    }
}
