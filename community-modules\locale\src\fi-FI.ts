/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_FI = {
    // Set Filter
    selectAll: '(<PERSON><PERSON><PERSON> kaikki)',
    selectAllSearchResults: '(Valitse kaikki hakutulo<PERSON>et)',
    addCurrentSelectionToFilter: 'Lisää nykyinen valinta suodattimeen',
    searchOoo: 'Haku...',
    blanks: '(Tyhjät)',
    noMatches: 'Ei osumia',

    // Number Filter & Text Filter
    filterOoo: 'Suodata...',
    equals: 'Yhtä kuin',
    notEqual: '<PERSON>ri kuin',
    blank: 'Tyhjä',
    notBlank: 'Ei tyhjä',
    empty: 'Valitse yksi',

    // Number Filter
    lessThan: 'Vähemmän kuin',
    greaterThan: 'Enemmän kuin',
    lessThanOrEqual: 'Vähemmän tai yhtä paljon kuin',
    greaterThanOrEqual: 'Enemmän tai yhtä paljon kuin',
    inRange: 'Välillä',
    inRangeStart: 'Alkaen',
    inRangeEnd: 'Loppuun',

    // Text Filter
    contains: 'Sisältää',
    notContains: 'Ei sisällä',
    startsWith: 'Alkaa',
    endsWith: 'Päättyy',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Ennen',
    after: 'Jälkeen',

    // Filter Conditions
    andCondition: 'JA',
    orCondition: 'TAI',

    // Filter Buttons
    applyFilter: 'Käytä',
    resetFilter: 'Nollaa',
    clearFilter: 'Tyhjennä',
    cancelFilter: 'Peruuta',

    // Filter Titles
    textFilter: 'Tekstisuodatin',
    numberFilter: 'Numerosuodatin',
    dateFilter: 'Päivämääräsuodatin',
    setFilter: 'Joukko suodatin',

    // Group Column Filter
    groupFilterSelect: 'Valitse kenttä:',

    // New Filter Tool Panel
    filterSummaryInactive: 'on (kaikki)',
    filterSummaryContains: 'sisältää',
    filterSummaryNotContains: 'ei sisällä',
    filterSummaryTextEquals: 'on yhtä suuri kuin',
    filterSummaryTextNotEqual: 'ei ole yhtä suuri kuin',
    filterSummaryStartsWith: 'alkaa',
    filterSummaryEndsWith: 'päättyy',
    filterSummaryBlank: 'on tyhjä',
    filterSummaryNotBlank: 'ei ole tyhjä',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'välillä',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'on (kaikki)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'on (${variable})',
    filterSummaryListLong: 'on (${variable}) ja ${variable} enemmän',
    addFilterCard: 'Lisää suodatin',
    agTextColumnFilterDisplayName: 'Yksinkertainen suodatin',
    agNumberColumnFilterDisplayName: 'Yksinkertainen suodatin',
    agDateColumnFilterDisplayName: 'Yksinkertainen suodatin',
    agSetColumnFilterDisplayName: 'Valintasuodatin',
    agMultiColumnFilterDisplayName: 'Yhdistelmä suodatin',
    addFilterPlaceholder: 'Etsi sarakkeita...',

    // Advanced Filter
    advancedFilterContains: 'sisältää',
    advancedFilterNotContains: 'ei sisällä',
    advancedFilterTextEquals: 'on yhtä kuin',
    advancedFilterTextNotEqual: 'ei ole yhtä kuin',
    advancedFilterStartsWith: 'alkaa',
    advancedFilterEndsWith: 'päättyy',
    advancedFilterBlank: 'on tyhjä',
    advancedFilterNotBlank: 'ei ole tyhjä',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'on tosi',
    advancedFilterFalse: 'on epätosi',
    advancedFilterAnd: 'JA',
    advancedFilterOr: 'TAI',
    advancedFilterApply: 'Käytä',
    advancedFilterBuilder: 'Rakentaja',
    advancedFilterValidationMissingColumn: 'Sarake puuttuu',
    advancedFilterValidationMissingOption: 'Valinta puuttuu',
    advancedFilterValidationMissingValue: 'Arvo puuttuu',
    advancedFilterValidationInvalidColumn: 'Sarake ei löydy',
    advancedFilterValidationInvalidOption: 'Valintaa ei löydy',
    advancedFilterValidationMissingQuote: 'Arvosta puuttuu pääte lainausmerkki',
    advancedFilterValidationNotANumber: 'Arvo ei ole numero',
    advancedFilterValidationInvalidDate: 'Arvo ei ole kelvollinen päivämäärä',
    advancedFilterValidationMissingCondition: 'Ehto puuttuu',
    advancedFilterValidationJoinOperatorMismatch: 'Yhdisteoperaattoreiden täytyy olla samat ehdon sisällä',
    advancedFilterValidationInvalidJoinOperator: 'Yhdisteoperaattori ei löydy',
    advancedFilterValidationMissingEndBracket: 'Päättävä sulje puuttuu',
    advancedFilterValidationExtraEndBracket: 'Liian monta päättävää suljetta',
    advancedFilterValidationMessage: 'Lausekkeessa on virhe. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Lausekkeessa on virhe. ${variable} lausekkeen lopussa.',
    advancedFilterBuilderTitle: 'Tarkennettu suodatin',
    advancedFilterBuilderApply: 'Käytä',
    advancedFilterBuilderCancel: 'Peruuta',
    advancedFilterBuilderAddButtonTooltip: 'Lisää suodatin tai ryhmä',
    advancedFilterBuilderRemoveButtonTooltip: 'Poista',
    advancedFilterBuilderMoveUpButtonTooltip: 'Siirrä ylös',
    advancedFilterBuilderMoveDownButtonTooltip: 'Siirrä alas',
    advancedFilterBuilderAddJoin: 'Lisää ryhmä',
    advancedFilterBuilderAddCondition: 'Lisää suodatin',
    advancedFilterBuilderSelectColumn: 'Valitse sarake',
    advancedFilterBuilderSelectOption: 'Valitse vaihtoehto',
    advancedFilterBuilderEnterValue: 'Syötä arvo...',
    advancedFilterBuilderValidationAlreadyApplied: 'Nykyinen suodatin on jo käytössä.',
    advancedFilterBuilderValidationIncomplete: 'Kaikki ehdot eivät ole valmiita.',
    advancedFilterBuilderValidationSelectColumn: 'Täytyy valita sarake.',
    advancedFilterBuilderValidationSelectOption: 'Täytyy valita vaihtoehto.',
    advancedFilterBuilderValidationEnterValue: 'Täytyy syöttää arvo.',

    // Editor Validation Errors
    minDateValidation: 'Päivämäärän on oltava jälkeen ${variable}',
    maxDateValidation: 'Päivämäärän on oltava ennen ${variable}',
    maxLengthValidation: 'Saa olla enintään ${variable} merkkiä.',
    minValueValidation: 'Täytyy olla suurempi tai yhtä suuri kuin ${variable}',
    maxValueValidation: 'Täytyy olla pienempi tai yhtä suuri kuin ${variable}',
    invalidSelectionValidation: 'Virheellinen valinta.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Sarakkeet',
    filters: 'Suodattimet',

    // columns tool panel
    pivotMode: 'Pivot-tila',
    groups: 'Riviryhmät',
    rowGroupColumnsEmptyMessage: 'Vedä tänne asettaaksesi riviryhmät',
    values: 'Arvot',
    valueColumnsEmptyMessage: 'Vedä tänne tehdäksesi aggregaatin',
    pivots: 'Sarakemerkinnät',
    pivotColumnsEmptyMessage: 'Vedä tänne asettaaksesi sarakemerkinnät',

    // Header of the Default Group Column
    group: 'Ryhmä',

    // Row Drag
    rowDragRow: 'rivi',
    rowDragRows: 'rivit',

    // Other
    loadingOoo: 'Ladataan...',
    loadingError: 'VIRHE',
    noRowsToShow: 'Ei näytettäviä rivejä',
    enabled: 'Käytössä',

    // Menu
    pinColumn: 'Kiinnitä sarake',
    pinLeft: 'Kiinnitä vasemmalle',
    pinRight: 'Kiinnitä oikealle',
    noPin: 'Ei kiinnitystä',
    valueAggregation: 'Arvon yhteenveto',
    noAggregation: 'Ei yhteenvetoa',
    autosizeThisColumn: 'Automaattinen koko tälle sarakkeelle',
    autosizeAllColumns: 'Automaattinen koko kaikille sarakkeille',
    groupBy: 'Ryhmittele',
    ungroupBy: 'Poista ryhmittely',
    ungroupAll: 'Poista kaikkien ryhmien ryhmittely',
    addToValues: 'Lisää ${variable} arvoihin',
    removeFromValues: 'Poista ${variable} arvoista',
    addToLabels: 'Lisää ${variable} etiketteihin',
    removeFromLabels: 'Poista ${variable} etiketeistä',
    resetColumns: 'Palauta sarakkeet',
    expandAll: 'Laajenna kaikki riviryhmät',
    collapseAll: 'Sulje kaikki riviryhmät',
    copy: 'Kopioi',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopioi otsikoiden kanssa',
    copyWithGroupHeaders: 'Kopioi ryhmäotsikoiden kanssa',
    cut: 'Leikkaa',
    paste: 'Liitä',
    ctrlV: 'Ctrl+V',
    export: 'Vie',
    csvExport: 'Vie CSV-muodossa',
    excelExport: 'Vie Excel-muodossa',
    columnFilter: 'Sarakkeen suodatus',
    columnChooser: 'Valitse sarakkeet',
    chooseColumns: 'Valitse sarakkeet',
    sortAscending: 'Lajittele nousevasti',
    sortDescending: 'Lajittele laskevasti',
    sortUnSort: 'Tyhjennä lajittelu',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Summa',
    first: 'Ensimmäinen',
    last: 'Viimeinen',
    min: 'Minimi',
    max: 'Maksimi',
    none: 'Ei mitään',
    count: 'Lukumäärä',
    avg: 'Keskiarvo',
    filteredRows: 'Suodatettu',
    selectedRows: 'Valitut',
    totalRows: 'Rivit yhteensä',
    totalAndFilteredRows: 'Rivit',
    more: 'Lisää',
    to: '-',
    of: ' / ',
    page: 'Sivu',
    pageLastRowUnknown: '?',
    nextPage: 'Seuraava sivu',
    lastPage: 'Viimeinen sivu',
    firstPage: 'Ensimmäinen sivu',
    previousPage: 'Edellinen sivu',
    pageSizeSelectorLabel: 'Sivun koko:',
    footerTotal: 'Yhteensä',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Vieritä ${variable} näkyviin',

    // Pivoting
    pivotColumnGroupTotals: 'Yhteensä',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivot Kaavio & Pivot Tila',
    pivotChart: 'Pivot Kaavio',
    chartRange: 'Kaavion Alue',
    columnChart: 'Pylväs',
    groupedColumn: 'Ryhmitelty',
    stackedColumn: 'Pinottu',
    normalizedColumn: '100% Pinottu',
    barChart: 'Palkki',
    groupedBar: 'Ryhmitelty',
    stackedBar: 'Pinottu',
    normalizedBar: '100% Pinottu',
    pieChart: 'Piirakka',
    pie: 'Piirakka',
    donut: 'Donitsi',
    lineChart: 'Viiva',
    stackedLine: 'Pinottu',
    normalizedLine: '100% Pinottu',
    xyChart: 'X Y (Hajontakaavio)',
    scatter: 'Hajonta',
    bubble: 'Kupla',
    areaChart: 'Alue',
    area: 'Alue',
    stackedArea: 'Pinottu',
    normalizedArea: '100% Pinottu',
    histogramChart: 'Histogrammi',
    polarChart: 'Polaarinen',
    radarLine: 'Tutkaviiva',
    radarArea: 'Tutka-alue',
    nightingale: 'Nightingale',
    radialColumn: 'Sädepylväs',
    radialBar: 'Sädepalkki',
    statisticalChart: 'Tilastollinen',
    boxPlot: 'Laatikkokaavio',
    rangeBar: 'Aluepalkki',
    rangeArea: 'Aluekaavio',
    hierarchicalChart: 'Hierarkkinen',
    treemap: 'Puukaavio',
    sunburst: 'Auringonpurkaus',
    specializedChart: 'Erikoistunut',
    waterfall: 'Vesiputous',
    heatmap: 'Lämpökartta',
    combinationChart: 'Yhdistelmä',
    columnLineCombo: 'Pylväs & Viiva',
    AreaColumnCombo: 'Alue & Pylväs',

    // Charts
    pivotChartTitle: 'Pivot-kaavio',
    rangeChartTitle: 'Aluekaavio',
    settings: 'Kaavio',
    data: 'Määritys',
    format: 'Mukauta',
    categories: 'Luokat',
    defaultCategory: '(Ei mitään)',
    series: 'Sarjat',
    switchCategorySeries: 'Vaihda luokkaa / sarjaa',
    categoryValues: 'Luokka-arvot',
    seriesLabels: 'Sarjojen tunnisteet',
    aggregate: 'Yhdistä',
    xyValues: 'X Y -arvot',
    paired: 'Paritettu tila',
    axis: 'Akseli',
    xAxis: 'Vaaka-akseli',
    yAxis: 'Pysty-akseli',
    polarAxis: 'Polaarinen akseli',
    radiusAxis: 'Sädeakseli',
    navigator: 'Navigator',
    zoom: 'Zoomaa',
    animation: 'Animaatio',
    crosshair: 'Ristikko',
    color: 'Väri',
    thickness: 'Paksuus',
    preferredLength: 'Suositeltu pituus',
    xType: 'X-tyyppi',
    axisType: 'Akseleiden tyyppi',
    automatic: 'Automaattinen',
    category: 'Luokka',
    number: 'Numero',
    time: 'Aika',
    timeFormat: 'Aikaformaatti',
    autoRotate: 'Automaattinen kierto',
    labelRotation: 'Kierrä',
    circle: 'Ympyrä',
    polygon: 'Monikulmio',
    square: 'Neliö',
    cross: 'Risti',
    diamond: 'Timantti',
    plus: 'Plus',
    triangle: 'Kolmio',
    heart: 'Sydän',
    orientation: 'Orientaatio',
    fixed: 'Kiinnitetty',
    parallel: 'Rinnakkainen',
    perpendicular: 'Kohtisuora',
    radiusAxisPosition: 'Sijainti',
    ticks: 'Merkit',
    gridLines: 'Ruudukko viivat',
    width: 'Leveys',
    height: 'Korkeus',
    length: 'Pituus',
    padding: 'Täyttö',
    spacing: 'Väli',
    chartStyle: 'Kaavion tyyli',
    title: 'Otsikko',
    chartTitles: 'Kaavio-otsikot',
    chartTitle: 'Kaavio-otsikko',
    chartSubtitle: 'Alaotsikko',
    horizontalAxisTitle: 'Vaaka-akselin otsikko',
    verticalAxisTitle: 'Pysty-akselin otsikko',
    polarAxisTitle: 'Polaarisen akselin otsikko',
    titlePlaceholder: 'Kaavion otsikko',
    background: 'Tausta',
    font: 'Fontti',
    weight: 'Paksuus',
    top: 'Ylä',
    right: 'Oikea',
    bottom: 'Ala',
    left: 'Vasen',
    labels: 'Tunnisteet',
    calloutLabels: 'Huomautustunnisteet',
    sectorLabels: 'Sektoritunnisteet',
    positionRatio: 'Suhteen sijainti',
    size: 'Koko',
    shape: 'Muoto',
    minSize: 'Minimikoko',
    maxSize: 'Maksimikoko',
    legend: 'Selite',
    position: 'Sijainti',
    markerSize: 'Merkin koko',
    markerStroke: 'Merkki veto',
    markerPadding: 'Merkin täyttö',
    itemSpacing: 'Kohteen väli',
    itemPaddingX: 'Kohteen täyttö X',
    itemPaddingY: 'Kohteen täyttö Y',
    layoutHorizontalSpacing: 'Vaakasuuntainen väli',
    layoutVerticalSpacing: 'Pystysuuntainen väli',
    strokeWidth: 'Viivan leveys',
    offset: 'Siirtymä',
    offsets: 'Offsetit',
    tooltips: 'Työkaluvihjeet',
    callout: 'Huomautus',
    markers: 'Merkit',
    shadow: 'Varjo',
    blur: 'Sumennus',
    xOffset: 'X-siirtymä',
    yOffset: 'Y-siirtymä',
    lineWidth: 'Viivan leveys',
    lineDash: 'Viivan viivaus',
    lineDashOffset: 'Viivaus siirtymä',
    scrollingZoom: 'Vieritys',
    scrollingStep: 'Vieritys askel',
    selectingZoom: 'Valinta',
    durationMillis: 'Kesto (ms)',
    crosshairLabel: 'Tunniste',
    crosshairSnap: 'Napsauta solmuun',
    normal: 'Normaali',
    bold: 'Lihavoitu',
    italic: 'Kursiivi',
    boldItalic: 'Lihavoitu kursiivi',
    predefined: 'Ennalta määritelty',
    fillOpacity: 'Täyttöpeittävyys',
    strokeColor: 'Viivaväri',
    strokeOpacity: 'Viivapeittävyys',
    miniChart: 'Mini-kaavio',
    histogramBinCount: 'Bin count',
    connectorLine: 'Liitäntäviiva',
    seriesItems: 'Sarjan kohteet',
    seriesItemType: 'Kohteen tyyppi',
    seriesItemPositive: 'Positiivinen',
    seriesItemNegative: 'Negatiivinen',
    seriesItemLabels: 'Kohteen tunnisteet',
    columnGroup: 'Pylväs',
    barGroup: 'Palkki',
    pieGroup: 'Piirakka',
    lineGroup: 'Viiva',
    scatterGroup: 'X Y (Hajanaisuus)',
    areaGroup: 'Alue',
    polarGroup: 'Polaarinen',
    statisticalGroup: 'Tilastollinen',
    hierarchicalGroup: 'Hierarkkinen',
    specializedGroup: 'Erikoistunut',
    combinationGroup: 'Yhdistelmä',
    groupedColumnTooltip: 'Ryhmitetty',
    stackedColumnTooltip: 'Pinottu',
    normalizedColumnTooltip: '100 % pinottu',
    groupedBarTooltip: 'Ryhmitetty',
    stackedBarTooltip: 'Pinottu',
    normalizedBarTooltip: '100 % pinottu',
    pieTooltip: 'Piirakka',
    donutTooltip: 'Donitsi',
    lineTooltip: 'Viiva',
    stackedLineTooltip: 'Pino',
    normalizedLineTooltip: '100% Pinottu',
    groupedAreaTooltip: 'Alue',
    stackedAreaTooltip: 'Pinottu',
    normalizedAreaTooltip: '100 % pinottu',
    scatterTooltip: 'Hajanaisuus',
    bubbleTooltip: 'Kupla',
    histogramTooltip: 'Histogrammi',
    radialColumnTooltip: 'Säteittäinen pylväs',
    radialBarTooltip: 'Säteittäinen palkki',
    radarLineTooltip: 'Tutka viiva',
    radarAreaTooltip: 'Tutka-alue',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Alue palkki',
    rangeAreaTooltip: 'Alue',
    boxPlotTooltip: 'Box plot',
    treemapTooltip: 'Puukartta',
    sunburstTooltip: 'Sunburst',
    waterfallTooltip: 'Vesiputous',
    heatmapTooltip: 'Lämpökartta',
    columnLineComboTooltip: 'Pylväs & viiva',
    areaColumnComboTooltip: 'Alue & pylväs',
    customComboTooltip: 'Mukautettu yhdistelmä',
    innerRadius: 'Sisäinen säde',
    startAngle: 'Alkukulma',
    endAngle: 'Päätekulma',
    reverseDirection: 'Käänteinen suunta',
    groupPadding: 'Ryhmä Täyttö',
    seriesPadding: 'Sarjan Täyttö',
    tile: 'Laatoittaa',
    whisker: 'Viikset',
    cap: 'Korkki',
    capLengthRatio: 'Pituusosuus',
    labelPlacement: 'Sijoitus',
    inside: 'Sisällä',
    outside: 'Ulkopuolella',
    noDataToChart: 'Ei dataa kaavioon.',
    pivotChartRequiresPivotMode: 'Pivot-kaavio tarvitsee Pivot-tilan aktivoidun.',
    chartSettingsToolbarTooltip: 'Valikko',
    chartLinkToolbarTooltip: 'Linkitetty ruudukkoon',
    chartUnlinkToolbarTooltip: 'Irrotettu ruudukosta',
    chartDownloadToolbarTooltip: 'Lataa kaavio',
    chartMenuToolbarTooltip: 'Valikko',
    chartEdit: 'Muokkaa kaaviota',
    chartAdvancedSettings: 'Edistyneemmät asetukset',
    chartLink: 'Linkitä ruudukkoon',
    chartUnlink: 'Irrota ruudukosta',
    chartDownload: 'Lataa kaavio',
    histogramFrequency: 'Taajuus',
    seriesChartType: 'Sarjakaavion tyyppi',
    seriesType: 'Sarjan tyyppi',
    secondaryAxis: 'Toissijainen akseli',
    seriesAdd: 'Lisää sarjat',
    categoryAdd: 'Lisää luokka',
    bar: 'Palkki',
    column: 'Pylväs',
    histogram: 'Histogrammi',
    advancedSettings: 'Edistyneemmät asetukset',
    direction: 'Suunta',
    horizontal: 'Vaakasuora',
    vertical: 'Pystysuora',
    seriesGroupType: 'Ryhmätyyppi',
    groupedSeriesGroupType: 'Ryhmitetty',
    stackedSeriesGroupType: 'Pinottu',
    normalizedSeriesGroupType: '100 % pinottu',
    legendEnabled: 'Käytössä',
    invalidColor: 'Väriarvo on virheellinen',
    groupedColumnFull: 'Ryhmitetty pylväs',
    stackedColumnFull: 'Pinottu pylväs',
    normalizedColumnFull: '100 % pinottu pylväs',
    groupedBarFull: 'Ryhmitetty palkki',
    stackedBarFull: 'Pinottu palkki',
    normalizedBarFull: '100 % pinottu palkki',
    stackedAreaFull: 'Pinottu alue',
    normalizedAreaFull: '100 % pinottu alue',
    customCombo: 'Mukautettu yhdistelmä',
    funnel: 'Suppilo',
    coneFunnel: 'Kartiosuppilo',
    pyramid: 'Pyramidi',
    funnelGroup: 'Suppilo',
    funnelTooltip: 'Suppilo',
    coneFunnelTooltip: 'Kartiosuppilo',
    pyramidTooltip: 'Pyramidi',
    dropOff: 'Poistuminen',
    stageLabels: 'Vaiheiden nimikkeet',
    reverse: 'Käänteinen',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Taso ${variable}. Paina ENTER muokataksesi.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Taso ${variable}. ${variable} Paina ENTER muokataksesi.',
    ariaAdvancedFilterBuilderList: 'Tarkennettu suodattimen rakentaja lista',
    ariaAdvancedFilterBuilderFilterItem: 'Suodatusehto',
    ariaAdvancedFilterBuilderGroupItem: 'Suodatuksen ryhmä',
    ariaAdvancedFilterBuilderColumn: 'Sarake',
    ariaAdvancedFilterBuilderOption: 'Vaihtoehto',
    ariaAdvancedFilterBuilderValueP: 'Arvo',
    ariaAdvancedFilterBuilderJoinOperator: 'Liitosoperaattori',
    ariaAdvancedFilterInput: 'Tarkennettu suodattimen syöte',
    ariaChecked: 'valittu',
    ariaColumn: 'Sarake',
    ariaColumnGroup: 'Sarakeryhmä',
    ariaColumnFiltered: 'Suodatettu sarake',
    ariaColumnSelectAll: 'Vaihda kaikkien sarakkeiden näkyvyys',
    ariaDateFilterInput: 'Päivämäärän suodattimen syöte',
    ariaDefaultListName: 'Lista',
    ariaFilterColumnsInput: 'Suodata sarakkeiden syöte',
    ariaFilterFromValue: 'Suodata arvosta',
    ariaFilterInput: 'Suodattimen syöte',
    ariaFilterList: 'Suodatinlista',
    ariaFilterToValue: 'Suodata arvoon',
    ariaFilterValue: 'Suodattimen arvo',
    ariaFilterMenuOpen: 'Avaa suodattimen valikko',
    ariaFilteringOperator: 'Suodatuksen operaattori',
    ariaHidden: 'piilotettu',
    ariaIndeterminate: 'epämääräinen',
    ariaInputEditor: 'Syötteen muokkaaja',
    ariaMenuColumn: 'Paina ALT ALAS avataksesi sarakevalikon',
    ariaFilterColumn: 'Paina CTRL ENTER avataksesi suodattimen',
    ariaRowDeselect: 'Paina VÄLILYÖNTI poistaaksesi tämän rivin valinnan',
    ariaHeaderSelection: 'Valitse kolumni otsikkovalinnalla',
    ariaSelectAllCells: 'Paina välilyöntiä valitaksesi kaikki solut',
    ariaRowSelectAll: 'Paina VÄLILYÖNTI vaihtaaksesi kaikkien rivien valintaa',
    ariaRowToggleSelection: 'Paina VÄLILYÖNTI vaihtaaksesi rivin valintaa',
    ariaRowSelect: 'Paina VÄLILYÖNTI valitaksesi tämän rivin',
    ariaRowSelectionDisabled: 'Rivin valinta on poistettu käytöstä tälle riville',
    ariaSearch: 'Haku',
    ariaSortableColumn: 'Paina ENTER lajitellaksesi',
    ariaToggleVisibility: 'Paina VÄLILYÖNTI vaihtaaksesi näkyvyyttä',
    ariaToggleCellValue: 'Paina VÄLILYÖNTI vaihtaaksesi solun arvoa',
    ariaUnchecked: 'valitsematon',
    ariaVisible: 'näkyvä',
    ariaSearchFilterValues: 'Etsi suodattimen arvoja',
    ariaPageSizeSelectorLabel: 'Sivun koko',
    ariaChartMenuClose: 'Sulje kaavion muokkausvalikko',
    ariaChartSelected: 'Valittu',
    ariaSkeletonCellLoadingFailed: 'Rivin lataus epäonnistui',
    ariaSkeletonCellLoading: 'Rivin tietoja ladataan',
    ariaDeferSkeletonCellLoading: 'Solu latautuu',

    // ARIA for Batch Edit
    ariaPendingChange: 'Odotusmuutos',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Riviryhmät',
    ariaValuesDropZonePanelLabel: 'Arvot',
    ariaPivotDropZonePanelLabel: 'Sarakkeet',
    ariaDropZoneColumnComponentDescription: 'Poista painamalla DELETE',
    ariaDropZoneColumnValueItemDescription: 'Vaihda aggregaatiotyyppiä painamalla ENTER',
    ariaDropZoneColumnGroupItemDescription: 'Järjestä painamalla ENTER',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' kohta ',
    ariaDropZoneColumnComponentSortAscending: 'nouseva',
    ariaDropZoneColumnComponentSortDescending: 'laskeva',
    ariaLabelDialog: 'Dialogi',
    ariaLabelColumnMenu: 'Sarakkeen valikko',
    ariaLabelColumnFilter: 'Sarakkeen suodatin',
    ariaLabelSelectField: 'Valitse kenttä',

    // Cell Editor
    ariaLabelCellEditor: 'Solumuokkain',
    ariaValidationErrorPrefix: 'Solumuokkaimen vahvistus',
    ariaLabelLoadingContextMenu: 'Ladataan kontekstivalikkoa',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Rikas valintakenttä',
    ariaLabelRichSelectToggleSelection: 'Paina VÄLILYÖNTIÄ vaihtaaksesi valintaa',
    ariaLabelRichSelectDeselectAllItems: 'Paina DELETE poistaaksesi kaikkien kohteiden valinnan',
    ariaLabelRichSelectDeleteSelection: 'Paina DELETE poistaaksesi kohteen valinnan',
    ariaLabelTooltip: 'Vihje',
    ariaLabelContextMenu: 'Kontekstivalikko',
    ariaLabelSubMenu: 'Alivalikko',
    ariaLabelAggregationFunction: 'Aggregointitoiminto',
    ariaLabelAdvancedFilterAutocomplete: 'Edistynyt suodatin automaattinen täydennys',
    ariaLabelAdvancedFilterBuilderAddField: 'Edistynyt suodattimen luonti lisää kenttä',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Edistynyt suodattimen luonti sarakkeen valintakenttä',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Edistynyt suodattimen luonti vaihtoehdon valintakenttä',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Edistynyt suodattimen luonti liittymisoperaattorin valintakenttä',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Sarakeluettelo',
    ariaFilterPanelList: 'Suodatinluettelo',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Lisää suodatin kenttä',
    ariaLabelFilterCardDelete: 'Poista suodatin',
    ariaLabelFilterCardHasEdits: 'On muutoksia',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ' ',
    decimalSeparator: ',',

    // Data types
    true: 'Totta',
    false: 'Väärin',
    invalidDate: 'Virheellinen päivämäärä',
    invalidNumber: 'Virheellinen numero',
    january: 'Tammikuu',
    february: 'Helmikuu',
    march: 'Maaliskuu',
    april: 'Huhtikuu',
    may: 'Toukokuu',
    june: 'Kesäkuu',
    july: 'Heinäkuu',
    august: 'Elokuu',
    september: 'Syyskuu',
    october: 'Lokakuu',
    november: 'Marraskuu',
    december: 'Joulukuu',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'PP/KK/VVVV',
    timeFormatSlashesMMDDYYYY: 'KK/PP/VVVV',
    timeFormatSlashesDDMMYY: 'PP/KK/VV',
    timeFormatSlashesMMDDYY: 'KK/PP/VV',
    timeFormatDotsDDMYY: 'PP.K.VV',
    timeFormatDotsMDDYY: 'K.PP.VV',
    timeFormatDashesYYYYMMDD: 'VVVV-KK-PP',
    timeFormatSpacesDDMMMMYYYY: 'PP KKKK VVVV',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS AM/PM',
};
