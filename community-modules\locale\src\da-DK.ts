/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_DK = {
    // Set Filter
    selectAll: '(Vælg alle)',
    selectAllSearchResults: '(Vælg alle søgeresultater)',
    addCurrentSelectionToFilter: 'Tilføj aktuelle valg til filter',
    searchOoo: 'Søg...',
    blanks: '(Tomt)',
    noMatches: 'Ingen resultater',

    // Number Filter & Text Filter
    filterOoo: 'Filter...',
    equals: 'Lige med',
    notEqual: 'Ikke lige med',
    blank: 'Tom',
    notBlank: 'Ikke tom',
    empty: 'Vælg én',

    // Number Filter
    lessThan: 'Mindre end',
    greaterThan: 'Større end',
    lessThanOrEqual: 'Mindre end eller lig med',
    greaterThanOrEqual: 'Større end eller lig med',
    inRange: 'Mellem',
    inRangeStart: 'Fra',
    inRangeEnd: 'Til',

    // Text Filter
    contains: 'Indeholder',
    notContains: 'Indeholder ikke',
    startsWith: 'Begynder med',
    endsWith: 'Slutter med',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Før',
    after: 'Efter',

    // Filter Conditions
    andCondition: 'OG',
    orCondition: 'ELLER',

    // Filter Buttons
    applyFilter: 'Anvend',
    resetFilter: 'Nulstil',
    clearFilter: 'Ryd',
    cancelFilter: 'Annuller',

    // Filter Titles
    textFilter: 'Tekstfilter',
    numberFilter: 'Nummerfilter',
    dateFilter: 'Dato filter',
    setFilter: 'Sætfilter',

    // Group Column Filter
    groupFilterSelect: 'Vælg felt:',

    // New Filter Tool Panel
    filterSummaryInactive: 'er (alle)',
    filterSummaryContains: 'indeholder',
    filterSummaryNotContains: 'indeholder ikke',
    filterSummaryTextEquals: 'er lig med',
    filterSummaryTextNotEqual: 'er ikke lig med',
    filterSummaryStartsWith: 'begynder med',
    filterSummaryEndsWith: 'slutter med',
    filterSummaryBlank: 'er tom',
    filterSummaryNotBlank: 'er ikke tom',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'mellem',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'er (alle)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'er (${variable})',
    filterSummaryListLong: 'er (${variable}) og ${variable} mere',
    addFilterCard: 'Tilføj filter',
    agTextColumnFilterDisplayName: 'Simpelt filter',
    agNumberColumnFilterDisplayName: 'Simpelt filter',
    agDateColumnFilterDisplayName: 'Simpelt filter',
    agSetColumnFilterDisplayName: 'Valgfilter',
    agMultiColumnFilterDisplayName: 'Kombinationsfilter',
    addFilterPlaceholder: 'Søg i kolonner...',

    // Advanced Filter
    advancedFilterContains: 'indeholder',
    advancedFilterNotContains: 'indeholder-ikke',
    advancedFilterTextEquals: 'er-lig-med',
    advancedFilterTextNotEqual: 'er-ikke-lig-med',
    advancedFilterStartsWith: 'begynder-med',
    advancedFilterEndsWith: 'slutter-med',
    advancedFilterBlank: 'er-tom',
    advancedFilterNotBlank: 'er-ikke-tom',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'er-sand',
    advancedFilterFalse: 'er-falsk',
    advancedFilterAnd: 'OG',
    advancedFilterOr: 'ELLER',
    advancedFilterApply: 'Anvend',
    advancedFilterBuilder: 'Builder',
    advancedFilterValidationMissingColumn: 'Kolonne mangler',
    advancedFilterValidationMissingOption: 'Valgmulighed mangler',
    advancedFilterValidationMissingValue: 'Værdi mangler',
    advancedFilterValidationInvalidColumn: 'Kolonne ikke fundet',
    advancedFilterValidationInvalidOption: 'Valgmulighed ikke fundet',
    advancedFilterValidationMissingQuote: 'Værdi mangler et afsluttende anførselstegn',
    advancedFilterValidationNotANumber: 'Værdi er ikke et tal',
    advancedFilterValidationInvalidDate: 'Værdi er ikke en gyldig dato',
    advancedFilterValidationMissingCondition: 'Betingelse mangler',
    advancedFilterValidationJoinOperatorMismatch: 'Join-operatører inden for en betingelse skal være ens',
    advancedFilterValidationInvalidJoinOperator: 'Join-operator ikke fundet',
    advancedFilterValidationMissingEndBracket: 'Mangler afsluttende parentes',
    advancedFilterValidationExtraEndBracket: 'For mange afsluttende parenteser',
    advancedFilterValidationMessage: 'Udtrykket har en fejl. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Udtrykket har en fejl. ${variable} i slutningen af udtrykket.',
    advancedFilterBuilderTitle: 'Avanceret Filter',
    advancedFilterBuilderApply: 'Anvend',
    advancedFilterBuilderCancel: 'Annuller',
    advancedFilterBuilderAddButtonTooltip: 'Tilføj Filter eller Gruppe',
    advancedFilterBuilderRemoveButtonTooltip: 'Fjern',
    advancedFilterBuilderMoveUpButtonTooltip: 'Flyt op',
    advancedFilterBuilderMoveDownButtonTooltip: 'Flyt ned',
    advancedFilterBuilderAddJoin: 'Tilføj Gruppe',
    advancedFilterBuilderAddCondition: 'Tilføj Filter',
    advancedFilterBuilderSelectColumn: 'Vælg en kolonne',
    advancedFilterBuilderSelectOption: 'Vælg en valgmulighed',
    advancedFilterBuilderEnterValue: 'Indtast en værdi...',
    advancedFilterBuilderValidationAlreadyApplied: 'Aktuelt filter allerede anvendt.',
    advancedFilterBuilderValidationIncomplete: 'Ikke alle betingelser er fuldført.',
    advancedFilterBuilderValidationSelectColumn: 'Skal vælge en kolonne.',
    advancedFilterBuilderValidationSelectOption: 'Skal vælge en valgmulighed.',
    advancedFilterBuilderValidationEnterValue: 'Skal indtaste en værdi.',

    // Editor Validation Errors
    minDateValidation: 'Dato skal være efter ${variable}',
    maxDateValidation: 'Dato skal være før ${variable}',
    maxLengthValidation: 'Må ikke have flere end ${variable} tegn.',
    minValueValidation: 'Skal være større end eller lig med ${variable}',
    maxValueValidation: 'Skal være mindre end eller lig med ${variable}',
    invalidSelectionValidation: 'Ugyldigt valg.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Kolonner',
    filters: 'Filtre',

    // columns tool panel
    pivotMode: 'Pivot-tilstand',
    groups: 'Rækkegrupper',
    rowGroupColumnsEmptyMessage: 'Træk her for at sætte rækkegrupper',
    values: 'Værdier',
    valueColumnsEmptyMessage: 'Træk her for at samle',
    pivots: 'Kolonneetiketter',
    pivotColumnsEmptyMessage: 'Træk her for at sætte kolonneetiketter',

    // Header of the Default Group Column
    group: 'Gruppe',

    // Row Drag
    rowDragRow: 'række',
    rowDragRows: 'rækker',

    // Other
    loadingOoo: 'Indlæser...',
    loadingError: 'FEJL',
    noRowsToShow: 'Ingen rækker at vise',
    enabled: 'Aktiveret',

    // Menu
    pinColumn: 'Fastgør Kolonne',
    pinLeft: 'Fastgør Venstre',
    pinRight: 'Fastgør Højre',
    noPin: 'Ingen Fastgørelse',
    valueAggregation: 'Værdi Aggregering',
    noAggregation: 'Ingen',
    autosizeThisColumn: 'Autosize Denne Kolonne',
    autosizeAllColumns: 'Autosize Alle Kolonner',
    groupBy: 'Gruppér efter',
    ungroupBy: 'Fjern Gruppér efter',
    ungroupAll: 'Fjern Alle Gruppér',
    addToValues: 'Tilføj ${variable} til værdier',
    removeFromValues: 'Fjern ${variable} fra værdier',
    addToLabels: 'Tilføj ${variable} til etiketter',
    removeFromLabels: 'Fjern ${variable} fra etiketter',
    resetColumns: 'Nulstil Kolonner',
    expandAll: 'Udvid Alle Rækkegrupper',
    collapseAll: 'Luk Alle Rækkegrupper',
    copy: 'Kopier',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopier Med Overskrifter',
    copyWithGroupHeaders: 'Kopier med Gruppeoverskrifter',
    cut: 'Klip',
    paste: 'Sæt Ind',
    ctrlV: 'Ctrl+V',
    export: 'Eksporter',
    csvExport: 'CSV Eksport',
    excelExport: 'Excel Eksport',
    columnFilter: 'Kolonne Filter',
    columnChooser: 'Vælg Kolonner',
    chooseColumns: 'Vælg kolonner',
    sortAscending: 'Sorter Stigende',
    sortDescending: 'Sorter Faldende',
    sortUnSort: 'Ryd Sortering',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Sum',
    first: 'Første',
    last: 'Sidste',
    min: 'Min',
    max: 'Maks',
    none: 'Ingen',
    count: 'Tælle',
    avg: 'Gennemsnit',
    filteredRows: 'Filtreret',
    selectedRows: 'Valgte',
    totalRows: 'Total Rækker',
    totalAndFilteredRows: 'Rækker',
    more: 'Mere',
    to: 'til',
    of: 'af',
    page: 'Side',
    pageLastRowUnknown: '?',
    nextPage: 'Næste Side',
    lastPage: 'Sidste Side',
    firstPage: 'Første Side',
    previousPage: 'Forrige Side',
    pageSizeSelectorLabel: 'Side Størrelse:',
    footerTotal: 'Total',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Rul ${variable} i visning',

    // Pivoting
    pivotColumnGroupTotals: 'Totaler',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivotdiagram og pivottilstand',
    pivotChart: 'Pivotdiagram',
    chartRange: 'Diagramområde',
    columnChart: 'Søjlediagram',
    groupedColumn: 'Gruperet',
    stackedColumn: 'Stablet',
    normalizedColumn: '100% Stablet',
    barChart: 'Søjlediagram',
    groupedBar: 'Gruperet',
    stackedBar: 'Stablet',
    normalizedBar: '100% Stablet',
    pieChart: 'Cirkeldiagram',
    pie: 'Cirkeldiagram',
    donut: 'Donut',
    lineChart: 'Linje',
    stackedLine: 'Stablet',
    normalizedLine: '100% Stablet',
    xyChart: 'X Y (Punktdiagram)',
    scatter: 'Punktdiagram',
    bubble: 'Boblediagram',
    areaChart: 'Arealdiagram',
    area: 'Areal',
    stackedArea: 'Stablet',
    normalizedArea: '100% Stablet',
    histogramChart: 'Histogram',
    polarChart: 'Polardiagram',
    radarLine: 'Radarlinje',
    radarArea: 'Radarareal',
    nightingale: 'Nightingale',
    radialColumn: 'Radial Søjle',
    radialBar: 'Radial Søjle',
    statisticalChart: 'Statistisk diagram',
    boxPlot: 'Box Plot',
    rangeBar: 'Intervallsøjle',
    rangeArea: 'Intervallsareal',
    hierarchicalChart: 'Hierarkisk diagram',
    treemap: 'Trædiagram',
    sunburst: 'Solstrålediagram',
    specializedChart: 'Specialiseret diagram',
    waterfall: 'Vandfaldsdiagram',
    heatmap: 'Heatmap',
    combinationChart: 'Kombinationsdiagram',
    columnLineCombo: 'Søjle & Linje',
    AreaColumnCombo: 'Areal & Søjle',

    // Charts
    pivotChartTitle: 'Pivot Diagram',
    rangeChartTitle: 'Rækkediagram',
    settings: 'Diagram',
    data: 'Opsætning',
    format: 'Tilpas',
    categories: 'Kategorier',
    defaultCategory: '(Ingen)',
    series: 'Serier',
    switchCategorySeries: 'Skift Kategori / Serier',
    categoryValues: 'Kategoriværdier',
    seriesLabels: 'Seriemærker',
    aggregate: 'Aggregeret',
    xyValues: 'X Y Værdier',
    paired: 'Parret Tilstand',
    axis: 'Akse',
    xAxis: 'Horisontal Akse',
    yAxis: 'Vertikal Akse',
    polarAxis: 'Polar Akse',
    radiusAxis: 'Radius Akse',
    navigator: 'Navigator',
    zoom: 'Zoom',
    animation: 'Animation',
    crosshair: 'Tværhår',
    color: 'Farve',
    thickness: 'Tykkelse',
    preferredLength: 'Foretrukken Længde',
    xType: 'X Type',
    axisType: 'Akse Type',
    automatic: 'Automatisk',
    category: 'Kategori',
    number: 'Nummer',
    time: 'Tid',
    timeFormat: 'Tidsformat',
    autoRotate: 'Auto Rotate',
    labelRotation: 'Rotation',
    circle: 'Cirkel',
    polygon: 'Polygon',
    square: 'Firkant',
    cross: 'Kryds',
    diamond: 'Diamant',
    plus: 'Plus',
    triangle: 'Trekant',
    heart: 'Hjerte',
    orientation: 'Orientering',
    fixed: 'Fast',
    parallel: 'Parallelt',
    perpendicular: 'Vinkelret',
    radiusAxisPosition: 'Position',
    ticks: 'Markeringer',
    gridLines: 'Gitterlinjer',
    width: 'Bredde',
    height: 'Højde',
    length: 'Længde',
    padding: 'Polstring',
    spacing: 'Mellemrum',
    chartStyle: 'Diagramstil',
    title: 'Titel',
    chartTitles: 'Titler',
    chartTitle: 'Diagramtitel',
    chartSubtitle: 'Undertitel',
    horizontalAxisTitle: 'Horisontal Akse Titel',
    verticalAxisTitle: 'Vertikal Akse Titel',
    polarAxisTitle: 'Polar Akse Titel',
    titlePlaceholder: 'Diagramtitel',
    background: 'Baggrund',
    font: 'Skrifttype',
    weight: 'Vægt',
    top: 'Top',
    right: 'Højre',
    bottom: 'Bund',
    left: 'Venstre',
    labels: 'Mærker',
    calloutLabels: 'Callout Mærker',
    sectorLabels: 'Sektormærker',
    positionRatio: 'Positionsforhold',
    size: 'Størrelse',
    shape: 'Form',
    minSize: 'Minimumsstørrelse',
    maxSize: 'Maksimalstørrelse',
    legend: 'Legende',
    position: 'Position',
    markerSize: 'Markørstørrelse',
    markerStroke: 'Markørstrøg',
    markerPadding: 'Markørpolstring',
    itemSpacing: 'Vareafstand',
    itemPaddingX: 'Varepolstring X',
    itemPaddingY: 'Varepolstring Y',
    layoutHorizontalSpacing: 'Horisontal Afstand',
    layoutVerticalSpacing: 'Vertikal Afstand',
    strokeWidth: 'Strøgtykkelse',
    offset: 'Forskydning',
    offsets: 'Forskydninger',
    tooltips: 'Tooltips',
    callout: 'Callout',
    markers: 'Markører',
    shadow: 'Skygge',
    blur: 'Sløring',
    xOffset: 'X Forskydning',
    yOffset: 'Y Forskydning',
    lineWidth: 'Linjetykkelse',
    lineDash: 'Linje Markering',
    lineDashOffset: 'Markering Forskydning',
    scrollingZoom: 'Rulning',
    scrollingStep: 'Rulnigstrin',
    selectingZoom: 'Valg',
    durationMillis: 'Varighed (ms)',
    crosshairLabel: 'Mærkat',
    crosshairSnap: 'Snap til knude',
    normal: 'Normal',
    bold: 'Fed',
    italic: 'Kursiv',
    boldItalic: 'Fed Kursiv',
    predefined: 'Foruddefineret',
    fillOpacity: 'Fyld Opacitet',
    strokeColor: 'Linje Farve',
    strokeOpacity: 'Linjegennemsigtighed',
    miniChart: 'Mini-diagram',
    histogramBinCount: 'Bin tælling',
    connectorLine: 'Forbindelselinje',
    seriesItems: 'Seriens Elementer',
    seriesItemType: 'Varetype',
    seriesItemPositive: 'Positiv',
    seriesItemNegative: 'Negativ',
    seriesItemLabels: 'Varemærker',
    columnGroup: 'Kolonne',
    barGroup: 'Søjle',
    pieGroup: 'Tærte',
    lineGroup: 'Linje',
    scatterGroup: 'X Y (Spredning)',
    areaGroup: 'Areal',
    polarGroup: 'Polar',
    statisticalGroup: 'Statistisk',
    hierarchicalGroup: 'Hierarkisk',
    specializedGroup: 'Specialiseret',
    combinationGroup: 'Kombination',
    groupedColumnTooltip: 'Grupperet',
    stackedColumnTooltip: 'Stablet',
    normalizedColumnTooltip: '100% Stablet',
    groupedBarTooltip: 'Grupperet',
    stackedBarTooltip: 'Stablet',
    normalizedBarTooltip: '100% Stablet',
    pieTooltip: 'Tærte',
    donutTooltip: 'Donut',
    lineTooltip: 'Linje',
    stackedLineTooltip: 'Stablet',
    normalizedLineTooltip: '100% stablet',
    groupedAreaTooltip: 'Areal',
    stackedAreaTooltip: 'Stablet',
    normalizedAreaTooltip: '100% Stablet',
    scatterTooltip: 'Spredning',
    bubbleTooltip: 'Boble',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Radial Kolonne',
    radialBarTooltip: 'Radial Søjle',
    radarLineTooltip: 'Radar Linje',
    radarAreaTooltip: 'Radar Areal',
    nightingaleTooltip: 'Nattergal',
    rangeBarTooltip: 'Område Søjle',
    rangeAreaTooltip: 'Område Areal',
    boxPlotTooltip: 'Boksplot',
    treemapTooltip: 'Trekort',
    sunburstTooltip: 'Solstråle',
    waterfallTooltip: 'Vandfald',
    heatmapTooltip: 'Varmekort',
    columnLineComboTooltip: 'Kolonne & Linje',
    areaColumnComboTooltip: 'Areal & Kolonne',
    customComboTooltip: 'Brugerdefineret Kombination',
    innerRadius: 'Indre Radius',
    startAngle: 'Startvinkel',
    endAngle: 'Slutvinkel',
    reverseDirection: 'Omvendt Retning',
    groupPadding: 'Gruppepolstring',
    seriesPadding: 'Seriepolstring',
    tile: 'Flise',
    whisker: 'Vibrisse',
    cap: 'Hætte',
    capLengthRatio: 'Længdeforhold',
    labelPlacement: 'Mærkeplacering',
    inside: 'Inde',
    outside: 'Ude',
    noDataToChart: 'Ingen data til rådighed til at lave diagram',
    pivotChartRequiresPivotMode: 'Pivot Diagrams kræver at Pivot Tilstand er aktiveret',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Knyttet til Gitter',
    chartUnlinkToolbarTooltip: 'Ikke knyttet til Gitter',
    chartDownloadToolbarTooltip: 'Download Diagram',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Rediger Diagram',
    chartAdvancedSettings: 'Avancerede Indstillinger',
    chartLink: 'Knyt til Gitter',
    chartUnlink: 'Fjern Knytning fra Gitter',
    chartDownload: 'Download Diagram',
    histogramFrequency: 'Frekvens',
    seriesChartType: 'Serie Diagram Type',
    seriesType: 'Serietype',
    secondaryAxis: 'Sekundær Akse',
    seriesAdd: 'Tilføj en serie',
    categoryAdd: 'Tilføj en kategori',
    bar: 'Søjle',
    column: 'Kolonne',
    histogram: 'Histogram',
    advancedSettings: 'Avancerede Indstillinger',
    direction: 'Retning',
    horizontal: 'Horisontal',
    vertical: 'Vertikal',
    seriesGroupType: 'Gruppetype',
    groupedSeriesGroupType: 'Grupperet',
    stackedSeriesGroupType: 'Stablet',
    normalizedSeriesGroupType: '100% Stablet',
    legendEnabled: 'Aktiveret',
    invalidColor: 'Farveværdien er ugyldig',
    groupedColumnFull: 'Grupperet Kolonne',
    stackedColumnFull: 'Stablet Kolonne',
    normalizedColumnFull: '100% Stablet Kolonne',
    groupedBarFull: 'Grupperet Søjle',
    stackedBarFull: 'Stablet Søjle',
    normalizedBarFull: '100% Stablet Søjle',
    stackedAreaFull: 'Stablet Areal',
    normalizedAreaFull: '100% Stablet Areal',
    customCombo: 'Brugerdefineret Kombination',
    funnel: 'Tragt',
    coneFunnel: 'Kegletragt',
    pyramid: 'Pyramide',
    funnelGroup: 'Tragt',
    funnelTooltip: 'Tragt',
    coneFunnelTooltip: 'Kegletragt',
    pyramidTooltip: 'Pyramide',
    dropOff: 'Frafald',
    stageLabels: 'Trinmærkater',
    reverse: 'Omvendt',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Niveau ${variable}. Tryk på ENTER for at redigere.',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Niveau ${variable}. ${variable} Tryk på ENTER for at redigere.',
    ariaAdvancedFilterBuilderList: 'Avanceret Filter Builder Liste',
    ariaAdvancedFilterBuilderFilterItem: 'Filterbetingelse',
    ariaAdvancedFilterBuilderGroupItem: 'Filtergruppe',
    ariaAdvancedFilterBuilderColumn: 'Kolonne',
    ariaAdvancedFilterBuilderOption: 'Valgmulighed',
    ariaAdvancedFilterBuilderValueP: 'Værdi',
    ariaAdvancedFilterBuilderJoinOperator: 'Sammenkædningsoperator',
    ariaAdvancedFilterInput: 'Avanceret Filter Input',
    ariaChecked: 'markeret',
    ariaColumn: 'Kolonne',
    ariaColumnGroup: 'Kolonnegruppe',
    ariaColumnFiltered: 'Kolonne Filtreret',
    ariaColumnSelectAll: 'Skift synlighed for alle kolonner',
    ariaDateFilterInput: 'Dato Filter Input',
    ariaDefaultListName: 'Liste',
    ariaFilterColumnsInput: 'Filtrer Kolonner Input',
    ariaFilterFromValue: 'Filtrer fra værdi',
    ariaFilterInput: 'Filter Input',
    ariaFilterList: 'Filterliste',
    ariaFilterToValue: 'Filtrer til værdi',
    ariaFilterValue: 'Filtrerværdi',
    ariaFilterMenuOpen: 'Åbn Filtermenu',
    ariaFilteringOperator: 'Filtreringsoperator',
    ariaHidden: 'skjult',
    ariaIndeterminate: 'ubestemt',
    ariaInputEditor: 'Inputredaktør',
    ariaMenuColumn: 'Tryk på ALT NED for at åbne kolonnemenu',
    ariaFilterColumn: 'Tryk på CTRL ENTER for at åbne filter',
    ariaRowDeselect: 'Tryk på MELLEMRUM for at fravælge denne række',
    ariaHeaderSelection: 'Kolonne med hovedvalg',
    ariaSelectAllCells: 'Tryk på mellemrumstasten for at vælge alle celler',
    ariaRowSelectAll: 'Tryk på MELLEMRUM for at skifte alle rækkers valg',
    ariaRowToggleSelection: 'Tryk på MELLEMRUM for at skifte rækkes valg',
    ariaRowSelect: 'Tryk på MELLEMRUM for at vælge denne række',
    ariaRowSelectionDisabled: 'Rækkevalg er deaktiveret for denne række',
    ariaSearch: 'Søg',
    ariaSortableColumn: 'Tryk på ENTER for at sortere',
    ariaToggleVisibility: 'Tryk på MELLEMRUM for at skifte synlighed',
    ariaToggleCellValue: 'Tryk på MELLEMRUM for at skifte celleværdi',
    ariaUnchecked: 'umarkeret',
    ariaVisible: 'synlig',
    ariaSearchFilterValues: 'Søg filterværdier',
    ariaPageSizeSelectorLabel: 'Sidestørrelse',
    ariaChartMenuClose: 'Luk Diagram Redigeringsmenu',
    ariaChartSelected: 'Valgt',
    ariaSkeletonCellLoadingFailed: 'Række kunne ikke indlæses',
    ariaSkeletonCellLoading: 'Række data indlæses',
    ariaDeferSkeletonCellLoading: 'Celle indlæses',

    // ARIA for Batch Edit
    ariaPendingChange: 'Afventende ændring',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Rækkegrupper',
    ariaValuesDropZonePanelLabel: 'Værdier',
    ariaPivotDropZonePanelLabel: 'Kolonneetiketter',
    ariaDropZoneColumnComponentDescription: 'Tryk DELETE for at fjerne',
    ariaDropZoneColumnValueItemDescription: 'Tryk ENTER for at ændre aggregattypen',
    ariaDropZoneColumnGroupItemDescription: 'Tryk ENTER for at sortere',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' of ',
    ariaDropZoneColumnComponentSortAscending: 'stigende',
    ariaDropZoneColumnComponentSortDescending: 'faldende',
    ariaLabelDialog: 'Dialog',
    ariaLabelColumnMenu: 'Kolonnemenu',
    ariaLabelColumnFilter: 'Kolonnefilter',
    ariaLabelSelectField: 'Vælg felt',

    // Cell Editor
    ariaLabelCellEditor: 'Celleditor',
    ariaValidationErrorPrefix: 'Celleditorvalidering',
    ariaLabelLoadingContextMenu: 'Indlæser kontekstmenu',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Rich Select Felt',
    ariaLabelRichSelectToggleSelection: 'Tryk på MELLEMRUM for at skifte valg',
    ariaLabelRichSelectDeselectAllItems: 'Tryk på DELETE for at fjerne markeringen af alle emner',
    ariaLabelRichSelectDeleteSelection: 'Tryk på DELETE for at fjerne markeringen af emne',
    ariaLabelTooltip: 'Værktøjstip',
    ariaLabelContextMenu: 'Kontekstmenu',
    ariaLabelSubMenu: 'UnderMenu',
    ariaLabelAggregationFunction: 'Aggregeringsfunktion',
    ariaLabelAdvancedFilterAutocomplete: 'Avanceret Filter Autocomplete',
    ariaLabelAdvancedFilterBuilderAddField: 'Avanceret Filter Builder Tilføj Felt',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Avanceret Filter Builder Kolonne Vælg Felt',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Avanceret Filter Builder Valg Vælg Felt',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Avanceret Filter Builder Join Operator Vælg Felt',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Kolonner Liste',
    ariaFilterPanelList: 'Filter Liste',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Tilføj filterfelt',
    ariaLabelFilterCardDelete: 'Slet filter',
    ariaLabelFilterCardHasEdits: 'Har redigeringer',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'True',
    false: 'False',
    invalidDate: 'Ugyldig Dato',
    invalidNumber: 'Ugyldigt Nummer',
    january: 'Januar',
    february: 'Februar',
    march: 'Marts',
    april: 'April',
    may: 'Maj',
    june: 'Juni',
    july: 'Juli',
    august: 'August',
    september: 'September',
    october: 'Oktober',
    november: 'November',
    december: 'December',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'TT:MM:SS',
    timeFormatHHMMSSAmPm: 'TT:MM:SS AM/PM',
};
