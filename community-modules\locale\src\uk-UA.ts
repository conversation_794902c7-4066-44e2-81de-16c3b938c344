/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_UA = {
    // Set Filter
    selectAll: '(Вибрати все)',
    selectAllSearchResults: '(Вибрати всі результати пошуку)',
    addCurrentSelectionToFilter: 'Додати поточний вибір до фільтру',
    searchOoo: 'Пошук...',
    blanks: '(Пусті)',
    noMatches: 'Співпадінь не знайдено',

    // Number Filter & Text Filter
    filterOoo: 'Фільтр...',
    equals: 'Дорівнює',
    notEqual: 'Не дорівнює',
    blank: 'Порожній',
    notBlank: 'Не порожній',
    empty: 'Виберіть один',

    // Number Filter
    lessThan: 'Менше ніж',
    greaterThan: 'Більше ніж',
    lessThanOrEqual: 'Менше або дорівнює',
    greaterThanOrEqual: 'Більше або дорівнює',
    inRange: 'Між',
    inRangeStart: 'Від',
    inRangeEnd: 'До',

    // Text Filter
    contains: 'Містить',
    notContains: 'Не містить',
    startsWith: 'Починається з',
    endsWith: 'Закінчується на',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'До',
    after: 'Після',

    // Filter Conditions
    andCondition: 'І',
    orCondition: 'АБО',

    // Filter Buttons
    applyFilter: 'Застосувати',
    resetFilter: 'Скинути',
    clearFilter: 'Очистити',
    cancelFilter: 'Скасувати',

    // Filter Titles
    textFilter: 'Текстовий фільтр',
    numberFilter: 'Числовий фільтр',
    dateFilter: 'Фільтр за датою',
    setFilter: 'Фільтр за множиною',

    // Group Column Filter
    groupFilterSelect: 'Виберіть поле:',

    // New Filter Tool Panel
    filterSummaryInactive: 'дорівнює (Усі)',
    filterSummaryContains: 'містить',
    filterSummaryNotContains: 'не містить',
    filterSummaryTextEquals: 'дорівнює',
    filterSummaryTextNotEqual: 'не дорівнює',
    filterSummaryStartsWith: 'починається з',
    filterSummaryEndsWith: 'закінчується на',
    filterSummaryBlank: 'порожньо',
    filterSummaryNotBlank: 'не порожньо',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'між',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'дорівнює (Усі)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'дорівнює (${variable})',
    filterSummaryListLong: 'дорівнює (${variable}) та ${variable} більше',
    addFilterCard: 'Додати фільтр',
    agTextColumnFilterDisplayName: 'Простий фільтр',
    agNumberColumnFilterDisplayName: 'Простий фільтр',
    agDateColumnFilterDisplayName: 'Простий фільтр',
    agSetColumnFilterDisplayName: 'Фільтр вибору',
    agMultiColumnFilterDisplayName: 'Комбінований фільтр',
    addFilterPlaceholder: 'Пошук у стовпцях...',

    // Advanced Filter
    advancedFilterContains: 'містить',
    advancedFilterNotContains: 'не містить',
    advancedFilterTextEquals: 'дорівнює',
    advancedFilterTextNotEqual: 'не дорівнює',
    advancedFilterStartsWith: 'починається з',
    advancedFilterEndsWith: 'закінчується на',
    advancedFilterBlank: 'є пустим',
    advancedFilterNotBlank: 'не є пустим',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'є істинним',
    advancedFilterFalse: 'є хибним',
    advancedFilterAnd: 'І',
    advancedFilterOr: 'АБО',
    advancedFilterApply: 'Застосувати',
    advancedFilterBuilder: 'Побудова',
    advancedFilterValidationMissingColumn: 'Відсутня колонка',
    advancedFilterValidationMissingOption: 'Відсутній варіант',
    advancedFilterValidationMissingValue: 'Відсутнє значення',
    advancedFilterValidationInvalidColumn: 'Колонку не знайдено',
    advancedFilterValidationInvalidOption: 'Варіант не знайдено',
    advancedFilterValidationMissingQuote: 'Значення без кінцевої лапки',
    advancedFilterValidationNotANumber: 'Значення не є числом',
    advancedFilterValidationInvalidDate: 'Значення не є дійсною датою',
    advancedFilterValidationMissingCondition: 'Відсутня умова',
    advancedFilterValidationJoinOperatorMismatch: "Оператори об'єднання в межах умови мають бути однаковими",
    advancedFilterValidationInvalidJoinOperator: "Оператор об'єднання не знайдено",
    advancedFilterValidationMissingEndBracket: 'Відсутня кінцева дужка',
    advancedFilterValidationExtraEndBracket: 'Забагато кінцевих дужок',
    advancedFilterValidationMessage: 'У виразі є помилка. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'У виразі є помилка. ${variable} в кінці виразу.',
    advancedFilterBuilderTitle: 'Розширений фільтр',
    advancedFilterBuilderApply: 'Застосувати',
    advancedFilterBuilderCancel: 'Скасувати',
    advancedFilterBuilderAddButtonTooltip: 'Додати фільтр або групу',
    advancedFilterBuilderRemoveButtonTooltip: 'Видалити',
    advancedFilterBuilderMoveUpButtonTooltip: 'Перемістити вгору',
    advancedFilterBuilderMoveDownButtonTooltip: 'Перемістити вниз',
    advancedFilterBuilderAddJoin: 'Додати групу',
    advancedFilterBuilderAddCondition: 'Додати фільтр',
    advancedFilterBuilderSelectColumn: 'Оберіть колонку',
    advancedFilterBuilderSelectOption: 'Оберіть варіант',
    advancedFilterBuilderEnterValue: 'Введіть значення...',
    advancedFilterBuilderValidationAlreadyApplied: 'Поточний фільтр вже застосований.',
    advancedFilterBuilderValidationIncomplete: 'Не всі умови заповнені.',
    advancedFilterBuilderValidationSelectColumn: 'Потрібно обрати колонку.',
    advancedFilterBuilderValidationSelectOption: 'Потрібно обрати варіант.',
    advancedFilterBuilderValidationEnterValue: 'Потрібно ввести значення.',

    // Editor Validation Errors
    minDateValidation: 'Дата повинна бути після ${variable}',
    maxDateValidation: 'Дата повинна бути до ${variable}',
    maxLengthValidation: 'Повинно бути ${variable} символів або менше.',
    minValueValidation: 'Повинно бути більше або дорівнювати ${variable}',
    maxValueValidation: 'Повинно бути менше або дорівнювати ${variable}',
    invalidSelectionValidation: 'Неправильний вибір.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Стовпці',
    filters: 'Фільтри',

    // columns tool panel
    pivotMode: 'Режим зведення',
    groups: 'Групи рядків',
    rowGroupColumnsEmptyMessage: 'Перетягніть сюди для встановлення груп рядків',
    values: 'Значення',
    valueColumnsEmptyMessage: 'Перетягніть сюди для агрегування',
    pivots: 'Мітки стовпця',
    pivotColumnsEmptyMessage: 'Перетягніть сюди для встановлення міток стовпця',

    // Header of the Default Group Column
    group: 'Група',

    // Row Drag
    rowDragRow: 'рядок',
    rowDragRows: 'ряди',

    // Other
    loadingOoo: 'Завантаження...',
    loadingError: 'ПОМИЛКА',
    noRowsToShow: 'Немає рядків для відображення',
    enabled: 'Увімкнено',

    // Menu
    pinColumn: 'Закріпити Стовпець',
    pinLeft: 'Закріпити Ліворуч',
    pinRight: 'Закріпити Праворуч',
    noPin: 'Не Закріпляти',
    valueAggregation: 'Агрегація Значень',
    noAggregation: 'Жодна',
    autosizeThisColumn: 'Автоматично Змінити Розмір Цього Стовпця',
    autosizeAllColumns: 'Автоматично Змінити Розмір Усіх Стовпців',
    groupBy: 'Групувати за',
    ungroupBy: 'Розгрупувати за',
    ungroupAll: 'Розгрупувати Все',
    addToValues: 'Додати ${variable} до значень',
    removeFromValues: 'Видалити ${variable} зі значень',
    addToLabels: 'Додати ${variable} до міток',
    removeFromLabels: 'Видалити ${variable} з міток',
    resetColumns: 'Скинути Налаштування Стовпців',
    expandAll: 'Розгорнути Всі Групи Рядків',
    collapseAll: 'Згорнути Всі Групи Рядків',
    copy: 'Копіювати',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Копіювати з Заголовками',
    copyWithGroupHeaders: 'Копіювати з Груповими Заголовками',
    cut: 'Вирізати',
    paste: 'Вставити',
    ctrlV: 'Ctrl+V',
    export: 'Експорт',
    csvExport: 'Експорт CSV',
    excelExport: 'Експорт Excel',
    columnFilter: 'Фільтр Стовпців',
    columnChooser: 'Вибрати Стовпці',
    chooseColumns: 'Виберіть стовпці',
    sortAscending: 'Сортувати за Зростанням',
    sortDescending: 'Сортувати за Спаданням',
    sortUnSort: 'Очистити Сортування',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Сума',
    first: 'Перший',
    last: 'Останній',
    min: 'Мін',
    max: 'Макс',
    none: 'Немає',
    count: 'Кількість',
    avg: 'Середнє',
    filteredRows: 'Відфільтровано',
    selectedRows: 'Вибрано',
    totalRows: 'Усього рядків',
    totalAndFilteredRows: 'Рядки',
    more: 'Більше',
    to: 'до',
    of: 'з',
    page: 'Сторінка',
    pageLastRowUnknown: '?',
    nextPage: 'Наступна сторінка',
    lastPage: 'Остання сторінка',
    firstPage: 'Перша сторінка',
    previousPage: 'Попередня сторінка',
    pageSizeSelectorLabel: 'Розмір сторінки:',
    footerTotal: 'Усього',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Прокрутити ${variable} до подання',

    // Pivoting
    pivotColumnGroupTotals: 'Загальна сума',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Зведена діаграма та режим зведення',
    pivotChart: 'Зведена діаграма',
    chartRange: 'Діапазон діаграми',
    columnChart: 'Стовпчаста діаграма',
    groupedColumn: 'Групований',
    stackedColumn: 'Шаруватий',
    normalizedColumn: 'Шаруватий на 100%',
    barChart: 'Гістограма',
    groupedBar: 'Групований',
    stackedBar: 'Шаруватий',
    normalizedBar: 'Шаруватий на 100%',
    pieChart: 'Кругова діаграма',
    pie: 'Кругова діаграма',
    donut: 'Кільцева діаграма',
    lineChart: 'Лінійний',
    stackedLine: 'Шаруватий',
    normalizedLine: '100% Шаруватий',
    xyChart: 'X Y (Розсіяна)',
    scatter: 'Розсіяна',
    bubble: 'Бульбашкова',
    areaChart: 'Площа',
    area: 'Площа',
    stackedArea: 'Шаруватий',
    normalizedArea: 'Шаруватий на 100%',
    histogramChart: 'Гістограма',
    polarChart: 'Полярна діаграма',
    radarLine: 'Радарна лінія',
    radarArea: 'Радарна площа',
    nightingale: 'Діаграма Найтінгейла',
    radialColumn: 'Радіальний стовпчик',
    radialBar: 'Радіальна гістограма',
    statisticalChart: 'Статистична діаграма',
    boxPlot: 'Схема коробчатого вигляду',
    rangeBar: 'Діаграма інтервалів',
    rangeArea: 'Область інтервалів',
    hierarchicalChart: 'Ієрархічна діаграма',
    treemap: 'Деревоподібна діаграма',
    sunburst: 'Сонячний графік',
    specializedChart: 'Спеціалізована діаграма',
    waterfall: 'Водоспадна діаграма',
    heatmap: 'Теплова карта',
    combinationChart: 'Комбінована діаграма',
    columnLineCombo: 'Стовпчик та лінія',
    AreaColumnCombo: 'Площа та стовпчик',

    // Charts
    pivotChartTitle: 'Зведена діаграма',
    rangeChartTitle: 'Діапазон діаграм',
    settings: 'Графік',
    data: 'Налаштування',
    format: 'Налаштувати',
    categories: 'Категорії',
    defaultCategory: '(Немає)',
    series: 'Ряди',
    switchCategorySeries: 'Перемкнути категорію / серію',
    categoryValues: 'Значення категорії',
    seriesLabels: 'Позначки серій',
    aggregate: 'Агрегат',
    xyValues: 'Значення X Y',
    paired: 'Парний режим',
    axis: 'Вісь',
    xAxis: 'Горизонтальна вісь',
    yAxis: 'Вертикальна вісь',
    polarAxis: 'Полярна вісь',
    radiusAxis: 'Вісь радіуса',
    navigator: 'Навігатор',
    zoom: 'Збільшити',
    animation: 'Анімація',
    crosshair: 'Перехрестя',
    color: 'Колір',
    thickness: 'Товщина',
    preferredLength: 'Бажана довжина',
    xType: 'Тип X',
    axisType: 'Тип осі',
    automatic: 'Автоматично',
    category: 'Категорія',
    number: 'Число',
    time: 'Час',
    timeFormat: 'Формат часу',
    autoRotate: 'Автоматичне обертання',
    labelRotation: 'Обертання ярлика',
    circle: 'Коло',
    polygon: 'Багатокутник',
    square: 'Квадрат',
    cross: 'Хрест',
    diamond: 'Ромб',
    plus: 'Плюс',
    triangle: 'Трикутник',
    heart: 'Серце',
    orientation: 'Орієнтація',
    fixed: 'Фіксований',
    parallel: 'Паралельно',
    perpendicular: 'Перпендикуляр',
    radiusAxisPosition: 'Позиція',
    ticks: 'Мітки',
    gridLines: 'Лінії сітки',
    width: 'Ширина',
    height: 'Висота',
    length: 'Довжина',
    padding: 'Відступ',
    spacing: 'Відставання',
    chartStyle: 'Стиль діаграми',
    title: 'Заголовок',
    chartTitles: 'Заголовки',
    chartTitle: 'Назва діаграми',
    chartSubtitle: 'Підзаголовок',
    horizontalAxisTitle: 'Назва горизонтальної осі',
    verticalAxisTitle: 'Назва вертикальної осі',
    polarAxisTitle: 'Назва полярної осі',
    titlePlaceholder: 'Назва діаграми',
    background: 'Фон',
    font: 'Шрифт',
    weight: 'Вага',
    top: 'Верх',
    right: 'Праворуч',
    bottom: 'Низ',
    left: 'Ліворуч',
    labels: 'Ярлики',
    calloutLabels: 'Ярлики виноски',
    sectorLabels: 'Ярлики сектору',
    positionRatio: 'Співвідношення позиції',
    size: 'Розмір',
    shape: 'Форма',
    minSize: 'Мінімальний розмір',
    maxSize: 'Максимальний розмір',
    legend: 'Легенда',
    position: 'Позиція',
    markerSize: 'Розмір маркера',
    markerStroke: 'Обведення маркера',
    markerPadding: 'Відступ маркера',
    itemSpacing: 'Відставання елемента',
    itemPaddingX: 'Відступ елемента X',
    itemPaddingY: 'Відступ елемента Y',
    layoutHorizontalSpacing: 'Горизонтальне відставання',
    layoutVerticalSpacing: 'Вертикальне відставання',
    strokeWidth: 'Товщина лінії',
    offset: 'Зсув',
    offsets: 'Зсуви',
    tooltips: 'Підказки',
    callout: 'Виноска',
    markers: 'Маркери',
    shadow: 'Тінь',
    blur: 'Розмиття',
    xOffset: 'Зсув X',
    yOffset: 'Зсув Y',
    lineWidth: 'Товщина лінії',
    lineDash: 'Штрихування лінії',
    lineDashOffset: 'Зсув штрихування',
    scrollingZoom: 'Прокрутка',
    scrollingStep: 'Крок прокрутки',
    selectingZoom: 'Вибір',
    durationMillis: 'Тривалість (мс)',
    crosshairLabel: 'Ярлик',
    crosshairSnap: "Прив'язка до вузла",
    normal: 'Звичайний',
    bold: 'Напівжирний',
    italic: 'Курсив',
    boldItalic: 'Напівжирний курсив',
    predefined: 'Визначено наперед',
    fillOpacity: 'Непрозорість заливки',
    strokeColor: 'Колір лінії',
    strokeOpacity: 'Непрозорість лінії',
    miniChart: 'Міні-діаграма',
    histogramBinCount: 'Кількість бінів',
    connectorLine: "З'єднувальна лінія",
    seriesItems: 'Елементи серій',
    seriesItemType: 'Тип елемента',
    seriesItemPositive: 'Позитивний',
    seriesItemNegative: 'Негативний',
    seriesItemLabels: 'Ярлики елементів',
    columnGroup: 'Стовпець',
    barGroup: 'Смуга',
    pieGroup: 'Кругова',
    lineGroup: 'Лінія',
    scatterGroup: 'X Y (Розкидання)',
    areaGroup: 'Область',
    polarGroup: 'Полярна',
    statisticalGroup: 'Статистична',
    hierarchicalGroup: 'Ієрархічна',
    specializedGroup: 'Спеціалізована',
    combinationGroup: 'Комбінація',
    groupedColumnTooltip: 'Групована',
    stackedColumnTooltip: 'Штабельована',
    normalizedColumnTooltip: '100% штабельована',
    groupedBarTooltip: 'Групована',
    stackedBarTooltip: 'Штабельована',
    normalizedBarTooltip: '100% штабельована',
    pieTooltip: 'Кругова',
    donutTooltip: 'Кільцева',
    lineTooltip: 'Лінія',
    stackedLineTooltip: 'Складена',
    normalizedLineTooltip: '100% Складена',
    groupedAreaTooltip: 'Область',
    stackedAreaTooltip: 'Штабельована',
    normalizedAreaTooltip: '100% штабельована',
    scatterTooltip: 'Розкидання',
    bubbleTooltip: 'Бульбашка',
    histogramTooltip: 'Гістограма',
    radialColumnTooltip: 'Радіальний стовпець',
    radialBarTooltip: 'Радіальна смуга',
    radarLineTooltip: 'Радарна лінія',
    radarAreaTooltip: 'Радарна область',
    nightingaleTooltip: 'Діаграма Найтінгейлів',
    rangeBarTooltip: 'Діапазонна смуга',
    rangeAreaTooltip: 'Діапазонна область',
    boxPlotTooltip: 'Ящиковий графік',
    treemapTooltip: 'Трімеп',
    sunburstTooltip: 'Сонячний сплеск',
    waterfallTooltip: 'Водоспад',
    heatmapTooltip: 'Теплова карта',
    columnLineComboTooltip: 'Стовпець і лінія',
    areaColumnComboTooltip: 'Область і стовпець',
    customComboTooltip: 'Спеціальна комбінація',
    innerRadius: 'Внутрішній радіус',
    startAngle: 'Кут початку',
    endAngle: 'Кут закінчення',
    reverseDirection: 'Зворотний напрямок',
    groupPadding: 'Відступ групи',
    seriesPadding: 'Відступ серій',
    tile: 'Плитка',
    whisker: 'Вусики',
    cap: 'Ковпачок',
    capLengthRatio: 'Співвідношення довжини ковпачка',
    labelPlacement: 'Розташування ярлика',
    inside: 'Всередині',
    outside: 'Ззовні',
    noDataToChart: 'Немає даних для побудови графіка.',
    pivotChartRequiresPivotMode: 'Зведена діаграма вимагає увімкнення режиму зведення.',
    chartSettingsToolbarTooltip: 'Меню',
    chartLinkToolbarTooltip: "Пов'язано з сіткою",
    chartUnlinkToolbarTooltip: 'Відключено від сітки',
    chartDownloadToolbarTooltip: 'Завантажити діаграму',
    chartMenuToolbarTooltip: 'Меню',
    chartEdit: 'Редагувати діаграму',
    chartAdvancedSettings: 'Розширені налаштування',
    chartLink: "Зв'язати з сіткою",
    chartUnlink: 'Відключити від сітки',
    chartDownload: 'Завантажити діаграму',
    histogramFrequency: 'Частота',
    seriesChartType: 'Тип серії графіків',
    seriesType: 'Тип серії',
    secondaryAxis: 'Вторинна вісь',
    seriesAdd: 'Додати серію',
    categoryAdd: 'Додати категорію',
    bar: 'Смуга',
    column: 'Стовпець',
    histogram: 'Гістограма',
    advancedSettings: 'Розширені налаштування',
    direction: 'Напрямок',
    horizontal: 'Горизонтальний',
    vertical: 'Вертикальний',
    seriesGroupType: 'Тип групи серій',
    groupedSeriesGroupType: 'Групована',
    stackedSeriesGroupType: 'Штабельована',
    normalizedSeriesGroupType: '100% штабельована',
    legendEnabled: 'Увімкнено',
    invalidColor: 'Недійсне значення кольору',
    groupedColumnFull: 'Групований стовпець',
    stackedColumnFull: 'Штабельований стовпець',
    normalizedColumnFull: '100% штабельований стовпець',
    groupedBarFull: 'Групована смуга',
    stackedBarFull: 'Штабельована смуга',
    normalizedBarFull: '100% штабельована смуга',
    stackedAreaFull: 'Штабельована область',
    normalizedAreaFull: '100% штабельована область',
    customCombo: 'Спеціальна комбінація',
    funnel: 'Воронка',
    coneFunnel: 'Конічна Воронка',
    pyramid: 'Піраміда',
    funnelGroup: 'Воронка',
    funnelTooltip: 'Воронка',
    coneFunnelTooltip: 'Конічна Воронка',
    pyramidTooltip: 'Піраміда',
    dropOff: 'Відтік',
    stageLabels: 'Мітки Етапів',
    reverse: 'Зворотний',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Рівень ${variable}. Натисніть ENTER для редагування.',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Рівень ${variable}. ${variable} Натисніть ENTER для редагування.',
    ariaAdvancedFilterBuilderList: 'Список розширеного фільтру',
    ariaAdvancedFilterBuilderFilterItem: 'Умова фільтру',
    ariaAdvancedFilterBuilderGroupItem: 'Група фільтрів',
    ariaAdvancedFilterBuilderColumn: 'Колонка',
    ariaAdvancedFilterBuilderOption: 'Опція',
    ariaAdvancedFilterBuilderValueP: 'Значення',
    ariaAdvancedFilterBuilderJoinOperator: "Оператор з'єднання",
    ariaAdvancedFilterInput: 'Ввід розширеного фільтру',
    ariaChecked: 'відмічено',
    ariaColumn: 'Колонка',
    ariaColumnGroup: 'Група колонок',
    ariaColumnFiltered: 'Колонка відфільтрована',
    ariaColumnSelectAll: 'Перемкнути видимість усіх стовпців',
    ariaDateFilterInput: 'Ввід фільтру за датою',
    ariaDefaultListName: 'Список',
    ariaFilterColumnsInput: 'Ввід колонок для фільтрування',
    ariaFilterFromValue: 'Фільтрувати від значення',
    ariaFilterInput: 'Ввід фільтру',
    ariaFilterList: 'Список фільтрів',
    ariaFilterToValue: 'Фільтрувати до значення',
    ariaFilterValue: 'Значення фільтру',
    ariaFilterMenuOpen: 'Відкрити меню фільтру',
    ariaFilteringOperator: 'Оператор фільтрування',
    ariaHidden: 'приховано',
    ariaIndeterminate: 'невизначено',
    ariaInputEditor: 'Редактор вводу',
    ariaMenuColumn: 'Натисніть ALT DOWN для відкриття меню колонок',
    ariaFilterColumn: 'Натисніть CTRL ENTER для відкриття фільтру',
    ariaRowDeselect: 'Натисніть SPACE для зняття виділення з цього рядка',
    ariaHeaderSelection: 'Стовпець з вибором заголовка',
    ariaSelectAllCells: 'Натисніть пробіл, щоб вибрати всі комірки',
    ariaRowSelectAll: 'Натисніть Space для перемикання вибору всіх рядків',
    ariaRowToggleSelection: 'Натисніть Space для перемикання вибору рядка',
    ariaRowSelect: 'Натисніть SPACE для вибору цього рядка',
    ariaRowSelectionDisabled: 'Вибір рядка відключено для цього рядка',
    ariaSearch: 'Пошук',
    ariaSortableColumn: 'Натисніть ENTER для сортування',
    ariaToggleVisibility: 'Натисніть SPACE для перемикання видимості',
    ariaToggleCellValue: 'Натисніть SPACE для перемикання значення комірки',
    ariaUnchecked: 'не відмічено',
    ariaVisible: 'видимо',
    ariaSearchFilterValues: 'Пошук значень фільтру',
    ariaPageSizeSelectorLabel: 'Розмір сторінки',
    ariaChartMenuClose: 'Закрити меню редагування діаграми',
    ariaChartSelected: 'Вибрано',
    ariaSkeletonCellLoadingFailed: 'Помилка завантаження рядка',
    ariaSkeletonCellLoading: 'Дані рядка завантажуються',
    ariaDeferSkeletonCellLoading: 'Завантажується комірка',

    // ARIA for Batch Edit
    ariaPendingChange: 'Зміна в очікуванні',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Групи рядків',
    ariaValuesDropZonePanelLabel: 'Значення',
    ariaPivotDropZonePanelLabel: 'Мітки стовпців',
    ariaDropZoneColumnComponentDescription: 'Натисніть DELETE, щоб видалити',
    ariaDropZoneColumnValueItemDescription: 'Натисніть ENTER, щоб змінити тип агрегації',
    ariaDropZoneColumnGroupItemDescription: 'Натисніть ENTER, щоб сортувати',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' з ',
    ariaDropZoneColumnComponentSortAscending: 'спадання',
    ariaDropZoneColumnComponentSortDescending: 'зростання',
    ariaLabelDialog: 'Діалог',
    ariaLabelColumnMenu: 'Меню стовпця',
    ariaLabelColumnFilter: 'Фільтр стовпця',
    ariaLabelSelectField: 'Виберіть поле',

    // Cell Editor
    ariaLabelCellEditor: 'Редактор комірки',
    ariaValidationErrorPrefix: 'Валідація редактора комірки',
    ariaLabelLoadingContextMenu: 'Завантаження контекстного меню',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Поле вибору',
    ariaLabelRichSelectToggleSelection: 'Натисніть ПРОПУСК для переключення вибору',
    ariaLabelRichSelectDeselectAllItems: 'Натисніть DELETE для скасування вибору всіх елементів',
    ariaLabelRichSelectDeleteSelection: 'Натисніть DELETE для скасування вибору елемента',
    ariaLabelTooltip: 'Підказка',
    ariaLabelContextMenu: 'Контекстне меню',
    ariaLabelSubMenu: 'Підменю',
    ariaLabelAggregationFunction: 'Функція агрегації',
    ariaLabelAdvancedFilterAutocomplete: 'Автозаповнення розширеного фільтра',
    ariaLabelAdvancedFilterBuilderAddField: 'Додати поле в розширений фільтр',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Вибір колонки в розширеному фільтрі',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Вибрати опцію в розширеному фільтрі',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Вибір оператора приєднання в розширеному фільтрі',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Список колонок',
    ariaFilterPanelList: 'Список фільтрів',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Додати поле фільтра',
    ariaLabelFilterCardDelete: 'Видалити фільтр',
    ariaLabelFilterCardHasEdits: 'Є зміни',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ' ',
    decimalSeparator: ',',

    // Data types
    true: 'Правда',
    false: 'Неправда',
    invalidDate: 'Невірна дата',
    invalidNumber: 'Невірний номер',
    january: 'Січень',
    february: 'Лютий',
    march: 'Березень',
    april: 'Квітень',
    may: 'Травень',
    june: 'Червень',
    july: 'Липень',
    august: 'Серпень',
    september: 'Вересень',
    october: 'Жовтень',
    november: 'Листопад',
    december: 'Грудень',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'ДД/ММ/РРРР',
    timeFormatSlashesMMDDYYYY: 'ММ/ДД/РРРР',
    timeFormatSlashesDDMMYY: 'ДД/ММ/РР',
    timeFormatSlashesMMDDYY: 'ММ/ДД/РР',
    timeFormatDotsDDMYY: 'ДД.М.РР',
    timeFormatDotsMDDYY: 'М.ДД.РР',
    timeFormatDashesYYYYMMDD: 'РРРР-ММ-ДД',
    timeFormatSpacesDDMMMMYYYY: 'ДД ММММ РРРР',
    timeFormatHHMMSS: 'ГГ:ХХ:СС',
    timeFormatHHMMSSAmPm: 'ГГ:ХХ:СС ДП/ПП',
};
