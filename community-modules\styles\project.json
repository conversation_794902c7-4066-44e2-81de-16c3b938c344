{"name": "@ag-grid-community/styles", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "community-modules/styles", "projectType": "library", "targets": {"build": {"executor": "nx:noop", "dependsOn": ["build:styles"], "inputs": [], "outputs": []}, "build:styles": {"outputs": ["{projectRoot}/ag-grid.css", "{projectRoot}/ag-grid-no-native-widgets.css", "{projectRoot}/ag-theme-alpine.css", "{projectRoot}/ag-theme-alpine-no-font.css", "{projectRoot}/ag-theme-balham.css", "{projectRoot}/ag-theme-balham-no-font.css", "{projectRoot}/ag-theme-material.css", "{projectRoot}/ag-theme-material-no-font.css", "{projectRoot}/ag-theme-quartz.css", "{projectRoot}/ag-theme-quartz-no-font.css", "{projectRoot}/agGridAlpineFont.css", "{projectRoot}/agGridBalhamFont.css", "{projectRoot}/agGridClassicFont.css", "{projectRoot}/agGridMaterialFont.css", "{projectRoot}/agGridQuartzFont.css", "{projectRoot}/ag-grid.min.css", "{projectRoot}/ag-grid-no-native-widgets.min.css", "{projectRoot}/ag-theme-alpine.min.css", "{projectRoot}/ag-theme-alpine-no-font.min.css", "{projectRoot}/ag-theme-balham.min.css", "{projectRoot}/ag-theme-balham-no-font.min.css", "{projectRoot}/ag-theme-material.min.css", "{projectRoot}/ag-theme-material-no-font.min.css", "{projectRoot}/ag-theme-quartz.min.css", "{projectRoot}/ag-theme-quartz-no-font.min.css", "{projectRoot}/agGridAlpineFont.min.css", "{projectRoot}/agGridBalhamFont.min.css", "{projectRoot}/agGridClassicFont.min.css", "{projectRoot}/agGridMaterialFont.min.css", "{projectRoot}/agGridQuartzFont.min.css", "{projectRoot}/_css-content.scss"], "inputs": ["{projectRoot}/src/**/*.scss", "{projectRoot}/*.scss", "{projectRoot}/*.js", "!{projectRoot}/_css-content.scss"], "options": {"cwd": "{projectRoot}", "command": "sass --silence-deprecation=mixed-decls --no-source-map --load-path src/internal src:."}, "cache": true}, "eslint": {"command": "eslint", "options": {"cwd": "{projectRoot}"}, "cache": true}, "stylelint": {"executor": "nx:run-commands", "dependsOn": ["build:styles"], "inputs": [], "outputs": [], "options": {"cwd": "{projectRoot}", "commands": ["stylelint --formatter unix --config stylelint-config-scss.js 'src/**/*.scss' '*.scss'", "stylelint --formatter unix --config stylelint-config-css.js '*.css'"]}, "cache": true}, "lint": {"executor": "nx:noop", "dependsOn": ["eslint", "stylelint"], "inputs": [], "outputs": []}, "test": {"executor": "nx:run-commands", "dependsOn": ["build"], "inputs": [], "outputs": [], "options": {"cwd": "{projectRoot}", "commands": ["sass test/test.scss"]}}}, "tags": ["module", "community", "styles", "no-sharding"]}