/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_NO = {
    // Set Filter
    selectAll: '(Velg alle)',
    selectAllSearchResults: '(Velg alle søkeresultater)',
    addCurrentSelectionToFilter: 'Legg nåværende valg til filter',
    searchOoo: 'Søk...',
    blanks: '(Tomme)',
    noMatches: 'Ingen treff',

    // Number Filter & Text Filter
    filterOoo: 'Filter...',
    equals: 'Er lik',
    notEqual: 'Er ikke lik',
    blank: 'Tom',
    notBlank: 'Ikke tom',
    empty: 'Velg en',

    // Number Filter
    lessThan: 'Mindre enn',
    greaterThan: 'Større enn',
    lessThanOrEqual: 'Mindre enn eller lik',
    greaterThanOrEqual: 'St<PERSON><PERSON> enn eller lik',
    inRange: '<PERSON>lom',
    inRangeStart: 'Fra',
    inRangeEnd: 'Til',

    // Text Filter
    contains: 'Inneholder',
    notContains: 'Inneholder ikke',
    startsWith: 'Begynner med',
    endsWith: 'Slutter med',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Før',
    after: 'Etter',

    // Filter Conditions
    andCondition: 'OG',
    orCondition: 'ELLER',

    // Filter Buttons
    applyFilter: 'Bruk',
    resetFilter: 'Tilbakestill',
    clearFilter: 'Tøm',
    cancelFilter: 'Avbryt',

    // Filter Titles
    textFilter: 'Tekstfilter',
    numberFilter: 'Tallfilter',
    dateFilter: 'Dato filter',
    setFilter: 'Sett filter',

    // Group Column Filter
    groupFilterSelect: 'Velg felt:',

    // New Filter Tool Panel
    filterSummaryInactive: 'er (Alle)',
    filterSummaryContains: 'inneholder',
    filterSummaryNotContains: 'inneholder ikke',
    filterSummaryTextEquals: 'er lik',
    filterSummaryTextNotEqual: 'er ikke lik',
    filterSummaryStartsWith: 'begynner med',
    filterSummaryEndsWith: 'slutter med',
    filterSummaryBlank: 'er tom',
    filterSummaryNotBlank: 'er ikke tom',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'mellom',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'er (Alle)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'er (${variable})',
    filterSummaryListLong: 'er (${variable}) og ${variable} flere',
    addFilterCard: 'Legg til filter',
    agTextColumnFilterDisplayName: 'Enkel filter',
    agNumberColumnFilterDisplayName: 'Enkel filter',
    agDateColumnFilterDisplayName: 'Enkel filter',
    agSetColumnFilterDisplayName: 'Utvalgfilter',
    agMultiColumnFilterDisplayName: 'Kombinasjonsfilter',
    addFilterPlaceholder: 'Søk kolonner...',

    // Advanced Filter
    advancedFilterContains: 'inneholder',
    advancedFilterNotContains: 'inneholder-ikke',
    advancedFilterTextEquals: 'er-lik',
    advancedFilterTextNotEqual: 'er-ikke-lik',
    advancedFilterStartsWith: 'begynner-med',
    advancedFilterEndsWith: 'slutter-med',
    advancedFilterBlank: 'er-tom',
    advancedFilterNotBlank: 'er-ikke-tom',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'er-sann',
    advancedFilterFalse: 'er-falsk',
    advancedFilterAnd: 'OG',
    advancedFilterOr: 'ELLER',
    advancedFilterApply: 'Bruk',
    advancedFilterBuilder: 'Bygger',
    advancedFilterValidationMissingColumn: 'Kolonne mangler',
    advancedFilterValidationMissingOption: 'Alternativ mangler',
    advancedFilterValidationMissingValue: 'Verdi mangler',
    advancedFilterValidationInvalidColumn: 'Kolonne ikke funnet',
    advancedFilterValidationInvalidOption: 'Alternativ ikke funnet',
    advancedFilterValidationMissingQuote: 'Verdi mangler en avsluttende anførselstegn',
    advancedFilterValidationNotANumber: 'Verdi er ikke et tall',
    advancedFilterValidationInvalidDate: 'Verdi er ikke en gyldig dato',
    advancedFilterValidationMissingCondition: 'Betingelse mangler',
    advancedFilterValidationJoinOperatorMismatch: 'Sammenkoblingsoperatorer innen en betingelse må være de samme',
    advancedFilterValidationInvalidJoinOperator: 'Sammenkoblingsoperator ikke funnet',
    advancedFilterValidationMissingEndBracket: 'Mangler avsluttende parentes',
    advancedFilterValidationExtraEndBracket: 'For mange avsluttende parenteser',
    advancedFilterValidationMessage: 'Uttrykket har en feil. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Uttrykket har en feil. ${variable} på slutten av uttrykket.',
    advancedFilterBuilderTitle: 'Avansert Filter',
    advancedFilterBuilderApply: 'Bruk',
    advancedFilterBuilderCancel: 'Avbryt',
    advancedFilterBuilderAddButtonTooltip: 'Legg til filter eller gruppe',
    advancedFilterBuilderRemoveButtonTooltip: 'Fjern',
    advancedFilterBuilderMoveUpButtonTooltip: 'Flytt opp',
    advancedFilterBuilderMoveDownButtonTooltip: 'Flytt ned',
    advancedFilterBuilderAddJoin: 'Legg til gruppe',
    advancedFilterBuilderAddCondition: 'Legg til filter',
    advancedFilterBuilderSelectColumn: 'Velg en kolonne',
    advancedFilterBuilderSelectOption: 'Velg et alternativ',
    advancedFilterBuilderEnterValue: 'Skriv inn en verdi...',
    advancedFilterBuilderValidationAlreadyApplied: 'Gjeldende filter allerede brukt.',
    advancedFilterBuilderValidationIncomplete: 'Ikke alle betingelser er komplette.',
    advancedFilterBuilderValidationSelectColumn: 'Må velge en kolonne.',
    advancedFilterBuilderValidationSelectOption: 'Må velge et alternativ.',
    advancedFilterBuilderValidationEnterValue: 'Må skrive inn en verdi.',

    // Editor Validation Errors
    minDateValidation: 'Dato må være etter ${variable}',
    maxDateValidation: 'Dato må være før ${variable}',
    maxLengthValidation: 'Må være ${variable} tegn eller færre.',
    minValueValidation: 'Må være større enn eller lik ${variable}',
    maxValueValidation: 'Må være mindre enn eller lik ${variable}',
    invalidSelectionValidation: 'Ugyldig valg.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Kolonner',
    filters: 'Filtre',

    // columns tool panel
    pivotMode: 'Pivot modus',
    groups: 'Radgrupper',
    rowGroupColumnsEmptyMessage: 'Dra hit for å sette radgrupper',
    values: 'Verdier',
    valueColumnsEmptyMessage: 'Dra hit for å aggregere',
    pivots: 'Kolonneetiketter',
    pivotColumnsEmptyMessage: 'Dra hit for å sette kolonneetiketter',

    // Header of the Default Group Column
    group: 'Gruppe',

    // Row Drag
    rowDragRow: 'rad',
    rowDragRows: 'rader',

    // Other
    loadingOoo: 'Laster...',
    loadingError: 'FEIL',
    noRowsToShow: 'Ingen rader å vise',
    enabled: 'Aktivert',

    // Menu
    pinColumn: 'Fest Kolonne',
    pinLeft: 'Fest til Venstre',
    pinRight: 'Fest til Høyre',
    noPin: 'Ingen Festing',
    valueAggregation: 'Verdi Aggregering',
    noAggregation: 'Ingen',
    autosizeThisColumn: 'Tilpass Denne Kolonnen',
    autosizeAllColumns: 'Tilpass Alle Kolonner',
    groupBy: 'Grupper Etter',
    ungroupBy: 'Fjern Gruppering Etter',
    ungroupAll: 'Fjern All Gruppering',
    addToValues: 'Legg til ${variable} til verdier',
    removeFromValues: 'Fjern ${variable} fra verdier',
    addToLabels: 'Legg til ${variable} til etiketter',
    removeFromLabels: 'Fjern ${variable} fra etiketter',
    resetColumns: 'Tilbakestill Kolonner',
    expandAll: 'Utvid Alle Rad Grupper',
    collapseAll: 'Lukk Alle Rad Grupper',
    copy: 'Kopier',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopier med Overskrifter',
    copyWithGroupHeaders: 'Kopier med Gruppeoverskrifter',
    cut: 'Klipp',
    paste: 'Lim inn',
    ctrlV: 'Ctrl+V',
    export: 'Eksporter',
    csvExport: 'CSV Eksport',
    excelExport: 'Excel Eksport',
    columnFilter: 'Kolonnefilter',
    columnChooser: 'Velg Kolonner',
    chooseColumns: 'Velg kolonner',
    sortAscending: 'Sorter Stigende',
    sortDescending: 'Sorter Synkende',
    sortUnSort: 'Fjern Sortering',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Sum',
    first: 'Første',
    last: 'Siste',
    min: 'Min',
    max: 'Maks',
    none: 'Ingen',
    count: 'Antall',
    avg: 'Gjennomsnitt',
    filteredRows: 'Filtrert',
    selectedRows: 'Valgt',
    totalRows: 'Totale rader',
    totalAndFilteredRows: 'Rader',
    more: 'Mer',
    to: 'til',
    of: 'av',
    page: 'Side',
    pageLastRowUnknown: '?',
    nextPage: 'Neste side',
    lastPage: 'Siste side',
    firstPage: 'Første side',
    previousPage: 'Forrige side',
    pageSizeSelectorLabel: 'Sidestørrelse:',
    footerTotal: 'Total',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Rull ${variable} inn i visningen',

    // Pivoting
    pivotColumnGroupTotals: 'Total',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivotskjema og pivotmodus',
    pivotChart: 'Pivotskjema',
    chartRange: 'Skjemautvalg',
    columnChart: 'Kolonne',
    groupedColumn: 'Gruppert',
    stackedColumn: 'Stablet',
    normalizedColumn: '100% stablet',
    barChart: 'Søyle',
    groupedBar: 'Gruppert',
    stackedBar: 'Stablet',
    normalizedBar: '100% stablet',
    pieChart: 'Kakestykke',
    pie: 'Kakestykke',
    donut: 'Smultring',
    lineChart: 'Linje',
    stackedLine: 'Stablet',
    normalizedLine: '100% Stablet',
    xyChart: 'X Y (Spredning)',
    scatter: 'Spredning',
    bubble: 'Boble',
    areaChart: 'Område',
    area: 'Område',
    stackedArea: 'Stablet',
    normalizedArea: '100% stablet',
    histogramChart: 'Histogram',
    polarChart: 'Polar',
    radarLine: 'Radarlinje',
    radarArea: 'Radarområde',
    nightingale: 'Nightingale',
    radialColumn: 'Radial kolonne',
    radialBar: 'Radial søyle',
    statisticalChart: 'Statistisk',
    boxPlot: 'Boksdiagram',
    rangeBar: 'Rekke søyle',
    rangeArea: 'Rekke område',
    hierarchicalChart: 'Hierarkisk',
    treemap: 'Treemap',
    sunburst: 'Solstråle',
    specializedChart: 'Spesialisert',
    waterfall: 'Vannfall',
    heatmap: 'Varmekart',
    combinationChart: 'Kombinasjon',
    columnLineCombo: 'Kolonne og linje',
    AreaColumnCombo: 'Område og kolonne',

    // Charts
    pivotChartTitle: 'Pivoteringsdiagram',
    rangeChartTitle: 'Områdekart',
    settings: 'Diagram',
    data: 'Oppsett',
    format: 'Tilpass',
    categories: 'Kategorier',
    defaultCategory: '(Ingen)',
    series: 'Serier',
    switchCategorySeries: 'Bytt kategori / serie',
    categoryValues: 'Kategoriverdier',
    seriesLabels: 'Seriemerker',
    aggregate: 'Aggreger',
    xyValues: 'X Y Verdier',
    paired: 'Parret modus',
    axis: 'Akse',
    xAxis: 'Horisontal akse',
    yAxis: 'Vertikal akse',
    polarAxis: 'Polar akse',
    radiusAxis: 'Radius akse',
    navigator: 'Navigator',
    zoom: 'Zoom',
    animation: 'Animasjon',
    crosshair: 'Hårkors',
    color: 'Farge',
    thickness: 'Tykkelse',
    preferredLength: 'Foretrukket lengde',
    xType: 'X Type',
    axisType: 'Aksetype',
    automatic: 'Automatisk',
    category: 'Kategori',
    number: 'Nummer',
    time: 'Tid',
    timeFormat: 'Tidsformat',
    autoRotate: 'Auto rotér',
    labelRotation: 'Rotasjon',
    circle: 'Sirkel',
    polygon: 'Polygon',
    square: 'Firkant',
    cross: 'Kryss',
    diamond: 'Diamant',
    plus: 'Pluss',
    triangle: 'Trekant',
    heart: 'Hjerte',
    orientation: 'Orientering',
    fixed: 'Fast',
    parallel: 'Parallell',
    perpendicular: 'Vinkelrett',
    radiusAxisPosition: 'Posisjon',
    ticks: 'Merker',
    gridLines: 'Rutenett',
    width: 'Bredde',
    height: 'Høyde',
    length: 'Lengde',
    padding: 'Polstring',
    spacing: 'Avstand',
    chartStyle: 'Diagramstil',
    title: 'Tittel',
    chartTitles: 'Titler',
    chartTitle: 'Diagramtittel',
    chartSubtitle: 'Undertekst',
    horizontalAxisTitle: 'Horisontal aksetittel',
    verticalAxisTitle: 'Vertikal aksetittel',
    polarAxisTitle: 'Polar aksetittel',
    titlePlaceholder: 'Diagramtittel',
    background: 'Bakgrunn',
    font: 'Skrift',
    weight: 'Vekt',
    top: 'Topp',
    right: 'Høyre',
    bottom: 'Bunn',
    left: 'Venstre',
    labels: 'Etiketter',
    calloutLabels: 'Utkallingsetiketter',
    sectorLabels: 'Sektoretiketter',
    positionRatio: 'Posisjonsforhold',
    size: 'Størrelse',
    shape: 'Form',
    minSize: 'Minimumsstørrelse',
    maxSize: 'Maksimumsstørrelse',
    legend: 'Forklaring',
    position: 'Posisjon',
    markerSize: 'Markørstørrelse',
    markerStroke: 'Markørslag',
    markerPadding: 'Markørpolstring',
    itemSpacing: 'Gjenstandsavstand',
    itemPaddingX: 'Gjenstand polstring X',
    itemPaddingY: 'Gjenstand polstring Y',
    layoutHorizontalSpacing: 'Horisontal avstand',
    layoutVerticalSpacing: 'Vertikal avstand',
    strokeWidth: 'Slagbredde',
    offset: 'Forskyvning',
    offsets: 'Forskyvninger',
    tooltips: 'Verktøytips',
    callout: 'Utkalling',
    markers: 'Markører',
    shadow: 'Skygge',
    blur: 'Uklart',
    xOffset: 'X forskyvning',
    yOffset: 'Y forskyvning',
    lineWidth: 'Linje bredde',
    lineDash: 'Linjemønster',
    lineDashOffset: 'Mønster forskyvning',
    scrollingZoom: 'Rulling',
    scrollingStep: 'Rulletrinn',
    selectingZoom: 'Velger',
    durationMillis: 'Varighet (ms)',
    crosshairLabel: 'Etikett',
    crosshairSnap: 'Fest til node',
    normal: 'Normal',
    bold: 'Fet',
    italic: 'Kursiv',
    boldItalic: 'Fet kursiv',
    predefined: 'Forhåndsdefinert',
    fillOpacity: 'Fyll opasitet',
    strokeColor: 'Linjefarge',
    strokeOpacity: 'Linje opasitet',
    miniChart: 'Mini-diagram',
    histogramBinCount: 'BinkCount',
    connectorLine: 'Koblingslinje',
    seriesItems: 'Serieobjekter',
    seriesItemType: 'Objekttype',
    seriesItemPositive: 'Positiv',
    seriesItemNegative: 'Negativ',
    seriesItemLabels: 'Objektetiketter',
    columnGroup: 'Kolonne',
    barGroup: 'Søyle',
    pieGroup: 'Kake',
    lineGroup: 'Linje',
    scatterGroup: 'X Y (Punkter)',
    areaGroup: 'Område',
    polarGroup: 'Polar',
    statisticalGroup: 'Statistisk',
    hierarchicalGroup: 'Hierarkisk',
    specializedGroup: 'Spesialisert',
    combinationGroup: 'Kombinasjon',
    groupedColumnTooltip: 'Gruppert',
    stackedColumnTooltip: 'Stablet',
    normalizedColumnTooltip: '100% Stablet',
    groupedBarTooltip: 'Gruppert',
    stackedBarTooltip: 'Stablet',
    normalizedBarTooltip: '100% Stablet',
    pieTooltip: 'Kake',
    donutTooltip: 'Smultring',
    lineTooltip: 'Linje',
    stackedLineTooltip: 'Stablet',
    normalizedLineTooltip: '100% Stablet',
    groupedAreaTooltip: 'Område',
    stackedAreaTooltip: 'Stablet',
    normalizedAreaTooltip: '100% Stablet',
    scatterTooltip: 'Punkter',
    bubbleTooltip: 'Boble',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Radiell kolonne',
    radialBarTooltip: 'Radiell søyle',
    radarLineTooltip: 'Radard linje',
    radarAreaTooltip: 'Radard område',
    nightingaleTooltip: 'Nattergal',
    rangeBarTooltip: 'Områdesøyle',
    rangeAreaTooltip: 'Område',
    boxPlotTooltip: 'Boksplott',
    treemapTooltip: 'Trekart',
    sunburstTooltip: 'Solglimt',
    waterfallTooltip: 'Fossefall',
    heatmapTooltip: 'Varmekart',
    columnLineComboTooltip: 'Kolonne & Linje',
    areaColumnComboTooltip: 'Område & Kolonne',
    customComboTooltip: 'Tilpasset kombinert',
    innerRadius: 'Indre radius',
    startAngle: 'Startvinkel',
    endAngle: 'Sluttvinkel',
    reverseDirection: 'Omvendt retning',
    groupPadding: 'Gruppolstring',
    seriesPadding: 'Seriepolstring',
    tile: 'Flis',
    whisker: 'Hår',
    cap: 'Kappe',
    capLengthRatio: 'Lengdeforhold',
    labelPlacement: 'Plassering',
    inside: 'Innsiden',
    outside: 'Utsiden',
    noDataToChart: 'Ingen data tilgjengelig for å lage diagram.',
    pivotChartRequiresPivotMode: 'Pivoteringsdiagram krever Pivoteringsmodus aktivert.',
    chartSettingsToolbarTooltip: 'Meny',
    chartLinkToolbarTooltip: 'Knytet til rutenettet',
    chartUnlinkToolbarTooltip: 'Frakoblet fra rutenettet',
    chartDownloadToolbarTooltip: 'Last ned diagram',
    chartMenuToolbarTooltip: 'Meny',
    chartEdit: 'Rediger diagram',
    chartAdvancedSettings: 'Avanserte innstillinger',
    chartLink: 'Link til rutenettet',
    chartUnlink: 'Frakoble fra rutenettet',
    chartDownload: 'Last ned diagram',
    histogramFrequency: 'Frekvens',
    seriesChartType: 'Seriediagramtype',
    seriesType: 'Serietype',
    secondaryAxis: 'Sekundær akse',
    seriesAdd: 'Legg til en serie',
    categoryAdd: 'Legg til en kategori',
    bar: 'Søyle',
    column: 'Kolonne',
    histogram: 'Histogram',
    advancedSettings: 'Avanserte innstillinger',
    direction: 'Retning',
    horizontal: 'Horisontal',
    vertical: 'Vertikal',
    seriesGroupType: 'Gruppetype',
    groupedSeriesGroupType: 'Gruppert',
    stackedSeriesGroupType: 'Stablet',
    normalizedSeriesGroupType: '100% Stablet',
    legendEnabled: 'Aktivert',
    invalidColor: 'Fargeverdi er ugyldig',
    groupedColumnFull: 'Gruppert kolonne',
    stackedColumnFull: 'Stablet kolonne',
    normalizedColumnFull: '100% stablet kolonne',
    groupedBarFull: 'Gruppert søyle',
    stackedBarFull: 'Stablet søyle',
    normalizedBarFull: '100% stablet søyle',
    stackedAreaFull: 'Stablet område',
    normalizedAreaFull: '100% stablet område',
    customCombo: 'Tilpasset kombinasjon',
    funnel: 'Trakt',
    coneFunnel: 'Kontrakt',
    pyramid: 'Pyramide',
    funnelGroup: 'Trakt',
    funnelTooltip: 'Trakt',
    coneFunnelTooltip: 'Kontrakt',
    pyramidTooltip: 'Pyramide',
    dropOff: 'Bortfall',
    stageLabels: 'Fasetekster',
    reverse: 'Omvendt',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Nivå ${variable}. Trykk ENTER for å redigere.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Nivå ${variable}. ${variable} Trykk ENTER for å redigere.',
    ariaAdvancedFilterBuilderList: 'Avansert filterbyggerliste',
    ariaAdvancedFilterBuilderFilterItem: 'Filterbetingelse',
    ariaAdvancedFilterBuilderGroupItem: 'Filtergruppe',
    ariaAdvancedFilterBuilderColumn: 'Kolonne',
    ariaAdvancedFilterBuilderOption: 'Alternativ',
    ariaAdvancedFilterBuilderValueP: 'Verdi',
    ariaAdvancedFilterBuilderJoinOperator: 'Koblingsoperator',
    ariaAdvancedFilterInput: 'Inndata for avansert filter',
    ariaChecked: 'avmerket',
    ariaColumn: 'Kolonne',
    ariaColumnGroup: 'Kolonnegruppe',
    ariaColumnFiltered: 'Kolonne filtrert',
    ariaColumnSelectAll: 'Bytt synlighet for alle kolonner',
    ariaDateFilterInput: 'Inndata for datofilter',
    ariaDefaultListName: 'Liste',
    ariaFilterColumnsInput: 'Filtrer kolonner inndata',
    ariaFilterFromValue: 'Filtrer fra verdi',
    ariaFilterInput: 'Filterinndata',
    ariaFilterList: 'Filterliste',
    ariaFilterToValue: 'Filtrer til verdi',
    ariaFilterValue: 'Filterverdi',
    ariaFilterMenuOpen: 'Åpne filtermeny',
    ariaFilteringOperator: 'Filtreringsoperator',
    ariaHidden: 'skjult',
    ariaIndeterminate: 'ubestemt',
    ariaInputEditor: 'Inndataredigerer',
    ariaMenuColumn: 'Trykk ALT NED for å åpne kolonnemeny',
    ariaFilterColumn: 'Trykk CTRL ENTER for å åpne filter',
    ariaRowDeselect: 'Trykk SPACE for å fjerne markeringen av denne raden',
    ariaHeaderSelection: 'Kolonne med overskriftsvalg',
    ariaSelectAllCells: 'Trykk på mellomromstasten for å velge alle celler',
    ariaRowSelectAll: 'Trykk SPACE for å veksle alle radvalgene',
    ariaRowToggleSelection: 'Trykk SPACE for å veksle radvalg',
    ariaRowSelect: 'Trykk SPACE for å velge denne raden',
    ariaRowSelectionDisabled: 'Radvalg er deaktivert for denne raden',
    ariaSearch: 'Søk',
    ariaSortableColumn: 'Trykk ENTER for å sortere',
    ariaToggleVisibility: 'Trykk SPACE for å veksle synlighet',
    ariaToggleCellValue: 'Trykk SPACE for å veksle celleverdi',
    ariaUnchecked: 'uavmerket',
    ariaVisible: 'synlig',
    ariaSearchFilterValues: 'Søk filterverdier',
    ariaPageSizeSelectorLabel: 'Sidestørrelse',
    ariaChartMenuClose: 'Lukk diagramredigeringsmeny',
    ariaChartSelected: 'Valgt',
    ariaSkeletonCellLoadingFailed: 'Raden kunne ikke lastes',
    ariaSkeletonCellLoading: 'Raddata lastes',
    ariaDeferSkeletonCellLoading: 'Cell laster',

    // ARIA for Batch Edit
    ariaPendingChange: 'Ventende endring',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Radgrupper',
    ariaValuesDropZonePanelLabel: 'Verdier',
    ariaPivotDropZonePanelLabel: 'Kolonneetiketter',
    ariaDropZoneColumnComponentDescription: 'Trykk DELETE for å fjerne',
    ariaDropZoneColumnValueItemDescription: 'Trykk ENTER for å endre aggregeringstype',
    ariaDropZoneColumnGroupItemDescription: 'Trykk ENTER for å sortere',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' av ',
    ariaDropZoneColumnComponentSortAscending: 'stigende',
    ariaDropZoneColumnComponentSortDescending: 'synkende',
    ariaLabelDialog: 'Dialog',
    ariaLabelColumnMenu: 'Kolonnemeny',
    ariaLabelColumnFilter: 'Kolonnefilter',
    ariaLabelSelectField: 'Velg Felt',

    // Cell Editor
    ariaLabelCellEditor: 'Celle redigerer',
    ariaValidationErrorPrefix: 'Celle redigerer validering',
    ariaLabelLoadingContextMenu: 'Laster inn kontekstmeny',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Rik velg felt',
    ariaLabelRichSelectToggleSelection: 'Trykk SPACE for å veksle valg',
    ariaLabelRichSelectDeselectAllItems: 'Trykk DELETE for å fjerne alle valg',
    ariaLabelRichSelectDeleteSelection: 'Trykk DELETE for å fjerne valget',
    ariaLabelTooltip: 'Verktøytips',
    ariaLabelContextMenu: 'Kontekstmeny',
    ariaLabelSubMenu: 'Undermeny',
    ariaLabelAggregationFunction: 'Aggregeringsfunksjon',
    ariaLabelAdvancedFilterAutocomplete: 'Avansert filter autfullstendig',
    ariaLabelAdvancedFilterBuilderAddField: 'Byggeverktøy for avansert filter legg til felt',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Byggeverktøy for avansert filter kolonnevelger felt',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Byggeverktøy for avansert filter alternativvelger felt',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Byggeverktøy for avansert filter sammenføyingsoperator velger felt',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Kolonneliste',
    ariaFilterPanelList: 'Filterliste',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Legg til filterfelt',
    ariaLabelFilterCardDelete: 'Slett filter',
    ariaLabelFilterCardHasEdits: 'Har endringer',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Sant',
    false: 'Falsk',
    invalidDate: 'Ugyldig dato',
    invalidNumber: 'Ugyldig nummer',
    january: 'Januar',
    february: 'Februar',
    march: 'Mars',
    april: 'April',
    may: 'Mai',
    june: 'Juni',
    july: 'Juli',
    august: 'August',
    september: 'September',
    october: 'Oktober',
    november: 'November',
    december: 'Desember',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'TT:MM:SS',
    timeFormatHHMMSSAmPm: 'TT:MM:SS AM/PM',
};
