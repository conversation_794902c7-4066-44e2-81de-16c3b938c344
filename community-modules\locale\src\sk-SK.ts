/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_SK = {
    // Set Filter
    selectAll: '(<PERSON>ybrať všetko)',
    selectAllSearchResults: '(Vybrať všetky výsledky vyhľadávania)',
    addCurrentSelectionToFilter: 'Pridať aktuálny výber do filtra',
    searchOoo: 'Vyhľadávanie...',
    blanks: '(Prázdne)',
    noMatches: '<PERSON>iadne zhody',

    // Number Filter & Text Filter
    filterOoo: 'Filter...',
    equals: 'Rovná sa',
    notEqual: 'Nerovná sa',
    blank: 'Prázdny',
    notBlank: 'Nie je prázdny',
    empty: 'Vyberte jednu možnosť',

    // Number Filter
    lessThan: '<PERSON>ej ako',
    greaterThan: '<PERSON>c ako',
    lessThanOrEqual: 'Menej alebo rovné',
    greaterThanOrEqual: 'Viac alebo rovné',
    inRange: 'Medzi',
    inRangeStart: 'Od',
    inRangeEnd: 'Do',

    // Text Filter
    contains: 'Obsahuje',
    notContains: 'Neobsahuje',
    startsWith: 'Začína s',
    endsWith: 'Končí s',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Pred',
    after: 'Po',

    // Filter Conditions
    andCondition: 'A',
    orCondition: 'ALEBO',

    // Filter Buttons
    applyFilter: 'Použiť',
    resetFilter: 'Resetovať',
    clearFilter: 'Vyčistiť',
    cancelFilter: 'Zrušiť',

    // Filter Titles
    textFilter: 'Textový filter',
    numberFilter: 'Číselný filter',
    dateFilter: 'Dátumový filter',
    setFilter: 'Nastavený filter',

    // Group Column Filter
    groupFilterSelect: 'Vyberte pole:',

    // New Filter Tool Panel
    filterSummaryInactive: 'je (Všetko)',
    filterSummaryContains: 'obsahuje',
    filterSummaryNotContains: 'neobsahuje',
    filterSummaryTextEquals: 'rovná sa',
    filterSummaryTextNotEqual: 'nerovná sa',
    filterSummaryStartsWith: 'začína s',
    filterSummaryEndsWith: 'končí s',
    filterSummaryBlank: 'je prázdne',
    filterSummaryNotBlank: 'nie je prázdne',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'medzi',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'je (Všetko)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'je (${variable})',
    filterSummaryListLong: 'je (${variable}) a ${variable} viac',
    addFilterCard: 'Pridať filter',
    agTextColumnFilterDisplayName: 'Jednoduchý filter',
    agNumberColumnFilterDisplayName: 'Jednoduchý filter',
    agDateColumnFilterDisplayName: 'Jednoduchý filter',
    agSetColumnFilterDisplayName: 'Filter výberu',
    agMultiColumnFilterDisplayName: 'Kombinovaný filter',
    addFilterPlaceholder: 'Vyhľadať stĺpce...',

    // Advanced Filter
    advancedFilterContains: 'obsahuje',
    advancedFilterNotContains: 'neobsahuje',
    advancedFilterTextEquals: 'rovná sa',
    advancedFilterTextNotEqual: 'nerovná sa',
    advancedFilterStartsWith: 'začína na',
    advancedFilterEndsWith: 'končí na',
    advancedFilterBlank: 'je prázdne',
    advancedFilterNotBlank: 'nie je prázdne',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'je pravdivé',
    advancedFilterFalse: 'je nepravdivé',
    advancedFilterAnd: 'A',
    advancedFilterOr: 'ALEBO',
    advancedFilterApply: 'Použiť',
    advancedFilterBuilder: 'Tvorca',
    advancedFilterValidationMissingColumn: 'Chýba stĺpec',
    advancedFilterValidationMissingOption: 'Chýba možnosť',
    advancedFilterValidationMissingValue: 'Chýba hodnota',
    advancedFilterValidationInvalidColumn: 'Stĺpec sa nenašiel',
    advancedFilterValidationInvalidOption: 'Možnosť sa nenašla',
    advancedFilterValidationMissingQuote: 'Hodnota chýba koncová úvodzovka',
    advancedFilterValidationNotANumber: 'Hodnota nie je číslo',
    advancedFilterValidationInvalidDate: 'Hodnota nie je platný dátum',
    advancedFilterValidationMissingCondition: 'Chýba podmienka',
    advancedFilterValidationJoinOperatorMismatch: 'Spojovacie operátory v rámci podmienky musia byť rovnaké',
    advancedFilterValidationInvalidJoinOperator: 'Spojovací operátor sa nenašiel',
    advancedFilterValidationMissingEndBracket: 'Chýba koncová zátvorka',
    advancedFilterValidationExtraEndBracket: 'Príliš veľa koncových zátvoriek',
    advancedFilterValidationMessage: 'Výraz obsahuje chybu. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Výraz obsahuje chybu. ${variable} na konci výrazu.',
    advancedFilterBuilderTitle: 'Pokročilý filter',
    advancedFilterBuilderApply: 'Použiť',
    advancedFilterBuilderCancel: 'Zrušiť',
    advancedFilterBuilderAddButtonTooltip: 'Pridať filter alebo skupinu',
    advancedFilterBuilderRemoveButtonTooltip: 'Odstrániť',
    advancedFilterBuilderMoveUpButtonTooltip: 'Presunúť hore',
    advancedFilterBuilderMoveDownButtonTooltip: 'Presunúť dole',
    advancedFilterBuilderAddJoin: 'Pridať skupinu',
    advancedFilterBuilderAddCondition: 'Pridať filter',
    advancedFilterBuilderSelectColumn: 'Vybrať stĺpec',
    advancedFilterBuilderSelectOption: 'Vybrať možnosť',
    advancedFilterBuilderEnterValue: 'Zadajte hodnotu...',
    advancedFilterBuilderValidationAlreadyApplied: 'Aktuálny filter je už použitý.',
    advancedFilterBuilderValidationIncomplete: 'Nie všetky podmienky sú kompletné.',
    advancedFilterBuilderValidationSelectColumn: 'Musí byť vybraný stĺpec.',
    advancedFilterBuilderValidationSelectOption: 'Musí byť vybraná možnosť.',
    advancedFilterBuilderValidationEnterValue: 'Musí byť zadaná hodnota.',

    // Editor Validation Errors
    minDateValidation: 'Dátum musí byť po ${variable}',
    maxDateValidation: 'Dátum musí byť pred ${variable}',
    maxLengthValidation: 'Musí mať ${variable} znakov alebo menej.',
    minValueValidation: 'Musí byť väčšie alebo rovné ${variable}',
    maxValueValidation: 'Musí byť menšie alebo rovné ${variable}',
    invalidSelectionValidation: 'Neplatný výber.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Stĺpce',
    filters: 'Filtre',

    // columns tool panel
    pivotMode: 'Režim Pivot',
    groups: 'Skupiny riadkov',
    rowGroupColumnsEmptyMessage: 'Presuňte sem pre nastavenie skupinových riadkov',
    values: 'Hodnoty',
    valueColumnsEmptyMessage: 'Presuňte sem pre agregáciu',
    pivots: 'Označenia stĺpcov',
    pivotColumnsEmptyMessage: 'Presuňte sem pre nastavenie označení stĺpcov',

    // Header of the Default Group Column
    group: 'Skupina',

    // Row Drag
    rowDragRow: 'riadok',
    rowDragRows: 'riadky',

    // Other
    loadingOoo: 'Načítavam...',
    loadingError: 'CHYBA',
    noRowsToShow: 'Žiadne riadky na zobrazenie',
    enabled: 'Povolené',

    // Menu
    pinColumn: 'Pripnúť stĺpec',
    pinLeft: 'Pripnúť vľavo',
    pinRight: 'Pripnúť vpravo',
    noPin: 'Neprepínať',
    valueAggregation: 'Súhrn hodnôt',
    noAggregation: 'Žiadny',
    autosizeThisColumn: 'Automaticky nastaviť veľkosť tohto stĺpca',
    autosizeAllColumns: 'Automaticky nastaviť veľkosť všetkých stĺpcov',
    groupBy: 'Zoskupiť podľa',
    ungroupBy: 'Odskupiť podľa',
    ungroupAll: 'Odskupiť všetko',
    addToValues: 'Pridať ${variable} do hodnôt',
    removeFromValues: 'Odstrániť ${variable} z hodnôt',
    addToLabels: 'Pridať ${variable} do označení',
    removeFromLabels: 'Odstrániť ${variable} z označení',
    resetColumns: 'Resetovať stĺpce',
    expandAll: 'Rozbaliť všetky skupiny riadkov',
    collapseAll: 'Zbaliť všetky skupiny riadkov',
    copy: 'Kopírovať',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopírovať s hlavičkami',
    copyWithGroupHeaders: 'Kopírovať s hlavičkami skupín',
    cut: 'Vystrihnúť',
    paste: 'Vložiť',
    ctrlV: 'Ctrl+V',
    export: 'Export',
    csvExport: 'Export do CSV',
    excelExport: 'Export do Excelu',
    columnFilter: 'Filter stĺpca',
    columnChooser: 'Výber stĺpcov',
    chooseColumns: 'Vyberte stĺpce',
    sortAscending: 'Zoradiť vzostupne',
    sortDescending: 'Zoradiť zostupne',
    sortUnSort: 'Zrušiť zoradenie',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Súčet',
    first: 'Prvý',
    last: 'Posledný',
    min: 'Minimálny',
    max: 'Maximálny',
    none: 'Žiadny',
    count: 'Počet',
    avg: 'Priemer',
    filteredRows: 'Filtrované',
    selectedRows: 'Vybrané',
    totalRows: 'Celkovo riadkov',
    totalAndFilteredRows: 'Riadky',
    more: 'Viac',
    to: 'do',
    of: 'z',
    page: 'Strana',
    pageLastRowUnknown: '?',
    nextPage: 'Ďalšia strana',
    lastPage: 'Posledná strana',
    firstPage: 'Prvá strana',
    previousPage: 'Predchádzajúca strana',
    pageSizeSelectorLabel: 'Veľkosť strany:',
    footerTotal: 'Celkom',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Posunúť ${variable} do zobrazenia',

    // Pivoting
    pivotColumnGroupTotals: 'Celkom',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Kontingenčný graf a kontingenčný režim',
    pivotChart: 'Kontingenčný graf',
    chartRange: 'Rozsah grafu',
    columnChart: 'Stĺpcový graf',
    groupedColumn: 'Zoskupené',
    stackedColumn: 'Skladané',
    normalizedColumn: '100% skladené',
    barChart: 'Pruhový graf',
    groupedBar: 'Zoskupené',
    stackedBar: 'Skladané',
    normalizedBar: '100% skladené',
    pieChart: 'Koláčový graf',
    pie: 'Koláč',
    donut: 'Donut',
    lineChart: 'Čiara',
    stackedLine: 'Skladaný',
    normalizedLine: '100% Skladaný',
    xyChart: 'X Y (Rozptylový)',
    scatter: 'Rozptyl',
    bubble: 'Bublina',
    areaChart: 'Oblasťový graf',
    area: 'Oblasť',
    stackedArea: 'Skladané',
    normalizedArea: '100% skladené',
    histogramChart: 'Histogram',
    polarChart: 'Polárny graf',
    radarLine: 'Radarová čiara',
    radarArea: 'Radarová oblasť',
    nightingale: 'Nightingale',
    radialColumn: 'Radiálny stĺpcový',
    radialBar: 'Radiálny pruhový',
    statisticalChart: 'Štatistický graf',
    boxPlot: 'Krabicový graf',
    rangeBar: 'Pruhový graf rozpätia',
    rangeArea: 'Graf rozpätia oblasti',
    hierarchicalChart: 'Hierarchický graf',
    treemap: 'Mapa stromov',
    sunburst: 'Sunburst',
    specializedChart: 'Špecializovaný graf',
    waterfall: 'Vodopád',
    heatmap: 'Tepelná mapa',
    combinationChart: 'Kombinovaný graf',
    columnLineCombo: 'Stĺpcový a čiarový',
    AreaColumnCombo: 'Oblasťový a stĺpcový',

    // Charts
    pivotChartTitle: 'Kontingenčný graf',
    rangeChartTitle: 'Rozsahový graf',
    settings: 'Graf',
    data: 'Nastavenie',
    format: 'Prispôsobenie',
    categories: 'Kategórie',
    defaultCategory: '(Žiadna)',
    series: 'Série',
    switchCategorySeries: 'Prepnúť kategóriu / sériu',
    categoryValues: 'Hodnoty kategórie',
    seriesLabels: 'Štítky sérií',
    aggregate: 'Agregát',
    xyValues: 'X Y hodnoty',
    paired: 'Párový režim',
    axis: 'Os',
    xAxis: 'Horizontálna os',
    yAxis: 'Vertikálna os',
    polarAxis: 'Polárna os',
    radiusAxis: 'Polomerová os',
    navigator: 'Navigátor',
    zoom: 'Priblíženie',
    animation: 'Animácia',
    crosshair: 'Krížové vlásko',
    color: 'Farba',
    thickness: 'Hrúbka',
    preferredLength: 'Preferovaná dĺžka',
    xType: 'X typ',
    axisType: 'Typ osy',
    automatic: 'Automatický',
    category: 'Kategória',
    number: 'Číslo',
    time: 'Čas',
    timeFormat: 'Formát času',
    autoRotate: 'Automatické otočenie',
    labelRotation: 'Otočenie',
    circle: 'Kruh',
    polygon: 'Mnohouholník',
    square: 'Štvorec',
    cross: 'Kríž',
    diamond: 'Diamant',
    plus: 'Plus',
    triangle: 'Trojuholník',
    heart: 'Srdce',
    orientation: 'Orientácia',
    fixed: 'Fixný',
    parallel: 'Paralelný',
    perpendicular: 'Kolmý',
    radiusAxisPosition: 'Pozícia',
    ticks: 'Značky',
    gridLines: 'Mriežkové čiary',
    width: 'Šírka',
    height: 'Výška',
    length: 'Dĺžka',
    padding: 'Odsadenie',
    spacing: 'Rozostup',
    chartStyle: 'Štýl grafu',
    title: 'Názov',
    chartTitles: 'Názvy',
    chartTitle: 'Názov grafu',
    chartSubtitle: 'Podnázov',
    horizontalAxisTitle: 'Názov horizontálnej osi',
    verticalAxisTitle: 'Názov vertikálnej osi',
    polarAxisTitle: 'Názov polárnej osi',
    titlePlaceholder: 'Názov grafu',
    background: 'Pozadie',
    font: 'Písmo',
    weight: 'Hmotnosť',
    top: 'Hore',
    right: 'Pravo',
    bottom: 'Dole',
    left: 'Vľavo',
    labels: 'Štítky',
    calloutLabels: 'Štítky výzvy',
    sectorLabels: 'Štítky sektorov',
    positionRatio: 'Pomer pozície',
    size: 'Veľkosť',
    shape: 'Tvar',
    minSize: 'Minimálna veľkosť',
    maxSize: 'Maximálna veľkosť',
    legend: 'Legenda',
    position: 'Pozícia',
    markerSize: 'Veľkosť značky',
    markerStroke: 'Obrys značky',
    markerPadding: 'Odsadenie značky',
    itemSpacing: 'Rozostup položiek',
    itemPaddingX: 'Odsadenie položky X',
    itemPaddingY: 'Odsadenie položky Y',
    layoutHorizontalSpacing: 'Horizontálny rozostup',
    layoutVerticalSpacing: 'Vertikálny rozostup',
    strokeWidth: 'Šírka obrysu',
    offset: 'Offset',
    offsets: 'Offsets',
    tooltips: 'Tooltipy',
    callout: 'Výzva',
    markers: 'Značky',
    shadow: 'Tieň',
    blur: 'Rozmazanie',
    xOffset: 'X offset',
    yOffset: 'Y offset',
    lineWidth: 'Šírka línie',
    lineDash: 'Prerušovaná čiara',
    lineDashOffset: 'Offset prerušovanej čiary',
    scrollingZoom: 'Rolovanie',
    scrollingStep: 'Krok rolovania',
    selectingZoom: 'Výber',
    durationMillis: 'Doba trvania (ms)',
    crosshairLabel: 'Štítok',
    crosshairSnap: 'Prichytiť k uzlu',
    normal: 'Normálny',
    bold: 'Tučný',
    italic: 'Kurzíva',
    boldItalic: 'Tučná kurzíva',
    predefined: 'Preddefinovaný',
    fillOpacity: 'Nepriehľadnosť výplne',
    strokeColor: 'Farba obrysu',
    strokeOpacity: 'Nepriehľadnosť obrysu',
    miniChart: 'Mini-graf',
    histogramBinCount: 'Počet košov',
    connectorLine: 'Spojovacia čiara',
    seriesItems: 'Položky sérií',
    seriesItemType: 'Typ položky',
    seriesItemPositive: 'Pozitívne',
    seriesItemNegative: 'Negatívne',
    seriesItemLabels: 'Štítky položiek',
    columnGroup: 'Stĺpec',
    barGroup: 'Stĺpec',
    pieGroup: 'Koláč',
    lineGroup: 'Línia',
    scatterGroup: 'X Y (Rozptyl)',
    areaGroup: 'Oblasť',
    polarGroup: 'Polárny',
    statisticalGroup: 'Štatistický',
    hierarchicalGroup: 'Hierarchický',
    specializedGroup: 'Špecializovaný',
    combinationGroup: 'Kombinovaný',
    groupedColumnTooltip: 'Zoskupené',
    stackedColumnTooltip: 'Na sebe',
    normalizedColumnTooltip: '100% na sebe',
    groupedBarTooltip: 'Zoskupené',
    stackedBarTooltip: 'Na sebe',
    normalizedBarTooltip: '100% na sebe',
    pieTooltip: 'Koláč',
    donutTooltip: 'Donut',
    lineTooltip: 'Línia',
    stackedLineTooltip: 'Skladaný',
    normalizedLineTooltip: '100% Skladaný',
    groupedAreaTooltip: 'Oblasť',
    stackedAreaTooltip: 'Na sebe',
    normalizedAreaTooltip: '100% na sebe',
    scatterTooltip: 'Rozptyl',
    bubbleTooltip: 'Bublina',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Radiálny stĺpec',
    radialBarTooltip: 'Radiálny stĺpec',
    radarLineTooltip: 'Radarová línia',
    radarAreaTooltip: 'Radarová oblasť',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Rozsahový stĺpec',
    rangeAreaTooltip: 'Rozsahová oblasť',
    boxPlotTooltip: 'Box Plot',
    treemapTooltip: 'Treemap',
    sunburstTooltip: 'Sunburst',
    waterfallTooltip: 'Vodopádový graf',
    heatmapTooltip: 'Heatmap',
    columnLineComboTooltip: 'Stĺpec a línia',
    areaColumnComboTooltip: 'Oblasť a stĺpec',
    customComboTooltip: 'Vlastná kombinácia',
    innerRadius: 'Vnútorný polomer',
    startAngle: 'Počiatočný uhol',
    endAngle: 'Koncový uhol',
    reverseDirection: 'Obrátiť smer',
    groupPadding: 'Odsadenie skupiny',
    seriesPadding: 'Odsadenie série',
    tile: 'Dlaždica',
    whisker: 'Fúz',
    cap: 'Čiapka',
    capLengthRatio: 'Pomer dĺžky',
    labelPlacement: 'Umiestnenie',
    inside: 'Vo vnútri',
    outside: 'Vonku',
    noDataToChart: 'Nie sú k dispozícii žiadne údaje na graf.',
    pivotChartRequiresPivotMode: 'Kontingenčný graf vyžaduje zapnutý kontingenčný režim.',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Prepojené s mriežkou',
    chartUnlinkToolbarTooltip: 'Odpojené od mriežky',
    chartDownloadToolbarTooltip: 'Stiahnuť graf',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Upraviť graf',
    chartAdvancedSettings: 'Pokročilé nastavenia',
    chartLink: 'Prepojiť s mriežkou',
    chartUnlink: 'Odpojiť od mriežky',
    chartDownload: 'Stiahnuť graf',
    histogramFrequency: 'Frekvencia',
    seriesChartType: 'Typ grafu série',
    seriesType: 'Typ série',
    secondaryAxis: 'Sekundárna os',
    seriesAdd: 'Pridať sériu',
    categoryAdd: 'Pridať kategóriu',
    bar: 'Bar',
    column: 'Stĺpec',
    histogram: 'Histogram',
    advancedSettings: 'Pokročilé nastavenia',
    direction: 'Smer',
    horizontal: 'Horizontálny',
    vertical: 'Vertikálny',
    seriesGroupType: 'Typ skupiny série',
    groupedSeriesGroupType: 'Zoskupené',
    stackedSeriesGroupType: 'Na sebe',
    normalizedSeriesGroupType: '100% na sebe',
    legendEnabled: 'Povolené',
    invalidColor: 'Hodnota farby je neplatná',
    groupedColumnFull: 'Zoskupený stĺpec',
    stackedColumnFull: 'Na sebe stĺpec',
    normalizedColumnFull: '100% na sebe stĺpec',
    groupedBarFull: 'Zoskupený bar',
    stackedBarFull: 'Na sebe bar',
    normalizedBarFull: '100% na sebe bar',
    stackedAreaFull: 'Na sebe oblasť',
    normalizedAreaFull: '100% na sebe oblasť',
    customCombo: 'Vlastná kombinácia',
    funnel: 'Trychtýř',
    coneFunnel: 'Kužeľový Trychtýř',
    pyramid: 'Pyramída',
    funnelGroup: 'Trychtýř',
    funnelTooltip: 'Trychtýř',
    coneFunnelTooltip: 'Kužeľový Trychtýř',
    pyramidTooltip: 'Pyramída',
    dropOff: 'Pokles',
    stageLabels: 'Etikety Fáz',
    reverse: 'Prehodiť',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Úroveň ${variable}. Stlačte ENTER na úpravu.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. Úroveň ${variable}. ${variable} Stlačte ENTER na úpravu.',
    ariaAdvancedFilterBuilderList: 'Zoznam Pokročilých Filtrov',
    ariaAdvancedFilterBuilderFilterItem: 'Podmienka Filtra',
    ariaAdvancedFilterBuilderGroupItem: 'Skupina Filtrov',
    ariaAdvancedFilterBuilderColumn: 'Stĺpec',
    ariaAdvancedFilterBuilderOption: 'Možnosť',
    ariaAdvancedFilterBuilderValueP: 'Hodnota',
    ariaAdvancedFilterBuilderJoinOperator: 'Spojovací Operátor',
    ariaAdvancedFilterInput: 'Vstup Pokročilého Filtra',
    ariaChecked: 'označené',
    ariaColumn: 'Stĺpec',
    ariaColumnGroup: 'Skupina Stĺpcov',
    ariaColumnFiltered: 'Stĺpec Filtrovaný',
    ariaColumnSelectAll: 'Prepínať viditeľnosť všetkých stĺpcov',
    ariaDateFilterInput: 'Vstup Dátumového Filtra',
    ariaDefaultListName: 'Zoznam',
    ariaFilterColumnsInput: 'Vstup Filtra pre Stĺpce',
    ariaFilterFromValue: 'Filtrovať od hodnoty',
    ariaFilterInput: 'Vstup Filtra',
    ariaFilterList: 'Zoznam Filtrov',
    ariaFilterToValue: 'Filtrovať do hodnoty',
    ariaFilterValue: 'Hodnota Filtra',
    ariaFilterMenuOpen: 'Otvoriť Menu Filtra',
    ariaFilteringOperator: 'Filtračný Operátor',
    ariaHidden: 'skryté',
    ariaIndeterminate: 'neurčené',
    ariaInputEditor: 'Editor Vstupu',
    ariaMenuColumn: 'Stlačte ALT DOWN na otvorenie menu stĺpcov',
    ariaFilterColumn: 'Stlačte CTRL ENTER na otvorenie filtra',
    ariaRowDeselect: 'Stlačte SPACE na zrušenie výberu tohto riadku',
    ariaHeaderSelection: 'Stĺpec s výberom hlavičky',
    ariaSelectAllCells: 'Stlačte medzerník pre výber všetkých buniek',
    ariaRowSelectAll: 'Stlačte SPACE na prepínanie výberu všetkých riadkov',
    ariaRowToggleSelection: 'Stlačte SPACE na prepínanie výberu riadku',
    ariaRowSelect: 'Stlačte SPACE na výber tohto riadku',
    ariaRowSelectionDisabled: 'Výber riadku je pre tento riadok zakázaný',
    ariaSearch: 'Hľadať',
    ariaSortableColumn: 'Stlačte ENTER na zoradenie',
    ariaToggleVisibility: 'Stlačte SPACE na prepínanie viditeľnosti',
    ariaToggleCellValue: 'Stlačte SPACE na prepínanie hodnoty bunky',
    ariaUnchecked: 'neoznačené',
    ariaVisible: 'viditeľné',
    ariaSearchFilterValues: 'Hľadať hodnoty filtra',
    ariaPageSizeSelectorLabel: 'Veľkosť Strany',
    ariaChartMenuClose: 'Zatvoriť Menu Úpravy Grafu',
    ariaChartSelected: 'Vybrané',
    ariaSkeletonCellLoadingFailed: 'Nahrávanie riadku zlyhalo',
    ariaSkeletonCellLoading: 'Údaje riadku sa nahrávajú',
    ariaDeferSkeletonCellLoading: 'Načítava sa bunka',

    // ARIA for Batch Edit
    ariaPendingChange: 'Čakajúca zmena',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Skupiny riadkov',
    ariaValuesDropZonePanelLabel: 'Hodnoty',
    ariaPivotDropZonePanelLabel: 'Štítky stĺpcov',
    ariaDropZoneColumnComponentDescription: 'Stlačte DELETE pre odstránenie',
    ariaDropZoneColumnValueItemDescription: 'Stlačte ENTER pre zmenu typu agregácie',
    ariaDropZoneColumnGroupItemDescription: 'Stlačte ENTER pre zoradenie',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' z ',
    ariaDropZoneColumnComponentSortAscending: 'vzostupne',
    ariaDropZoneColumnComponentSortDescending: 'zostupne',
    ariaLabelDialog: 'Dialóg',
    ariaLabelColumnMenu: 'Ponuka stĺpcov',
    ariaLabelColumnFilter: 'Filter stĺpcov',
    ariaLabelSelectField: 'Vyberte pole',

    // Cell Editor
    ariaLabelCellEditor: 'Editor bunky',
    ariaValidationErrorPrefix: 'Validácia editora bunky',
    ariaLabelLoadingContextMenu: 'Načítava sa kontextová ponuka',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Pole Rich Select',
    ariaLabelRichSelectToggleSelection: 'Stlačte MEDZERNÍK na prepnutie výberu',
    ariaLabelRichSelectDeselectAllItems: 'Stlačte DELETE na odznačenie všetkých položiek',
    ariaLabelRichSelectDeleteSelection: 'Stlačte DELETE na odznačenie položky',
    ariaLabelTooltip: 'Tooltip',
    ariaLabelContextMenu: 'Kontextové menu',
    ariaLabelSubMenu: 'SubMenu',
    ariaLabelAggregationFunction: 'Funkcia agregácie',
    ariaLabelAdvancedFilterAutocomplete: 'Pokročilé filtrovanie Autocomplete',
    ariaLabelAdvancedFilterBuilderAddField: 'Pridať pole v Pokročilom filtrovaní',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Vybrať stĺpec v Pokročilom filtrovaní',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Vybrať možnosť v Pokročilom filtrovaní',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Vybrať operátor spojenia v Pokročilom filtrovaní',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Zoznam stĺpcov',
    ariaFilterPanelList: 'Zoznam filtrov',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Pridať pole filtra',
    ariaLabelFilterCardDelete: 'Odstrániť filter',
    ariaLabelFilterCardHasEdits: 'Má úpravy',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '.',
    decimalSeparator: ',',

    // Data types
    true: 'Pravda',
    false: 'Nepravda',
    invalidDate: 'Neplatný dátum',
    invalidNumber: 'Neplatné číslo',
    january: 'Január',
    february: 'Február',
    march: 'Marec',
    april: 'Apríl',
    may: 'Máj',
    june: 'Jún',
    july: 'Júl',
    august: 'August',
    september: 'September',
    october: 'Október',
    november: 'November',
    december: 'December',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.RR',
    timeFormatDotsMDDYY: 'M.DD.RR',
    timeFormatDashesYYYYMMDD: 'RRRR-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM RRRR',
    timeFormatHHMMSS: 'HH:MM:SS',
    timeFormatHHMMSSAmPm: 'HH:MM:SS AM/PM',
};
