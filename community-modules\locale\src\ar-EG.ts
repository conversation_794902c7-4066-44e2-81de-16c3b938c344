/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_EG = {
    // Set Filter
    selectAll: '(تحديد الكل)',
    selectAllSearchResults: '(تحديد كل نتائج البحث)',
    addCurrentSelectionToFilter: 'إضافة التحديد الحالي إلى الفلتر',
    searchOoo: 'بحث...',
    blanks: '(فارغ)',
    noMatches: 'لا توجد تطابقات',

    // Number Filter & Text Filter
    filterOoo: 'تصفية...',
    equals: 'يساوي',
    notEqual: 'لا يساوي',
    blank: 'فارغ',
    notBlank: 'ليس فارغ',
    empty: 'اختر واحد',

    // Number Filter
    lessThan: 'أقل من',
    greaterThan: 'أكبر من',
    lessThanOrEqual: 'أقل من أو يساوي',
    greaterThanOrEqual: 'أكبر من أو يساوي',
    inRange: 'بين',
    inRangeStart: 'من',
    inRangeEnd: 'إلى',

    // Text Filter
    contains: 'يحتوي على',
    notContains: 'لا يحتوي على',
    startsWith: 'يبدأ بـ',
    endsWith: 'ينتهي بـ',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'قبل',
    after: 'بعد',

    // Filter Conditions
    andCondition: 'و',
    orCondition: 'أو',

    // Filter Buttons
    applyFilter: 'تطبيق',
    resetFilter: 'إعادة تعيين',
    clearFilter: 'مسح',
    cancelFilter: 'إلغاء',

    // Filter Titles
    textFilter: 'فلتر النص',
    numberFilter: 'فلتر الأرقام',
    dateFilter: 'فلتر التاريخ',
    setFilter: 'فلتر المجموعة',

    // Group Column Filter
    groupFilterSelect: 'اختر الحقل:',

    // New Filter Tool Panel
    filterSummaryInactive: 'هو (الكل)',
    filterSummaryContains: 'يحتوي على',
    filterSummaryNotContains: 'لا يحتوي على',
    filterSummaryTextEquals: 'يساوي',
    filterSummaryTextNotEqual: 'لا يساوي',
    filterSummaryStartsWith: 'يبدأ بـ',
    filterSummaryEndsWith: 'ينتهي بـ',
    filterSummaryBlank: 'هو فارغ',
    filterSummaryNotBlank: 'ليس فارغًا',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'بين',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'هو (الكل)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'هو (${variable})',
    filterSummaryListLong: 'هو (${variable}) و${variable} أخرى',
    addFilterCard: 'إضافة فلتر',
    agTextColumnFilterDisplayName: 'فلتر بسيط',
    agNumberColumnFilterDisplayName: 'فلتر بسيط',
    agDateColumnFilterDisplayName: 'فلتر بسيط',
    agSetColumnFilterDisplayName: 'فلتر اختيار',
    agMultiColumnFilterDisplayName: 'فلتر مركب',
    addFilterPlaceholder: 'ابحث في الأعمدة...',

    // Advanced Filter
    advancedFilterContains: 'يحتوي على',
    advancedFilterNotContains: 'لا يحتوي على',
    advancedFilterTextEquals: 'يساوي',
    advancedFilterTextNotEqual: 'لا يساوي',
    advancedFilterStartsWith: 'يبدأ بـ',
    advancedFilterEndsWith: 'ينتهي بـ',
    advancedFilterBlank: 'فارغ',
    advancedFilterNotBlank: 'غير فارغ',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'صحيح',
    advancedFilterFalse: 'خاطئ',
    advancedFilterAnd: 'و',
    advancedFilterOr: 'أو',
    advancedFilterApply: 'تطبيق',
    advancedFilterBuilder: 'المنشئ',
    advancedFilterValidationMissingColumn: 'العمود مفقود',
    advancedFilterValidationMissingOption: 'الخيار مفقود',
    advancedFilterValidationMissingValue: 'القيمة مفقودة',
    advancedFilterValidationInvalidColumn: 'العمود غير موجود',
    advancedFilterValidationInvalidOption: 'الخيار غير موجود',
    advancedFilterValidationMissingQuote: 'القيمة تفتقد علامة الاقتباس النهائية',
    advancedFilterValidationNotANumber: 'القيمة ليست رقماً',
    advancedFilterValidationInvalidDate: 'القيمة ليست تاريخاً صحيحاً',
    advancedFilterValidationMissingCondition: 'الشرط مفقود',
    advancedFilterValidationJoinOperatorMismatch: 'يجب أن تكون عوامل الربط داخل الشرط نفسها',
    advancedFilterValidationInvalidJoinOperator: 'عامل الربط غير موجود',
    advancedFilterValidationMissingEndBracket: 'القوس الختامي مفقود',
    advancedFilterValidationExtraEndBracket: 'أقواس ختامية كثيرة جداً',
    advancedFilterValidationMessage: 'التعبير يحتوي على خطأ. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'التعبير يحتوي على خطأ. ${variable} في نهاية التعبير.',
    advancedFilterBuilderTitle: 'عامل تصفية متقدم',
    advancedFilterBuilderApply: 'تطبيق',
    advancedFilterBuilderCancel: 'إلغاء',
    advancedFilterBuilderAddButtonTooltip: 'إضافة عامل تصفية أو مجموعة',
    advancedFilterBuilderRemoveButtonTooltip: 'إزالة',
    advancedFilterBuilderMoveUpButtonTooltip: 'تحريك للأعلى',
    advancedFilterBuilderMoveDownButtonTooltip: 'تحريك للأسفل',
    advancedFilterBuilderAddJoin: 'إضافة مجموعة',
    advancedFilterBuilderAddCondition: 'إضافة عامل تصفية',
    advancedFilterBuilderSelectColumn: 'اختر عمودًا',
    advancedFilterBuilderSelectOption: 'اختر خيارًا',
    advancedFilterBuilderEnterValue: 'أدخل قيمة...',
    advancedFilterBuilderValidationAlreadyApplied: 'عامل التصفية الحالي مُطبَّق بالفعل.',
    advancedFilterBuilderValidationIncomplete: 'لم يتم إكمال كل الشروط.',
    advancedFilterBuilderValidationSelectColumn: 'يجب اختيار عمود.',
    advancedFilterBuilderValidationSelectOption: 'يجب اختيار خيار.',
    advancedFilterBuilderValidationEnterValue: 'يجب إدخال قيمة.',

    // Editor Validation Errors
    minDateValidation: 'التاريخ يجب أن يكون بعد ${variable}',
    maxDateValidation: 'التاريخ يجب أن يكون قبل ${variable}',
    maxLengthValidation: 'يجب أن يكون ${variable} حرف أو أقل.',
    minValueValidation: 'يجب أن يكون أكبر من أو يساوي ${variable}',
    maxValueValidation: 'يجب أن يكون أقل من أو يساوي ${variable}',
    invalidSelectionValidation: 'اختيار غير صالح.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'أعمدة',
    filters: 'مرشحات',

    // columns tool panel
    pivotMode: 'وضع المحور',
    groups: 'مجموعات الصفوف',
    rowGroupColumnsEmptyMessage: 'اسحب هنا لتعيين مجموعات الصفوف',
    values: 'القيم',
    valueColumnsEmptyMessage: 'اسحب هنا للتجميع',
    pivots: 'تسميات الأعمدة',
    pivotColumnsEmptyMessage: 'اسحب هنا لتعيين تسميات الأعمدة',

    // Header of the Default Group Column
    group: 'مجموعة',

    // Row Drag
    rowDragRow: 'صف',
    rowDragRows: 'صفوف',

    // Other
    loadingOoo: 'جارٍ التحميل...',
    loadingError: 'خطأ',
    noRowsToShow: 'لا توجد صفوف للعرض',
    enabled: 'مُمكّن',

    // Menu
    pinColumn: 'تثبيت العمود',
    pinLeft: 'تثبيت لليسار',
    pinRight: 'تثبيت لليمين',
    noPin: 'بدون تثبيت',
    valueAggregation: 'تجميع القيمة',
    noAggregation: 'بدون تجميع',
    autosizeThisColumn: 'تغيير حجم هذا العمود تلقائيًا',
    autosizeAllColumns: 'تغيير حجم جميع الأعمدة تلقائيًا',
    groupBy: 'تجميع بواسطة',
    ungroupBy: 'إلغاء التجميع بواسطة',
    ungroupAll: 'إلغاء التجميع الكل',
    addToValues: 'إضافة ${variable} إلى القيم',
    removeFromValues: 'إزالة ${variable} من القيم',
    addToLabels: 'إضافة ${variable} إلى التسميات',
    removeFromLabels: 'إزالة ${variable} من التسميات',
    resetColumns: 'إعادة ضبط الأعمدة',
    expandAll: 'توسيع جميع مجموعات الصفوف',
    collapseAll: 'إغلاق جميع مجموعات الصفوف',
    copy: 'نسخ',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'نسخ مع الرؤوس',
    copyWithGroupHeaders: 'نسخ مع رؤوس المجموعات',
    cut: 'قص',
    paste: 'لصق',
    ctrlV: 'Ctrl+V',
    export: 'تصدير',
    csvExport: 'تصدير CSV',
    excelExport: 'تصدير Excel',
    columnFilter: 'تصفية العمود',
    columnChooser: 'اختيار الأعمدة',
    chooseColumns: 'اختيار الأعمدة',
    sortAscending: 'ترتيب تصاعدي',
    sortDescending: 'ترتيب تنازلي',
    sortUnSort: 'إلغاء الترتيب',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'المجموع',
    first: 'الأول',
    last: 'الأخير',
    min: 'الحد الأدنى',
    max: 'الحد الأقصى',
    none: 'بدون',
    count: 'العدد',
    avg: 'المتوسط',
    filteredRows: 'المرشحة',
    selectedRows: 'المحددة',
    totalRows: 'إجمالي الصفوف',
    totalAndFilteredRows: 'الصفوف',
    more: 'المزيد',
    to: 'إلى',
    of: 'من',
    page: 'الصفحة',
    pageLastRowUnknown: '?',
    nextPage: 'الصفحة التالية',
    lastPage: 'الصفحة الأخيرة',
    firstPage: 'الصفحة الأولى',
    previousPage: 'الصفحة السابقة',
    pageSizeSelectorLabel: 'حجم الصفحة:',
    footerTotal: 'المجموع',
    statusBarLastRowUnknown: '؟',
    scrollColumnIntoView: "مرر '${variable}' إلى العرض",

    // Pivoting
    pivotColumnGroupTotals: 'المجموع',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'الرسم المحوري ووضع المحوري',
    pivotChart: 'الرسم المحوري',
    chartRange: 'نطاق الرسم',
    columnChart: 'عمود',
    groupedColumn: 'مجموعة',
    stackedColumn: 'مكدس',
    normalizedColumn: '100% مكدس',
    barChart: 'شريط',
    groupedBar: 'مجموعة',
    stackedBar: 'مكدس',
    normalizedBar: '100% مكدس',
    pieChart: 'دائرة',
    pie: 'دائرة',
    donut: 'دونات',
    lineChart: 'خط',
    stackedLine: 'مكدس',
    normalizedLine: 'مكدس 100%',
    xyChart: 'س س (نقطة)',
    scatter: 'نقاط مبعثرة',
    bubble: 'فقاعة',
    areaChart: 'منطقة',
    area: 'منطقة',
    stackedArea: 'مكدس',
    normalizedArea: '100% مكدس',
    histogramChart: 'الرسم البياني للتوزيع',
    polarChart: 'قطبي',
    radarLine: 'خط الرادار',
    radarArea: 'منطقة الرادار',
    nightingale: 'نايتنجيل',
    radialColumn: 'عمود شعاعي',
    radialBar: 'شريط شعاعي',
    statisticalChart: 'إحصائي',
    boxPlot: 'صندوق مخطط',
    rangeBar: 'شريط المدى',
    rangeArea: 'منطقة المدى',
    hierarchicalChart: 'التسلسل الهرمي',
    treemap: 'خريطة الشجرة',
    sunburst: 'انفجار شمسي',
    specializedChart: 'تخصصي',
    waterfall: 'شلال',
    heatmap: 'خريطة الحرارة',
    combinationChart: 'مزيج',
    columnLineCombo: 'عمود وخط',
    AreaColumnCombo: 'منطقة وعمود',

    // Charts
    pivotChartTitle: 'رسم محوري',
    rangeChartTitle: 'رسم نطاق',
    settings: 'إعدادات',
    data: 'إعداد البيانات',
    format: 'تخصيص',
    categories: 'الفئات',
    defaultCategory: '(لا شيء)',
    series: 'السلسلة',
    switchCategorySeries: 'تبديل الفئة / السلسلة',
    categoryValues: 'قيم الفئة',
    seriesLabels: 'تسميات السلسلة',
    aggregate: 'مجموعة',
    xyValues: 'قيم إحداثيات X Y',
    paired: 'الوضع المقترن',
    axis: 'محور',
    xAxis: 'المحور الأفقي',
    yAxis: 'المحور الرأسي',
    polarAxis: 'محور قطبي',
    radiusAxis: 'محور نصف القطر',
    navigator: 'مستكشف',
    zoom: 'تكبير',
    animation: 'تحريك',
    crosshair: 'مؤشر',
    color: 'لون',
    thickness: 'سُمك',
    preferredLength: 'الطول المفضل',
    xType: 'نوع X',
    axisType: 'نوع المحور',
    automatic: 'تلقائي',
    category: 'فئة',
    number: 'رقم',
    time: 'وقت',
    timeFormat: 'صيغة الوقت',
    autoRotate: 'تدوير تلقائي',
    labelRotation: 'تدوير التسميات',
    circle: 'دائرة',
    polygon: 'مضلع',
    square: 'مربع',
    cross: 'صليب',
    diamond: 'ماس',
    plus: 'زائد',
    triangle: 'مثلث',
    heart: 'قلب',
    orientation: 'اتجاه',
    fixed: 'ثابت',
    parallel: 'متوازي',
    perpendicular: 'عمودي',
    radiusAxisPosition: 'الموقع',
    ticks: 'علامات',
    gridLines: 'خطوط الشبكة',
    width: 'عرض',
    height: 'ارتفاع',
    length: 'طول',
    padding: 'حشوة',
    spacing: 'تباعد',
    chartStyle: 'نمط الرسم البياني',
    title: 'عنوان',
    chartTitles: 'عناوين الرسوم البيانية',
    chartTitle: 'عنوان الرسم البياني',
    chartSubtitle: 'العنوان الفرعي',
    horizontalAxisTitle: 'عنوان المحور الأفقي',
    verticalAxisTitle: 'عنوان المحور الرأسي',
    polarAxisTitle: 'عنوان المحور القطبي',
    titlePlaceholder: 'عنوان الرسم البياني',
    background: 'خلفية',
    font: 'خط',
    weight: 'وزن',
    top: 'أعلى',
    right: 'يمين',
    bottom: 'أسفل',
    left: 'يسار',
    labels: 'التسميات',
    calloutLabels: 'تسميات التنبيه',
    sectorLabels: 'تسميات القطاع',
    positionRatio: 'نسبة الموقع',
    size: 'حجم',
    shape: 'شكل',
    minSize: 'الحد الأدنى للحجم',
    maxSize: 'الحد الأقصى للحجم',
    legend: 'وسيلة الإيضاح',
    position: 'الموقع',
    markerSize: 'حجم العلامة',
    markerStroke: 'خط العلامة',
    markerPadding: 'حشوة العلامة',
    itemSpacing: 'تباعد العناصر',
    itemPaddingX: 'حشوة العنصر X',
    itemPaddingY: 'حشوة العنصر Y',
    layoutHorizontalSpacing: 'التباعد الأفقي',
    layoutVerticalSpacing: 'التباعد الرأسي',
    strokeWidth: 'سمك الخط',
    offset: 'إزاحة',
    offsets: 'إزاحات',
    tooltips: 'أدوات الإرشاد',
    callout: 'تنبيه',
    markers: 'علامات',
    shadow: 'ظل',
    blur: 'ضبابية',
    xOffset: 'إزاحة X',
    yOffset: 'إزاحة Y',
    lineWidth: 'عرض الخط',
    lineDash: 'خط منقط',
    lineDashOffset: 'إزاحة النقطة',
    scrollingZoom: 'تكبير التمرير',
    scrollingStep: 'خطوة التمرير',
    selectingZoom: 'تكبير التحديد',
    durationMillis: 'المدة (مللي ثانية)',
    crosshairLabel: 'تسمية المؤشر',
    crosshairSnap: 'التقاط المؤشر',
    normal: 'عادي',
    bold: 'غامق',
    italic: 'مائل',
    boldItalic: 'غامق ومائل',
    predefined: 'محدد مسبقًا',
    fillOpacity: 'شفافية التعبئة',
    strokeColor: 'لون الخط',
    strokeOpacity: 'شفافية الخط',
    miniChart: 'رسم صغير',
    histogramBinCount: 'عدد الصناديق',
    connectorLine: 'خط الموصل',
    seriesItems: 'عناصر السلسلة',
    seriesItemType: 'نوع العنصر',
    seriesItemPositive: 'إيجابي',
    seriesItemNegative: 'سلبي',
    seriesItemLabels: 'تسميات العناصر',
    columnGroup: 'عمود',
    barGroup: 'شريط',
    pieGroup: 'فطيرة',
    lineGroup: 'خط',
    scatterGroup: 'X Y (مبعثر)',
    areaGroup: 'مساحة',
    polarGroup: 'قطبي',
    statisticalGroup: 'إحصائي',
    hierarchicalGroup: 'هرمي',
    specializedGroup: 'متخصص',
    combinationGroup: 'مزيج',
    groupedColumnTooltip: 'مجموعة',
    stackedColumnTooltip: 'مكدس',
    normalizedColumnTooltip: '100% مكدس',
    groupedBarTooltip: 'مجموعة',
    stackedBarTooltip: 'مكدس',
    normalizedBarTooltip: '100% مكدس',
    pieTooltip: 'فطيرة',
    donutTooltip: 'دونات',
    lineTooltip: 'خط',
    stackedLineTooltip: 'مكدسة',
    normalizedLineTooltip: 'مكدسة بنسبة 100٪',
    groupedAreaTooltip: 'مساحة مجموعة',
    stackedAreaTooltip: 'مساحة مكدسة',
    normalizedAreaTooltip: '100% مكدسة',
    scatterTooltip: 'مبعثر',
    bubbleTooltip: 'فقاعة',
    histogramTooltip: 'رسم بياني',
    radialColumnTooltip: 'عمود شعاعي',
    radialBarTooltip: 'شريط شعاعي',
    radarLineTooltip: 'خط راداري',
    radarAreaTooltip: 'مساحة رادارية',
    nightingaleTooltip: 'نايتنجيل',
    rangeBarTooltip: 'شريط النطاق',
    rangeAreaTooltip: 'مساحة النطاق',
    boxPlotTooltip: 'مربع مخطط',
    treemapTooltip: 'خريطة الشجرة',
    sunburstTooltip: 'شمس متفجرة',
    waterfallTooltip: 'شلال',
    heatmapTooltip: 'خريطة حرارية',
    columnLineComboTooltip: 'عمود وخط',
    areaColumnComboTooltip: 'مساحة وعمود',
    customComboTooltip: 'مزيج مخصص',
    innerRadius: 'نصف القطر الداخلي',
    startAngle: 'زاوية البداية',
    endAngle: 'زاوية النهاية',
    reverseDirection: 'عكس الاتجاه',
    groupPadding: 'حشوة المجموعة',
    seriesPadding: 'حشوة السلسلة',
    tile: 'بلاطة',
    whisker: 'شعر',
    cap: 'غطاء',
    capLengthRatio: 'نسبة طول الغطاء',
    labelPlacement: 'موضع التسمية',
    inside: 'داخل',
    outside: 'خارج',
    noDataToChart: 'لا توجد بيانات لعرضها في الرسم.',
    pivotChartRequiresPivotMode: 'يتطلب الرسم المحوري تفعيل وضع المحورية.',
    chartSettingsToolbarTooltip: 'قائمة',
    chartLinkToolbarTooltip: 'مرتبط بالشبكة',
    chartUnlinkToolbarTooltip: 'غير مرتبط بالشبكة',
    chartDownloadToolbarTooltip: 'تنزيل الرسم البياني',
    chartMenuToolbarTooltip: 'قائمة',
    chartEdit: 'تحرير الرسم البياني',
    chartAdvancedSettings: 'إعدادات متقدمة',
    chartLink: 'رابط للشبكة',
    chartUnlink: 'إلغاء الرابط من الشبكة',
    chartDownload: 'تنزيل الرسم البياني',
    histogramFrequency: 'التردد',
    seriesChartType: 'نوع رسم السلسلة',
    seriesType: 'نوع السلسلة',
    secondaryAxis: 'محور ثانوي',
    seriesAdd: 'إضافة سلسلة',
    categoryAdd: 'إضافة فئة',
    bar: 'شريط',
    column: 'عمود',
    histogram: 'رسم بياني',
    advancedSettings: 'الإعدادات المتقدمة',
    direction: 'اتجاه',
    horizontal: 'أفقي',
    vertical: 'رأسي',
    seriesGroupType: 'نوع المجموعة',
    groupedSeriesGroupType: 'مجموعة',
    stackedSeriesGroupType: 'مكدس',
    normalizedSeriesGroupType: '100% مكدس',
    legendEnabled: 'ممكّن',
    invalidColor: 'قيمة اللون غير صحيحة',
    groupedColumnFull: 'عمود مجمع',
    stackedColumnFull: 'عمود مكدس',
    normalizedColumnFull: '100% عمود مكدس',
    groupedBarFull: 'شريط مجمع',
    stackedBarFull: 'شريط مكدس',
    normalizedBarFull: '100% شريط مكدس',
    stackedAreaFull: 'مساحة مكدسة',
    normalizedAreaFull: '100% مساحة مكدسة',
    customCombo: 'مزيج مخصص',
    funnel: 'قمع',
    coneFunnel: 'قمع مخروطي',
    pyramid: 'هرم',
    funnelGroup: 'قمع',
    funnelTooltip: 'قمع',
    coneFunnelTooltip: 'قمع مخروطي',
    pyramidTooltip: 'هرم',
    dropOff: 'إسقاط',
    stageLabels: 'تسميات المرحلة',
    reverse: 'عكسي',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. المستوى ${variable}. اضغط ENTER لتعديل.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. المستوى ${variable}. ${variable} اضغط ENTER لتعديل.',
    ariaAdvancedFilterBuilderList: 'قائمة مُنشئ الفلترة المتقدمة',
    ariaAdvancedFilterBuilderFilterItem: 'شرط الفلترة',
    ariaAdvancedFilterBuilderGroupItem: 'مجموعة الفلترة',
    ariaAdvancedFilterBuilderColumn: 'العمود',
    ariaAdvancedFilterBuilderOption: 'الخيار',
    ariaAdvancedFilterBuilderValueP: 'القيمة',
    ariaAdvancedFilterBuilderJoinOperator: 'العملية الربط',
    ariaAdvancedFilterInput: 'إدخال الفلترة المتقدمة',
    ariaChecked: 'محدد',
    ariaColumn: 'العمود',
    ariaColumnGroup: 'مجموعة الأعمدة',
    ariaColumnFiltered: 'العمود مفلتر',
    ariaColumnSelectAll: 'تبديل ظهور كل الأعمدة',
    ariaDateFilterInput: 'إدخال فلتر التاريخ',
    ariaDefaultListName: 'قائمة',
    ariaFilterColumnsInput: 'إدخال فلترة الأعمدة',
    ariaFilterFromValue: 'الفلترة من القيمة',
    ariaFilterInput: 'إدخال الفلترة',
    ariaFilterList: 'قائمة الفلترة',
    ariaFilterToValue: 'الفلترة إلى القيمة',
    ariaFilterValue: 'قيمة الفلترة',
    ariaFilterMenuOpen: 'فتح قائمة الفلترة',
    ariaFilteringOperator: 'مشغل الفلترة',
    ariaHidden: 'مخفي',
    ariaIndeterminate: 'غير محدد',
    ariaInputEditor: 'محرر الإدخال',
    ariaMenuColumn: 'اضغط ALT DOWN لفتح قائمة العمود',
    ariaFilterColumn: 'اضغط CTRL ENTER لفتح الفلترة',
    ariaRowDeselect: 'اضغط SPACE لإلغاء تحديد هذا الصف',
    ariaHeaderSelection: 'عمود مع اختيار الرؤوس',
    ariaSelectAllCells: 'اضغط على المسافة لاختيار كل الخلايا',
    ariaRowSelectAll: 'اضغط Space لتبديل تحديد كل الصفوف',
    ariaRowToggleSelection: 'اضغط Space لتبديل تحديد الصف',
    ariaRowSelect: 'اضغط SPACE لتحديد هذا الصف',
    ariaRowSelectionDisabled: 'تحديد الصف معطل لهذا الصف',
    ariaSearch: 'بحث',
    ariaSortableColumn: 'اضغط ENTER لفرز',
    ariaToggleVisibility: 'اضغط SPACE لتبديل الرؤية',
    ariaToggleCellValue: 'اضغط SPACE لتبديل قيمة الخلية',
    ariaUnchecked: 'غير محدد',
    ariaVisible: 'مرئي',
    ariaSearchFilterValues: 'قيمة فلترة البحث',
    ariaPageSizeSelectorLabel: 'حجم الصفحة',
    ariaChartMenuClose: 'إغلاق قائمة تحرير الرسم البياني',
    ariaChartSelected: 'محدد',
    ariaSkeletonCellLoadingFailed: 'فشل تحميل الصف',
    ariaSkeletonCellLoading: 'جاري تحميل بيانات الصف',
    ariaDeferSkeletonCellLoading: 'الخلية قيد التحميل',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'مجموعات الصفوف',
    ariaValuesDropZonePanelLabel: 'القيم',
    ariaPivotDropZonePanelLabel: 'تسميات الأعمدة',
    ariaDropZoneColumnComponentDescription: 'اضغط على DELETE للإزالة',
    ariaDropZoneColumnValueItemDescription: 'اضغط على ENTER لتغيير نوع التجميع',
    ariaDropZoneColumnGroupItemDescription: 'اضغط على ENTER للفرز',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' من ',
    ariaDropZoneColumnComponentSortAscending: 'تصاعدي',
    ariaDropZoneColumnComponentSortDescending: 'تنازلي',
    ariaLabelDialog: 'حوار',
    ariaLabelColumnMenu: 'قائمة الأعمدة',
    ariaLabelColumnFilter: 'تصفية العمود',
    ariaLabelSelectField: 'اختيار الحقل',

    // Cell Editor
    ariaLabelCellEditor: 'محرر الخلية',
    ariaValidationErrorPrefix: 'تحقق من صحة محرر الخلية',
    ariaLabelLoadingContextMenu: 'تحميل قائمة السياق',

    // aria labels for rich select
    ariaLabelRichSelectField: 'حقل اختيار متقدم',
    ariaLabelRichSelectToggleSelection: 'اضغط SPACE لتغيير التحديد',
    ariaLabelRichSelectDeselectAllItems: 'اضغط DELETE لإلغاء تحديد كل العناصر',
    ariaLabelRichSelectDeleteSelection: 'اضغط DELETE لإلغاء تحديد العنصر',
    ariaLabelTooltip: 'تلميح',
    ariaLabelContextMenu: 'قائمة السياق',
    ariaLabelSubMenu: 'القائمة الفرعية',
    ariaLabelAggregationFunction: 'دالة التجميع',
    ariaLabelAdvancedFilterAutocomplete: 'الإكمال التلقائي للفلاتر المتقدمة',
    ariaLabelAdvancedFilterBuilderAddField: 'إضافة حقل منشئ الفلاتر المتقدمة',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'حقل اختيار الأعمدة في منشئ الفلاتر المتقدمة',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'حقل اختيار الخيارات في منشئ الفلاتر المتقدمة',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'حقل اختيار مشغل الربط في منشئ الفلاتر المتقدمة',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'قائمة الأعمدة',
    ariaFilterPanelList: 'قائمة المرشحات',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'إضافة حقل الفلتر',
    ariaLabelFilterCardDelete: 'حذف الفلتر',
    ariaLabelFilterCardHasEdits: 'يوجد تعديلات',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: '،',
    decimalSeparator: '.',

    // Data types
    true: 'صح',
    false: 'خطأ',
    invalidDate: 'تاريخ غير صالح',
    invalidNumber: 'رقم غير صالح',
    january: 'يناير',
    february: 'فبراير',
    march: 'مارس',
    april: 'أبريل',
    may: 'مايو',
    june: 'يونيو',
    july: 'يوليو',
    august: 'أغسطس',
    september: 'سبتمبر',
    october: 'أكتوبر',
    november: 'نوفمبر',
    december: 'ديسمبر',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'ساعات:دقائق:ثواني',
    timeFormatHHMMSSAmPm: 'ساعات:دقائق:ثواني ص/م',
};
