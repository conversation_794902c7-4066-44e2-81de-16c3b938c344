{"name": "@ag-grid-community/locale", "version": "34.0.1", "description": "Localisation Module for AG Grid, providing translations in 31 languages.", "main": "./dist/package/main.cjs.js", "types": "./dist/types/src/main.d.ts", "module": "./dist/package/main.esm.mjs", "exports": {".": {"import": "./dist/package/main.esm.mjs", "require": "./dist/package/main.cjs.js", "types": "./dist/types/src/main.d.ts", "default": "./dist/package/main.cjs.js"}, "./*.js": {"import": "./dist/package/*.esm.mjs", "require": "./dist/package/*.cjs.js", "types": "./dist/types/src/*.d.ts", "default": "./dist/package/*.cjs.js"}}, "repository": {"type": "git", "url": "https://github.com/ag-grid/ag-grid.git"}, "keywords": [], "author": "AG Grid <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/ag-grid/ag-grid/issues"}, "browserslist": ["> 1%", "last 2 versions", "not ie >= 0", "not ie_mob >= 0", "not blackberry > 0", "not op_mini all", "not operamobile >= 0"], "homepage": "https://www.ag-grid.com/", "devDependencies": {"@jest/globals": "29.6.1", "@types/jest": "^29.5.0", "@types/node": "^16.0.0", "@typescript-eslint/eslint-plugin": "^5.51.0", "@typescript-eslint/parser": "^5.51.0", "jest": "^29.5.0", "jest-runner": "^29.5.0", "jest-serial-runner": "^1.2.1", "ts-jest": "^29.1.0", "typescript": "~5.2.2"}, "publishConfig": {"access": "public"}}