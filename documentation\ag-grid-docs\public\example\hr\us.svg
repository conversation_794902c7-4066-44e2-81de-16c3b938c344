<svg width="66" height="54" viewBox="0 0 66 54" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1666_1503)">
<g clip-path="url(#clip0_1666_1503)">
<g clip-path="url(#clip1_1666_1503)">
<rect width="47.9531" height="35.9648" transform="translate(9 3)" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 3H56.9531V38.9648H9V3Z" fill="#E31D1C"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 5.99707V8.99414H56.9531V5.99707H9ZM9 11.9912V14.9883H56.9531V11.9912H9ZM9 20.9824V17.9854H56.9531V20.9824H9ZM9 23.9795V26.9766H56.9531V23.9795H9ZM9 32.9707V29.9736H56.9531V32.9707H9ZM9 38.9648V35.9678H56.9531V38.9648H9Z" fill="#F7FCFF"/>
<rect x="9" y="3" width="26.9736" height="20.9795" fill="#2E42A5"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12.1171 9.51482L13.7053 8.40851L14.9374 9.29549H14.2398L15.6504 10.5432L15.1739 12.2926H14.4277L13.7031 10.6858L13.0851 12.2926H11.2427L12.6533 13.5402L12.1171 15.509L13.7053 14.4027L14.9373 15.2896H14.2398L15.6504 16.5373L15.1739 18.2867H14.4277L13.7031 16.68L13.0851 18.2867H11.2427L12.6533 19.5344L12.1171 21.5032L13.7053 20.3968L15.242 21.5032L14.7642 19.5344L15.9983 18.2867H15.4291L16.7024 17.3998L17.9344 18.2867H17.2368L18.6474 19.5344L18.1112 21.5032L19.6995 20.3968L21.2362 21.5032L20.7584 19.5344L21.9925 18.2867H21.4233L22.6965 17.3998L23.9285 18.2867H23.231L24.6416 19.5344L24.1054 21.5032L25.6936 20.3968L27.2303 21.5032L26.7525 19.5344L27.9866 18.2867H27.4174L28.6907 17.3998L29.9226 18.2867H29.2251L30.6357 19.5344L30.0995 21.5032L31.6877 20.3968L33.2244 21.5032L32.7467 19.5344L33.9808 18.2867H32.4101L31.6855 16.68L31.0676 18.2867H30.1741L29.7496 16.5373L30.9837 15.2896H30.4145L31.6877 14.4027L33.2244 15.509L32.7467 13.5402L33.9808 12.2926H32.4101L31.6855 10.6858L31.0676 12.2926H30.1741L29.7496 10.5432L30.9837 9.29549H30.4145L31.6877 8.40856L33.2244 9.51487L32.7467 7.5461L33.9808 6.29842H32.4101L31.6855 4.69171L31.0676 6.29842H29.2251L30.6357 7.5461L30.1593 9.29549H29.413L28.6884 7.68878L28.0705 9.29549H27.1771L26.7525 7.5461L27.9866 6.29842H26.416L25.6914 4.69171L25.0734 6.29842H23.231L24.6416 7.5461L24.1651 9.29549H23.4189L22.6943 7.68878L22.0764 9.29549H21.1829L20.7584 7.5461L21.9925 6.29842H20.4218L19.6972 4.69171L19.0793 6.29842H17.2368L18.6474 7.5461L18.171 9.29549H17.4248L16.7002 7.68878L16.0822 9.29549H15.1888L14.7642 7.54604L15.9983 6.29837H14.4277L13.7031 4.69165L13.0851 6.29837H11.2427L12.6533 7.54604L12.1171 9.51482ZM30.1593 15.2896L30.6357 13.5402L29.2251 12.2926H29.9226L28.6907 11.4056L27.4174 12.2926H27.9866L26.7525 13.5402L27.1771 15.2896H28.0705L28.6884 13.6829L29.413 15.2896H30.1593ZM26.9256 15.2896L25.6936 14.4027L24.4203 15.2896H24.9896L23.7555 16.5373L24.18 18.2867H25.0734L25.6914 16.68L26.416 18.2867H27.1622L27.6387 16.5373L26.228 15.2896H26.9256ZM21.6445 16.5373L21.1681 18.2867H20.4218L19.6972 16.68L19.0793 18.2867H18.1859L17.7613 16.5373L18.9954 15.2896H18.4262L19.6995 14.4027L20.9314 15.2896H20.2339L21.6445 16.5373ZM22.0764 15.2896H21.1829L20.7584 13.5402L21.9925 12.2926H21.4233L22.6965 11.4056L23.9285 12.2926H23.231L24.6416 13.5402L24.1651 15.2896H23.4189L22.6943 13.6829L22.0764 15.2896ZM18.171 15.2896L18.6474 13.5402L17.2368 12.2926H17.9344L16.7024 11.4056L15.4291 12.2926H15.9983L14.7642 13.5402L15.1888 15.2896H16.0822L16.7002 13.6829L17.4248 15.2896H18.171ZM27.6387 10.5432L27.1622 12.2926H26.416L25.6914 10.6858L25.0734 12.2926H24.18L23.7555 10.5432L24.9896 9.29549H24.4203L25.6936 8.40856L26.9256 9.29549H26.228L27.6387 10.5432ZM20.9314 9.29549L19.6995 8.40856L18.4262 9.29549H18.9954L17.7613 10.5432L18.1859 12.2926H19.0793L19.6972 10.6858L20.4218 12.2926H21.1681L21.6445 10.5432L20.2339 9.29549H20.9314Z" fill="#F7FCFF"/>
</g>
<rect x="9" y="3" width="47.9531" height="35.9648" fill="url(#paint0_linear_1666_1503)" style="mix-blend-mode:overlay"/>
</g>
<rect x="10.4985" y="4.49854" width="44.9561" height="32.9678" rx="1.49854" stroke="black" stroke-opacity="0.1" stroke-width="2.99707" style="mix-blend-mode:multiply"/>
</g>
<defs>
<filter id="filter0_d_1666_1503" x="0.00878906" y="0.00292969" width="65.9355" height="53.9473" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="5.99414"/>
<feGaussianBlur stdDeviation="4.49561"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1666_1503"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1666_1503" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1666_1503" x1="32.9766" y1="3" x2="32.9766" y2="38.9648" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.7"/>
<stop offset="1" stop-opacity="0.3"/>
</linearGradient>
<clipPath id="clip0_1666_1503">
<rect x="9" y="3" width="47.9531" height="35.9648" rx="2.99707" fill="white"/>
</clipPath>
<clipPath id="clip1_1666_1503">
<rect width="47.9531" height="35.9648" fill="white" transform="translate(9 3)"/>
</clipPath>
</defs>
</svg>
