@use 'design-system' as *;

.columnContainer {
    display: flex;
    justify-content: space-between;

    @media screen and (max-width: $breakpoint-landing-page-medium) {
        flex-direction: column;
    }
}

.column {
    width: var(--layout-width-2-4);

    @media screen and (max-width: $breakpoint-landing-page-medium) {
        width: 100%;
    }
}

.featureContainer {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: $spacing-size-4;
    margin-bottom: $spacing-size-4;
}

.title {
    font-size: var(--text-fs-2xl);
}

.feature {
    display: flex;
    flex-direction: column;
}

.featureHeading {
    display: flex;
    flex-grow: 1;
    font-size: var(--text-xl);
    margin-bottom: $spacing-size-2;
}

.featureDetail {
    font-size: var(--text-fs-md);
    margin-bottom: $spacing-size-2;
}
