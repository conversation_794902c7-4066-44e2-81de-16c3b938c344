name: CI

on:
  push:
    branches:
      - 'latest'
      - 'b[0-9][0-9]?.[0-9][0-9]?.[0-9][0-9]?'
      - 'sean<PERSON>man-patch-9'
  pull_request:
    branches:
      - 'latest'
      - 'next'
      - 'b[0-9][0-9]?.[0-9][0-9]?.[0-9][0-9]?'
      - 'seanlandsman-patch-9'
  workflow_dispatch:
    inputs:
      run_docs:
        description: 'Skip docs build & link checker'
        type: 'choice'
        required: true
        default: 'true'
        options:
          - 'true'
          - 'false'
      run_bundle_size:
        description: 'Skip bundle size tests'
        type: 'choice'
        required: true
        default: 'true'
        options:
          - 'true'
          - 'false'
      clean_checkout:
        description: 'Disable all caching'
        type: 'choice'
        required: true
        default: 'false'
        options:
          - 'true'
          - 'false'
      nx_command:
        type: 'choice'
        required: true
        default: 'affected'
        options:
          - 'affected'
          - 'run-many'
env:
  NX_NO_CLOUD: true
  NX_BRANCH: ${{ github.ref }}
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
  SHARP_IGNORE_GLOBAL_LIBVIPS: true
  YARN_REGISTRY: "http://************:4873"
  CI: true

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.ref != 'refs/heads/latest' }}

permissions:
  contents: read
  actions: read

jobs:
  build_lint:
    name: Build & Lint
    outputs:
      nx_base: ${{ steps.setup.outputs.base }}
      build: ${{ steps.build.outcome || '' }}
      lint: ${{ steps.lint.outcome || '' }}
      format: ${{ steps.format.outcome || '' }}
      test_count: ${{ steps.matrix.outputs.test_count }}
      test_matrix: ${{ steps.matrix.outputs.test_matrix }}
      test_pkg_matrix: ${{ steps.matrix.outputs.pkg_matrix }}
      test_angular_pkg_matrix: ${{ steps.matrix.outputs.angular_pkg_matrix }}
      e2e_test_count: ${{ steps.matrix.outputs.e2e_test_count }}
      e2e_test_matrix: ${{ steps.matrix.outputs.e2e_test_matrix }}
      bundle_test_count: ${{ steps.matrix.outputs.bundle_test_count }}
      bundle_test_matrix: ${{ steps.matrix.outputs.bundle_test_matrix }}
      build_type: ${{ steps.setup.outputs.type }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # shallow copy

      - name: Fetch Refs
        run: |
          git fetch origin --depth 1 latest
          git fetch origin --depth 1 tag latest-success

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ${{ github.event.inputs.clean_checkout == 'true' && 'off' || 'rw' }}

      - name: nx format:check
        id: format
        if: steps.setup.outcome == 'success' || steps.setup.outcome == 'skipped'
        run: |
          if [[ "${{ github.event.inputs.nx_command || 'affected' }}" == "run-many" ]] ; then
            yarn nx format:check --all
          else
            yarn nx format:check --base ${{ steps.setup.outputs.base }}
          fi

      - name: nx lint
        id: lint
        run: |
          yarn nx lint

      - name: update base url
        run: node scripts/deployments/prep_and_archive/updateBaseUrl.js staging

      - name: nx build
        id: build
        run: |
          yarn nx generate-doc-references
          yarn nx generate-examples -c staging
          yarn nx run-many --t build -c staging --exclude ag-grid-docs --exclude all

      - name: validate base url
        run: node scripts/deployments/sanityCheckBaseUrl.js staging

      - name: calculate matrix
        id: matrix
        run: |
          if [[ "${{ github.event.inputs.nx_command || 'affected' }}" == "run-many" ]] ; then
            count=10
          else
            count=$(yarn -s nx show projects --affected --base ${{ steps.setup.outputs.base }} -t test | wc -l)
          fi
          
          matrix=$(node ./scripts/test/calculate-shards.js eval --ratio 1 --zero ${count} --max 1)
          echo "test_matrix=${matrix}" >> $GITHUB_OUTPUT
          echo "test_count=${count}" >> $GITHUB_OUTPUT
          echo "Test matrix determined to be: ${matrix}"
          
          if [[ "${{ github.event.inputs.nx_command || 'affected' }}" == "run-many" ]] ; then
            e2e_count=10
          else
            e2e_count=$(yarn -s nx show projects --affected --base ${{ steps.setup.outputs.base }} -t test:e2e --exclude 'tag:module-size' --exclude all | wc -l)
          fi
          
          # not enough non-bundle size e2e tests to shard atm
          e2e_matrix={"shard":[0]}
          echo "e2e_test_matrix=${e2e_matrix}" >> $GITHUB_OUTPUT
          echo "e2e_test_count=${e2e_count}" >> $GITHUB_OUTPUT
          echo "e2e Test matrix determined to be: ${e2e_matrix}"
          
          # 3 is optimal atm for bundle tests
          bundle_count=3
          
          bundle_matrix=$(node ./scripts/test/calculate-shards.js eval --ratio 1 --zero ${bundle_count})
          echo "bundle_test_matrix=${bundle_matrix}" >> $GITHUB_OUTPUT
          echo "bundle_test_count=${bundle_count}" >> $GITHUB_OUTPUT
          echo "bundle Test matrix determined to be: ${bundle_matrix}"

          pkg_matrix=$(node ./external/ag-shared/scripts/shard/calculate-pkg-shards.js)
          echo "pkg_matrix=${pkg_matrix}" >> $GITHUB_OUTPUT
          echo "Package test matrix determined to be: ${pkg_matrix}"

          angular_pkg_matrix=$(node ./testing/angular-package-tests/calculate-angular-pkg-shards.js)
          echo "angular_pkg_matrix=${angular_pkg_matrix}" >> $GITHUB_OUTPUT
          echo "Angular package test matrix determined to be: ${angular_pkg_matrix}"

      - name: Perist build outputs
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: e2e-init-outputs
          path: |
            dist/
            packages/*/dist/
            external/*/dist/
  test:
    runs-on: ubuntu-24.04
    name: Unit Tests (${{ matrix.shard }}/${{ strategy.job-total }})
    needs: build_lint
    if: needs.build_lint.outputs.test_count > 0
    strategy:
      matrix: ${{ fromJson(needs.build_lint.outputs.test_matrix )}}
      fail-fast: false
    env:
      NX_PARALLEL: 1
      NX_BASE: ${{ needs.build_lint.outputs.nx_base }}
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # shallow copy

      - name: Fetch Refs
        run: |
          git fetch origin --depth 1 latest
          git fetch origin --depth 1 tag latest-success

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - name: nx test
        if: matrix.shard != 0
        id: test
        run: yarn nx ${{ github.event.inputs.nx_command || 'affected' }} -t test -c=staging --exclude tag:no-sharding --exclude all --shard=${{ matrix.shard }}/$((${{ strategy.job-total }} - 1))
      - name: nx test (non-sharded)
        if: matrix.shard == 0
        id: test-no-shard
        run: yarn nx ${{ github.event.inputs.nx_command || 'affected' }} -t test -c staging --exclude '*,!tag:no-sharding'
      - name: nx pack:verify
        if: matrix.shard == 0
        id: pack-verify
        run: |
          yarn nx ${{ github.event.inputs.nx_command || 'affected' }} -t pack:verify --prod
      - name: andrew checker
        if: matrix.shard == 0
        id: andrew
        run: |
          ./scripts/andrewChecker.sh
      - name: Persist test results
        if: always() && matrix.shard != 0
        uses: actions/upload-artifact@v4
        with:
          name: test-results-${{matrix.shard}}
          path: |
            reports/
            packages/**/__diff_output__/*

  e2e:
    runs-on: ubuntu-latest
    name: e2e Tests
    needs: build_lint
    if: needs.build_lint.outputs.e2e_test_count > 0
    strategy:
      matrix: ${{ fromJson(needs.build_lint.outputs.e2e_test_matrix )}}
      fail-fast: false
    env:
      NX_PARALLEL: 1
      NX_BASE: ${{ needs.build_lint.outputs.nx_base }}
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # shallow copy

      - name: Restore Init Build Outputs
        uses: actions/download-artifact@v4
        with:
          name: e2e-init-outputs

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - name: nx test:e2e
        id: tests
        run: yarn nx ${{ github.event.inputs.nx_command || 'affected' }} -t test:e2e -c staging --exclude 'tag:module-size' --exclude all

      - name: Persist test results
        if: always() && matrix.shard != 0
        uses: actions/upload-artifact@v4
        with:
          name: test-results-e2e-shard-${{matrix.shard}}
          path: |
            reports/

  bundle_size:
    runs-on: ubuntu-latest
    name: Bundle Size Tests
    needs: build_lint
    if: needs.build_lint.result == 'success' && ${{ github.event.inputs.run_bundle_size_tests }}
    strategy:
      matrix: ${{ fromJson(needs.build_lint.outputs.bundle_test_matrix )}}
      fail-fast: false
    env:
      NX_PARALLEL: 1
      NX_BASE: ${{ needs.build_lint.outputs.nx_base }}
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # shallow copy

      - name: Restore Init Build Outputs
        uses: actions/download-artifact@v4
        with:
          name: e2e-init-outputs

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - name: module-size tests (non-sharded)
        if: matrix.shard == 0
        id: test-no-shard
        run: yarn nx ${{ github.event.inputs.nx_command || 'affected' }} -t test:e2e -c staging --exclude '!tag:module-size' --exclude 'tag:sharding' --exclude all
      - name: module-size tests
        if: matrix.shard != 0
        id: test
        run: yarn nx ${{ github.event.inputs.nx_command || 'affected' }}  -t test:e2e -c staging --exclude '!tag:module-size' --exclude 'tag:non-sharding' --exclude all --shard=${{ matrix.shard }}/$((${{ strategy.job-total }} - 1))

      - name: Persist test results
        if: always() && matrix.shard != 0
        uses: actions/upload-artifact@v4
        with:
          name: test-results-bundle-shard-${{matrix.shard}}
          path: |
            reports/

  docs:
    runs-on: ubuntu-latest
    name: Docs Build & Link Checker
    needs: build_lint
    if: needs.build_lint.result == 'success' && ${{ github.event.inputs.run_docs }}
    strategy:
      fail-fast: false
    env:
      NX_BASE: ${{ needs.build_lint.outputs.nx_base }}
    outputs:
      docs_deployed: ${{ steps.deploy_to_staging.outcome }}
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # shallow copy

      - name: Restore Init Build Outputs
        uses: actions/download-artifact@v4
        with:
          name: e2e-init-outputs

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - name: validate grid modules
        run: node scripts/deployments/validateGridModulesList.js
      - name: docs build and link checker
        run: CHECK_LINKS=true CHECK_REDIRECTS=true yarn nx run ag-grid-docs:build -c staging

      - name: Deploy to Staging
        id: deploy_to_staging
        if: ${{ github.ref == 'refs/heads/latest' }}
        env:
          SSH_PRIVATE_KEY: |
            ${{ secrets.STAGING_SSH_KEY }}
          SSH_HOST: ${{ secrets.STAGING_HOST }}
          SSH_USER: ubuntu
          SSH_KEY_LOCATION: ${{ github.workspace }}/staging.pem
          WWW_ROOT_DIR: ${{ vars.WWW_ROOT_DIR }}
        run: |
          echo "$SSH_PRIVATE_KEY" > $SSH_KEY_LOCATION
          chmod 400 $SSH_KEY_LOCATION
          ./scripts/deployments/createAndDeployDocsToTCGH.sh

      - name: Persist test results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results-docs
          path: |
            reports/

  fw_pkg_test:
    runs-on: ubuntu-24.04
    name: Framework Package Tests (${{ matrix.framework }})
    needs: build_lint
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.build_lint.outputs.test_pkg_matrix )}}
    if: (needs.build_lint.outputs.build_type == 'latest' || needs.build_lint.outputs.build_type == 'release')
    env:
      NX_BASE: ${{ needs.init.outputs.nx_base }}
    steps:
      - name: Checkout
        id: checkout
        if: matrix.framework != 'none'
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Restore Init Build Outputs
        uses: actions/download-artifact@v4
        with:
          name: e2e-init-outputs

      - name: Setup
        id: setup
        if: matrix.framework != 'none'
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - name: nx package:test
        id: package-test
        if: matrix.framework != 'none'
        run: yarn nx run-many -t test:package -p ${{ matrix.framework }}-package-tests -c=staging

  angular_fw_pkg_test:
    runs-on: ubuntu-24.04
    name: Angular Package Tests
    needs: build_lint
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.build_lint.outputs.test_angular_pkg_matrix )}}
    if: (needs.build_lint.outputs.build_type == 'latest' || needs.build_lint.outputs.build_type == 'release')
    env:
      NX_BASE: ${{ needs.init.outputs.nx_base }}
    steps:
      - name: Checkout
        id: checkout
        if: matrix.framework != 'none'
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Restore Init Build Outputs
        uses: actions/download-artifact@v4
        with:
          name: e2e-init-outputs

      - name: Setup
        id: setup
        if: matrix.framework != 'none'
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - name: nx package:test
        id: package-test
        if: matrix.framework != 'none'
        run: yarn nx run-many -t test:package:shard-${{ matrix.shard }} -p angular-package-tests -c staging

  report:
    runs-on: ubuntu-24.04
    needs: [ build_lint, test, e2e, docs, bundle_size, fw_pkg_test, angular_fw_pkg_test ]
    if: cancelled() != true
    permissions:
      contents: write
      actions: read
      checks: write
    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Fetch Refs
        run: |
          git fetch origin --depth 1 latest
          git fetch origin --depth 1 tag latest-success

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: ro

      - uses: actions/download-artifact@v4
        with:
          path: test-results/

      - name: Merge JUnit Report XMLs
        run: |
          yarn global add junit-report-merger
          reports=$(find test-results/ -name \*.xml -type f -exec basename \{\} \; | sort | uniq)
          mkdir -p reports/
          echo "$reports" | (while read name ; do
            yarn exec -s jrm reports/${name} "test-results/**/${name}"
          done)

      - name: Test Report
        uses: dorny/test-reporter@v1
        if: needs.test.result == 'success' || needs.test.result == 'failure' ||
          needs.e2e.result == 'success' || needs.e2e.result == 'failure' ||
          needs.bundle_size.result == 'success' || needs.bundle_size.result == 'failure'
        id: testReport
        continue-on-error: true
        with:
          name: 'Tests Results'
          path: reports/*.xml
          reporter: jest-junit

      - name: Check last job status
        id: lastJobStatus
        if: always()
        run: |
          WORKFLOW_STATUS="success"
          if [[ "${{ needs.build_lint.result }}" == "failure" ]] ; then
            WORKFLOW_STATUS="failure"
          elif [ "${{ needs.test.result }}" == "failure" ] ; then
            WORKFLOW_STATUS="failure"
          elif [ "${{ needs.e2e.result }}" == "failure" ] ; then
            WORKFLOW_STATUS="failure"
          elif [ "${{ needs.docs.result }}" == "failure" ] ; then
            WORKFLOW_STATUS="failure"
          elif [ "${{ needs.fw_pkg_test.result }}" == "failure" ] ; then
            WORKFLOW_STATUS="failure"
          elif [ "${{ needs.angular_fw_pkg_test.result }}" == "failure" ] ; then
            WORKFLOW_STATUS="failure"
          elif [ "${{ needs.bundle_size.result }}" == "failure" ] ; then
            WORKFLOW_STATUS="failure"
          fi
          echo "workflowStatus=${WORKFLOW_STATUS}" >> $GITHUB_OUTPUT

          LAST_WORKFLOW_STATUS=$(gh run list --workflow .github/workflows/ci.yml -b latest | grep -oh "completed.*" | grep -v "cancelled" | head -1 | awk '{print $2}')
          if [ "$GITHUB_RUN_ATTEMPT" -ge 2 ]; then
            # Handle re-run cases - there is no way to query the previous run status, so we assume the most
            # common scenario will be re-run after failure.
            LAST_WORKFLOW_STATUS="failure"
          fi
          if [ "$LAST_WORKFLOW_STATUS" != "$WORKFLOW_STATUS" ]; then
            echo "status changed from $LAST_WORKFLOW_STATUS to $WORKFLOW_STATUS"
            echo "changedState=true" >> $GITHUB_OUTPUT
          else
            echo "status is still $WORKFLOW_STATUS"
            echo "changedState=false" >> $GITHUB_OUTPUT
          fi
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Commit History
        id: commits
        if: always() && job.status != 'cancelled' && github.ref == 'refs/heads/latest' && steps.lastJobStatus.outputs.changedState == 'true'
        run: |
          GIT_LOG=$(git log HEAD ^${{ needs.build_lint.outputs.nx_base }} --format="%an (%h) %s")
          echo "GIT_LOG<<EOF" >> $GITHUB_ENV
          echo "$GIT_LOG" >> $GITHUB_ENV
          echo "EOF" >> $GITHUB_ENV

      - name: Tag Latest Successful Commit
        if: success() && github.ref == 'refs/heads/latest' && steps.lastJobStatus.outputs.workflowStatus != 'failure'
        uses: EndBug/latest-tag@latest
        with:
          ref: latest-success
          description: Latest commit to pass GitHub Actions workflow on latest branch.

      - name: Find matching workflow
        if: always() && job.status != 'cancelled' && github.ref == 'refs/heads/latest'
        id: find_latest_sha
        uses: SamhammerAG/last-successful-build-action@v4
        with:
          branch: "latest"
          workflow: "ci"
          verify: false

      - name: SlackBot
        if: always() && job.status != 'cancelled' && github.ref == 'refs/heads/latest'
        env:
          GRID_TEAM_CITY_CHANNEL: ${{ secrets.GRID_TEAM_CITY_CHANNEL }}
          CHARTS_TEAM_CITY_CHANNEL: ${{ secrets.CHARTS_TEAM_CITY_CHANNEL }}
          WEBSITE_STATUS_CHANNEL: ${{ secrets.WEBSITE_STATUS_CHANNEL }}
          SLACK_BOT_OAUTH_TOKEN: ${{ secrets.SLACK_BOT_OAUTH_TOKEN }}
          SLACK_DEBUG_CHANNEL: ${{ secrets.SLACK_DEBUG_CHANNEL }}
          RUN_CONTEXT: >
            {
              "workflow": "${{ github.workflow }}",
              "ref": "${{ github.ref }}",
              "currentSha": "${{ github.sha }}",
              "lastSuccessfulSha": "${{ steps.find_latest_sha.outputs.sha }}",
              "runId": "${{ github.run_id }}",
              "project": "AgGrid",
              "reportUrl": "${{ steps.testReport.outputs.url_html }}",
              "deployToStaging": ${{ needs.docs.outputs.docs_deployed == 'success' }},
              "changedState": ${{ steps.lastJobStatus.outputs.changedState == 'true' }},
              "jobStatuses":
                {
                    "Build": "${{ needs.build_lint.outputs.build }}",
                    "Lint": "${{ needs.build_lint.outputs.lint }}",
                    "Format": "${{ needs.build_lint.outputs.format }}",
                    "Test": "${{ needs.test.result }}",
                    "e2e": "${{ needs.e2e.result }}",
                    "Docs": "${{ needs.docs.result }}",
                    "Bundle": "${{ needs.bundle_size.result }}",
                    "FW_Pkg": "${{ needs.fw_pkg_test.result }}",
                    "Angular_Pkg": "${{ needs.angular_fw_pkg_test.result }}"
                }
            }
        run: |
          npx ts-node ./scripts/agBotSlackMessage.ts --auth-token "$SLACK_BOT_OAUTH_TOKEN" \
            --grid-channel "$GRID_TEAM_CITY_CHANNEL" \
            --charts-channel "$CHARTS_TEAM_CITY_CHANNEL" \
            --website-status-channel "$WEBSITE_STATUS_CHANNEL" \
            --debug-channel "$SLACK_DEBUG_CHANNEL" \
            --run-context "$RUN_CONTEXT"

      - name: Fail job if workflow failed
        if: success() && steps.lastJobStatus.outputs.workflowStatus == 'failure'
        run: |
            echo "Workflow failed, failing the build."
            exit 1
