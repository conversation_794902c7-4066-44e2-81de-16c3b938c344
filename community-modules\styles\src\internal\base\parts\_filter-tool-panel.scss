@use 'ag';

@mixin output {
    .ag-filter-toolpanel-header {
        height: calc(var(--ag-grid-size) * 6);
    }

    .ag-filter-toolpanel-header,
    .ag-filter-toolpanel-search {
        padding: 0 var(--ag-grid-size);
    }

    @include ag.keyboard-focus((ag-filter-toolpanel-header), 4px);
    .ag-filter-toolpanel-group:not(.ag-has-filter)
        > .ag-group-title-bar
        .ag-filter-toolpanel-group-instance-header-icon {
        display: none;
    }

    .ag-filter-toolpanel-group-level-0-header {
        height: calc(var(--ag-grid-size) * 8);
    }

    .ag-filter-toolpanel-group-item {
        margin-top: calc(var(--ag-grid-size) * 0.5);
        margin-bottom: calc(var(--ag-grid-size) * 0.5);
    }

    .ag-filter-toolpanel-search {
        height: var(--ag-header-height);
    }

    .ag-filter-toolpanel-search-input {
        flex-grow: 1;
        height: calc(var(--ag-grid-size) * 4);
        @include ag.unthemed-rtl(
            (
                margin-right: var(--ag-grid-size),
            )
        );
    }

    .ag-filter-toolpanel-group-level-0 {
        border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
    }

    .ag-filter-toolpanel-expand,
    .ag-filter-toolpanel-group-title-bar-icon {
        @include ag.unthemed-rtl(
            (
                margin-right: var(--ag-grid-size),
            )
        );
    }

    .ag-filter-toolpanel-group-title-bar {
        background-color: transparent;
    }

    .ag-filter-toolpanel-header {
        @include ag.unthemed-rtl(
            (
                padding-left:
                    calc(
                        var(--ag-filter-tool-panel-group-indent) * var(--ag-indentation-level, 0) + var(--ag-grid-size)
                    ),
            )
        );
    }

    .ag-filter-toolpanel-instance-filter {
        border-bottom: var(--ag-borders) var(--ag-border-color);
        border-top: var(--ag-borders) var(--ag-border-color);
        margin-top: var(--ag-grid-size);
    }

    .ag-filter-toolpanel-group-instance-header-icon,
    .ag-filter-toolpanel-instance-header-icon {
        @include ag.unthemed-rtl(
            (
                margin-left: var(--ag-grid-size),
            )
        );
    }

    .ag-set-filter-group-icons {
        color: var(--ag-secondary-foreground-color);
    }

    // new filter tool panel

    .ag-filter-panel {
        display: flex;
        flex-direction: column;
        width: 100%;

        .ag-simple-filter-body-wrapper {
            padding: var(--ag-widget-vertical-spacing) var(--ag-widget-container-horizontal-padding) 0;
        }

        .ag-mini-filter {
            margin-top: var(--ag-widget-vertical-spacing);
            margin-left: var(--ag-widget-container-horizontal-padding);
            margin-right: var(--ag-widget-container-horizontal-padding);
        }

        .ag-standard-button {
            transition:
                background-color 0.25s ease-in-out,
                color 0.25s ease-in-out;
        }

        .ag-simple-filter-body-wrapper > *:last-child,
        .ag-set-filter-body-wrapper {
            margin-bottom: var(--ag-widget-container-vertical-padding);
        }
    }

    .ag-filter-panel-container {
        flex: 1;
        overflow: auto;
        padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding) 0;
    }

    .ag-filter-panel-container > *:not(:last-child) {
        margin-bottom: var(--ag-widget-container-vertical-padding);
    }

    .ag-filter-card {
        border: 1px solid var(--ag-border-color);
        border-radius: var(--ag-border-radius);
        background-color: var(--ag-background-color);

        .ag-set-filter-item {
            @include ag.unthemed-rtl(
                (
                    padding-left:
                        calc(
                            var(--ag-widget-container-horizontal-padding) + var(--ag-indentation-level) *
                                var(--ag-set-filter-indent-size)
                        ),
                    padding-right: var(--ag-widget-container-horizontal-padding),
                )
            );
        }
    }

    .ag-filter-card-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding-top: var(--ag-widget-vertical-spacing);

        & > *:not(:last-child) {
            @include ag.unthemed-rtl(
                (
                    padding-right: var(--ag-grid-size),
                )
            );
        }
    }

    .ag-filter-card-heading {
        flex: 1;
        overflow: hidden;
        padding-top: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
        padding-bottom: calc(var(--ag-widget-container-vertical-padding) - var(--ag-widget-vertical-spacing));
        @include ag.unthemed-rtl(
            (
                padding-left: var(--ag-widget-horizontal-spacing),
            )
        );
    }

    .ag-filter-card-expand {
        display: flex;
        flex-direction: row;
        width: 100%;
        justify-content: space-between;
        align-items: center;
    }

    .ag-filter-card-title {
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .ag-filter-card-expand-icon {
        display: flex;
        flex: 1;
        justify-content: end;
    }

    .ag-filter-card-editing-icon {
        margin: 0 var(--ag-grid-size);
    }

    .ag-filter-card-summary,
    .ag-filter-card-expand-icon .ag-icon,
    .ag-filter-card-delete-icon .ag-icon,
    .ag-filter-card-editing-icon .ag-icon {
        color: var(--ag-filter-panel-card-subtle-color);
    }

    .ag-filter-card-expand-icon .ag-icon,
    .ag-filter-card-delete-icon .ag-icon {
        transition: color 0.25s ease-in-out;
    }

    .ag-filter-card-expand-icon:hover .ag-icon,
    .ag-filter-card-delete-icon:hover .ag-icon {
        color: var(--ag-filter-panel-card-subtle-hover-color);
    }

    .ag-filter-card-heading:hover .ag-filter-card-expand-icon .ag-icon {
        color: var(--ag-filter-panel-card-subtle-hover-color);
    }

    .ag-filter-card-expand,
    .ag-filter-card-delete {
        border-radius: var(--ag-button-border-radius);
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
    }

    .ag-filter-panel .ag-standard-button {
        cursor: pointer;
    }

    .ag-filter-card-summary,
    .ag-filter-type-select {
        margin-left: var(--ag-widget-container-horizontal-padding);
        margin-right: var(--ag-widget-container-horizontal-padding);
    }

    .ag-filter-card-delete {
        @include ag.unthemed-rtl(
            (
                margin-right: var(--ag-widget-horizontal-spacing),
            )
        );
    }

    .ag-filter-card-summary {
        margin-bottom: var(--ag-widget-container-vertical-padding);
    }

    .ag-filter-type-select {
        padding-top: var(--ag-widget-vertical-spacing);
    }

    .ag-filter-card-add {
        padding: 0;
        border: 0;
    }

    .ag-filter-add-button {
        display: flex;
        flex-direction: row;
        width: 100%;
        align-items: center;
        line-height: 1.5;
    }

    .ag-filter-add-button-label {
        @include ag.unthemed-rtl(
            (
                margin-left: var(--ag-grid-size),
            )
        );
    }

    .ag-filter-add-select {
        border: 0;

        .ag-text-field-input {
            @include ag.unthemed-rtl(
                (
                    padding-left: calc(var(--ag-grid-size) * 1.5 + 12px) !important,
                )
            );
        }

        .ag-rich-select-value {
            padding: calc(((1.5 * var(--ag-font-size) + 4 * var(--ag-grid-size)) - var(--ag-input-height)) / 2)
                var(--ag-grid-size);
            border: 0;
        }
    }
}

.ag-filter-panel-buttons {
    display: flex;
    justify-content: flex-end;
    overflow: hidden;
    padding: var(--ag-widget-container-vertical-padding) var(--ag-widget-container-horizontal-padding) 0;
    flex-wrap: wrap;
    gap: var(--ag-widget-vertical-spacing) var(--ag-widget-horizontal-spacing);
}

.ag-filter-panel-buttons-button {
    line-height: 1.5;
}

.ag-filter-panel .ag-standard-button.ag-filter-panel-buttons-apply-button {
    color: var(--ag-filter-panel-apply-button-color);
    background-color: var(--ag-filter-panel-apply-button-background-color);
}

.ag-filter-panel > *:where(:last-child) {
    padding-bottom: var(--ag-widget-container-vertical-padding);
}
