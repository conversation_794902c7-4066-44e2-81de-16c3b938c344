/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_PL = {
    // Set Filter
    selectAll: '(Zaznacz wszystko)',
    selectAllSearchResults: '(Zaznacz wszystkie wyniki wyszukiwania)',
    addCurrentSelectionToFilter: 'Dodaj bie<PERSON>cy wybór do filtra',
    searchOoo: 'Szukaj...',
    blanks: '(<PERSON>uste)',
    noMatches: 'Brak wyników',

    // Number Filter & Text Filter
    filterOoo: 'Filtruj...',
    equals: 'Równa się',
    notEqual: 'Nie równa się',
    blank: 'Puste',
    notBlank: 'Niepuste',
    empty: 'Wybierz jeden',

    // Number Filter
    lessThan: 'Mniej niż',
    greaterThan: 'Wię<PERSON>j niż',
    lessThanOrEqual: '<PERSON><PERSON>j lub równe',
    greaterThanOrEqual: 'Więcej lub równe',
    inRange: 'Pomiędzy',
    inRangeStart: 'Od',
    inRangeEnd: 'Do',

    // Text Filter
    contains: 'Zawiera',
    notContains: 'Nie zawiera',
    startsWith: 'Zaczyna się od',
    endsWith: 'Kończy się na',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'Przed',
    after: 'Po',

    // Filter Conditions
    andCondition: 'I',
    orCondition: 'LUB',

    // Filter Buttons
    applyFilter: 'Zastosuj',
    resetFilter: 'Resetuj',
    clearFilter: 'Wyczyść',
    cancelFilter: 'Anuluj',

    // Filter Titles
    textFilter: 'Filtr tekstu',
    numberFilter: 'Filtr liczb',
    dateFilter: 'Filtr dat',
    setFilter: 'Filtr zbioru',

    // Group Column Filter
    groupFilterSelect: 'Wybierz pole:',

    // New Filter Tool Panel
    filterSummaryInactive: 'jest (Wszystkie)',
    filterSummaryContains: 'zawiera',
    filterSummaryNotContains: 'nie zawiera',
    filterSummaryTextEquals: 'jest równe',
    filterSummaryTextNotEqual: 'nie jest równe',
    filterSummaryStartsWith: 'zaczyna się od',
    filterSummaryEndsWith: 'kończy się na',
    filterSummaryBlank: 'jest puste',
    filterSummaryNotBlank: 'nie jest puste',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'pomiędzy',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'jest (Wszystkie)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'jest (${variable})',
    filterSummaryListLong: 'jest (${variable}) i ${variable} więcej',
    addFilterCard: 'Dodaj filtr',
    agTextColumnFilterDisplayName: 'Prosty filtr',
    agNumberColumnFilterDisplayName: 'Prosty filtr',
    agDateColumnFilterDisplayName: 'Prosty filtr',
    agSetColumnFilterDisplayName: 'Filtr wyboru',
    agMultiColumnFilterDisplayName: 'Filtr złożony',
    addFilterPlaceholder: 'Szukaj w kolumnach...',

    // Advanced Filter
    advancedFilterContains: 'zawiera',
    advancedFilterNotContains: 'nie zawiera',
    advancedFilterTextEquals: 'równa się',
    advancedFilterTextNotEqual: 'nie równa się',
    advancedFilterStartsWith: 'zaczyna się od',
    advancedFilterEndsWith: 'kończy się na',
    advancedFilterBlank: 'jest puste',
    advancedFilterNotBlank: 'nie jest puste',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'jest prawdą',
    advancedFilterFalse: 'jest fałszem',
    advancedFilterAnd: 'AND',
    advancedFilterOr: 'OR',
    advancedFilterApply: 'Zastosuj',
    advancedFilterBuilder: 'Kreator',
    advancedFilterValidationMissingColumn: 'Brak kolumny',
    advancedFilterValidationMissingOption: 'Brak opcji',
    advancedFilterValidationMissingValue: 'Brak wartości',
    advancedFilterValidationInvalidColumn: 'Kolumna nie istnieje',
    advancedFilterValidationInvalidOption: 'Opcja nie istnieje',
    advancedFilterValidationMissingQuote: 'Brak końcowego cudzysłowu',
    advancedFilterValidationNotANumber: 'Wartość nie jest liczbą',
    advancedFilterValidationInvalidDate: 'Wartość nie jest prawidłową datą',
    advancedFilterValidationMissingCondition: 'Brak warunku',
    advancedFilterValidationJoinOperatorMismatch: 'Operatory łączenia w ramach warunku muszą być takie same',
    advancedFilterValidationInvalidJoinOperator: 'Operator łączenia nie istnieje',
    advancedFilterValidationMissingEndBracket: 'Brak końcowego nawiasu',
    advancedFilterValidationExtraEndBracket: 'Za dużo końcowych nawiasów',
    advancedFilterValidationMessage: 'Wyrażenie zawiera błąd. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'Wyrażenie zawiera błąd. ${variable} na końcu wyrażenia.',
    advancedFilterBuilderTitle: 'Zaawansowany filtr',
    advancedFilterBuilderApply: 'Zastosuj',
    advancedFilterBuilderCancel: 'Anuluj',
    advancedFilterBuilderAddButtonTooltip: 'Dodaj filtr lub grupę',
    advancedFilterBuilderRemoveButtonTooltip: 'Usuń',
    advancedFilterBuilderMoveUpButtonTooltip: 'Przenieś w górę',
    advancedFilterBuilderMoveDownButtonTooltip: 'Przenieś w dół',
    advancedFilterBuilderAddJoin: 'Dodaj grupę',
    advancedFilterBuilderAddCondition: 'Dodaj filtr',
    advancedFilterBuilderSelectColumn: 'Wybierz kolumnę',
    advancedFilterBuilderSelectOption: 'Wybierz opcję',
    advancedFilterBuilderEnterValue: 'Wpisz wartość...',
    advancedFilterBuilderValidationAlreadyApplied: 'Obecny filtr już zastosowany.',
    advancedFilterBuilderValidationIncomplete: 'Nie wszystkie warunki są kompletne.',
    advancedFilterBuilderValidationSelectColumn: 'Musisz wybrać kolumnę.',
    advancedFilterBuilderValidationSelectOption: 'Musisz wybrać opcję.',
    advancedFilterBuilderValidationEnterValue: 'Musisz wpisać wartość.',

    // Editor Validation Errors
    minDateValidation: 'Data musi być po ${variable}',
    maxDateValidation: 'Data musi być przed ${variable}',
    maxLengthValidation: 'Musi mieć ${variable} znaków lub mniej.',
    minValueValidation: 'Musi być większa lub równa ${variable}',
    maxValueValidation: 'Musi być mniejsza lub równa ${variable}',
    invalidSelectionValidation: 'Nieprawidłowy wybór.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'Kolumny',
    filters: 'Filtry',

    // columns tool panel
    pivotMode: 'Tryb przestawny',
    groups: 'Grupy wierszy',
    rowGroupColumnsEmptyMessage: 'Przeciągnij tutaj, aby ustawić grupy wierszy',
    values: 'Wartości',
    valueColumnsEmptyMessage: 'Przeciągnij tutaj, aby agregować',
    pivots: 'Etykiety kolumn',
    pivotColumnsEmptyMessage: 'Przeciągnij tutaj, aby ustawić etykiety kolumn',

    // Header of the Default Group Column
    group: 'Grupa',

    // Row Drag
    rowDragRow: 'wiersz',
    rowDragRows: 'wiersze',

    // Other
    loadingOoo: 'Ładowanie...',
    loadingError: 'BŁĄD',
    noRowsToShow: 'Brak wierszy do wyświetlenia',
    enabled: 'Włączone',

    // Menu
    pinColumn: 'Przypnij Kolumnę',
    pinLeft: 'Przypnij z Lewa',
    pinRight: 'Przypnij z Prawa',
    noPin: 'Nie Przypinaj',
    valueAggregation: 'Agregacja Wartości',
    noAggregation: 'Brak',
    autosizeThisColumn: 'Autozmiar tej Kolumny',
    autosizeAllColumns: 'Autozmiar wszystkich Kolumn',
    groupBy: 'Grupuj według',
    ungroupBy: 'Rozgrupuj według',
    ungroupAll: 'Rozgrupuj Wszystko',
    addToValues: 'Dodaj ${variable} do wartości',
    removeFromValues: 'Usuń ${variable} z wartości',
    addToLabels: 'Dodaj ${variable} do etykiet',
    removeFromLabels: 'Usuń ${variable} z etykiet',
    resetColumns: 'Zresetuj Kolumny',
    expandAll: 'Rozwiń Wszystkie Grupy Wierszy',
    collapseAll: 'Zwiń Wszystkie Grupy Wierszy',
    copy: 'Kopiuj',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'Kopiuj z Nagłówkami',
    copyWithGroupHeaders: 'Kopiuj z Nagłówkami Grup',
    cut: 'Wytnij',
    paste: 'Wklej',
    ctrlV: 'Ctrl+V',
    export: 'Eksportuj',
    csvExport: 'Eksport CSV',
    excelExport: 'Eksport Excel',
    columnFilter: 'Filtr Kolumny',
    columnChooser: 'Wybierz Kolumny',
    chooseColumns: 'Wybierz kolumny',
    sortAscending: 'Sortuj Rosnąco',
    sortDescending: 'Sortuj Malejąco',
    sortUnSort: 'Usuń Sortowanie',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'Suma',
    first: 'Pierwszy',
    last: 'Ostatni',
    min: 'Min',
    max: 'Maks',
    none: 'Brak',
    count: 'Liczba',
    avg: 'Średnia',
    filteredRows: 'Filtrowane',
    selectedRows: 'Wybrane',
    totalRows: 'Całkowita liczba wierszy',
    totalAndFilteredRows: 'Wiersze',
    more: 'Więcej',
    to: 'do',
    of: 'z',
    page: 'Strona',
    pageLastRowUnknown: '?',
    nextPage: 'Następna Strona',
    lastPage: 'Ostatnia Strona',
    firstPage: 'Pierwsza Strona',
    previousPage: 'Poprzednia Strona',
    pageSizeSelectorLabel: 'Rozmiar strony:',
    footerTotal: 'Razem',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'Przewiń ${variable} do widoku',

    // Pivoting
    pivotColumnGroupTotals: 'Suma',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'Pivotowy wykres i tryb przestawny',
    pivotChart: 'Wykres przestawny',
    chartRange: 'Zakres danych wykresu',
    columnChart: 'Kolumnowy',
    groupedColumn: 'Grupowany',
    stackedColumn: 'Skumulowany',
    normalizedColumn: '100% Skumulowany',
    barChart: 'Słupkowy',
    groupedBar: 'Grupowany',
    stackedBar: 'Skumulowany',
    normalizedBar: '100% Skumulowany',
    pieChart: 'Kołowy',
    pie: 'Kołowy',
    donut: 'Pierścieniowy',
    lineChart: 'Linia',
    stackedLine: 'Skumulowana',
    normalizedLine: '100% Skumulowana',
    xyChart: 'XY (Punktowy)',
    scatter: 'Punktowy',
    bubble: 'Bąbelkowy',
    areaChart: 'Obszarowy',
    area: 'Obszarowy',
    stackedArea: 'Skumulowany',
    normalizedArea: '100% Skumulowany',
    histogramChart: 'Histogram',
    polarChart: 'Polarny',
    radarLine: 'Liniowy radarowy',
    radarArea: 'Obszarowy radarowy',
    nightingale: 'Nightingale',
    radialColumn: 'Kolumnowy radialny',
    radialBar: 'Słupkowy radialny',
    statisticalChart: 'Statystyczny',
    boxPlot: 'Wykres ramkowy',
    rangeBar: 'Zakres słupkowy',
    rangeArea: 'Zakres obszarowy',
    hierarchicalChart: 'Hierarchiczny',
    treemap: 'Mapa drzewa',
    sunburst: 'Słoneczny',
    specializedChart: 'Specjalizowany',
    waterfall: 'Kaskadowy',
    heatmap: 'Mapa cieplna',
    combinationChart: 'Połączony',
    columnLineCombo: 'Kolumna i linia',
    AreaColumnCombo: 'Obszar i kolumna',

    // Charts
    pivotChartTitle: 'Wykres przestawny',
    rangeChartTitle: 'Wykres zakresu',
    settings: 'Wykres',
    data: 'Ustawienia',
    format: 'Dostosuj',
    categories: 'Kategorie',
    defaultCategory: '(Brak)',
    series: 'Serie',
    switchCategorySeries: 'Przełącz kategorię / serię',
    categoryValues: 'Wartości kategorii',
    seriesLabels: 'Etykiety serii',
    aggregate: 'Agreguj',
    xyValues: 'Wartości XY',
    paired: 'Tryb parowania',
    axis: 'Oś',
    xAxis: 'Oś pozioma',
    yAxis: 'Oś pionowa',
    polarAxis: 'Oś polarna',
    radiusAxis: 'Oś promieniowa',
    navigator: 'Nawigator',
    zoom: 'Powiększenie',
    animation: 'Animacja',
    crosshair: 'Krzyżyk',
    color: 'Kolor',
    thickness: 'Grubość',
    preferredLength: 'Preferowana długość',
    xType: 'Typ X',
    axisType: 'Typ osi',
    automatic: 'Automatyczne',
    category: 'Kategoria',
    number: 'Liczba',
    time: 'Czas',
    timeFormat: 'Format czasu',
    autoRotate: 'Auto obrót',
    labelRotation: 'Obrót etykiety',
    circle: 'Okrąg',
    polygon: 'Wielokąt',
    square: 'Kwadrat',
    cross: 'Krzyżyk',
    diamond: 'Romb',
    plus: 'Plus',
    triangle: 'Trójkąt',
    heart: 'Serce',
    orientation: 'Orientacja',
    fixed: 'Stała',
    parallel: 'Równoległa',
    perpendicular: 'Prostopadła',
    radiusAxisPosition: 'Pozycja',
    ticks: 'Znaczniki',
    gridLines: 'Linie siatki',
    width: 'Szerokość',
    height: 'Wysokość',
    length: 'Długość',
    padding: 'Margines',
    spacing: 'Odstępy',
    chartStyle: 'Styl wykresu',
    title: 'Tytuł',
    chartTitles: 'Tytuły',
    chartTitle: 'Tytuł wykresu',
    chartSubtitle: 'Podtytuł',
    horizontalAxisTitle: 'Tytuł osi poziomej',
    verticalAxisTitle: 'Tytuł osi pionowej',
    polarAxisTitle: 'Tytuł osi polarnej',
    titlePlaceholder: 'Tytuł wykresu',
    background: 'Tło',
    font: 'Czcionka',
    weight: 'Waga',
    top: 'Góra',
    right: 'Prawo',
    bottom: 'Dół',
    left: 'Lewo',
    labels: 'Etykiety',
    calloutLabels: 'Etykiety wywołań',
    sectorLabels: 'Etykiety sektorowe',
    positionRatio: 'Stosunek pozycji',
    size: 'Rozmiar',
    shape: 'Kształt',
    minSize: 'Minimalny rozmiar',
    maxSize: 'Maksymalny rozmiar',
    legend: 'Legenda',
    position: 'Pozycja',
    markerSize: 'Rozmiar znacznika',
    markerStroke: 'Obrys znacznika',
    markerPadding: 'Margines znacznika',
    itemSpacing: 'Odstęp przedmiotu',
    itemPaddingX: 'Margines przedmiotu X',
    itemPaddingY: 'Margines przedmiotu Y',
    layoutHorizontalSpacing: 'Odstęp poziomy',
    layoutVerticalSpacing: 'Odstęp pionowy',
    strokeWidth: 'Szerokość linii',
    offset: 'Przesunięcie',
    offsets: 'Przesunięcia',
    tooltips: 'Podpowiedzi',
    callout: 'Wywołanie',
    markers: 'Znaczniki',
    shadow: 'Cień',
    blur: 'Rozmycie',
    xOffset: 'Przesunięcie X',
    yOffset: 'Przesunięcie Y',
    lineWidth: 'Szerokość linii',
    lineDash: 'Kreskowanie',
    lineDashOffset: 'Przesunięcie kreskowania',
    scrollingZoom: 'Powiększenie przewijania',
    scrollingStep: 'Krok przewijania',
    selectingZoom: 'Powiększenie wybrania',
    durationMillis: 'Czas trwania (ms)',
    crosshairLabel: 'Etykieta krzyżyka',
    crosshairSnap: 'Przyciąganie do węzła',
    normal: 'Normalny',
    bold: 'Pogrubiony',
    italic: 'Kursywa',
    boldItalic: 'Pogrubiona kursywa',
    predefined: 'Zdefiniowane',
    fillOpacity: 'Przezroczystość wypełnienia',
    strokeColor: 'Kolor linii',
    strokeOpacity: 'Przezroczystość linii',
    miniChart: 'Mini-wykres',
    histogramBinCount: 'Liczba koszy',
    connectorLine: 'Linia łącząca',
    seriesItems: 'Elementy serii',
    seriesItemType: 'Typ elementu',
    seriesItemPositive: 'Pozytywne',
    seriesItemNegative: 'Negatywne',
    seriesItemLabels: 'Etykiety elementów',
    columnGroup: 'Kolumna',
    barGroup: 'Słupek',
    pieGroup: 'Wykres kołowy',
    lineGroup: 'Linia',
    scatterGroup: 'XY (Punktowy)',
    areaGroup: 'Powierzchnia',
    polarGroup: 'Polarny',
    statisticalGroup: 'Statystyczny',
    hierarchicalGroup: 'Hierarchiczny',
    specializedGroup: 'Specjalistyczny',
    combinationGroup: 'Kombinacja',
    groupedColumnTooltip: 'Grupowane',
    stackedColumnTooltip: 'Skumulowane',
    normalizedColumnTooltip: '100% skumulowane',
    groupedBarTooltip: 'Grupowane',
    stackedBarTooltip: 'Skumulowane',
    normalizedBarTooltip: '100% skumulowane',
    pieTooltip: 'Wykres kołowy',
    donutTooltip: 'Pierścieniowy',
    lineTooltip: 'Linia',
    stackedLineTooltip: 'Skumulowany',
    normalizedLineTooltip: '100% Skumulowany',
    groupedAreaTooltip: 'Powierzchnia',
    stackedAreaTooltip: 'Skumulowana',
    normalizedAreaTooltip: '100% skumulowana',
    scatterTooltip: 'Punktowy',
    bubbleTooltip: 'Bąbelkowy',
    histogramTooltip: 'Histogram',
    radialColumnTooltip: 'Kolumna radialna',
    radialBarTooltip: 'Słupek radialny',
    radarLineTooltip: 'Linia radarowa',
    radarAreaTooltip: 'Powierzchnia radarowa',
    nightingaleTooltip: 'Nightingale',
    rangeBarTooltip: 'Słupek zakresu',
    rangeAreaTooltip: 'Powierzchnia zakresu',
    boxPlotTooltip: 'Wykres ramkowy',
    treemapTooltip: 'Mapa drzewa',
    sunburstTooltip: 'Sunburst',
    waterfallTooltip: 'Kaskadowy',
    heatmapTooltip: 'Mapa cieplna',
    columnLineComboTooltip: 'Kolumna i linia',
    areaColumnComboTooltip: 'Powierzchnia i kolumna',
    customComboTooltip: 'Własna kombinacja',
    innerRadius: 'Promień wewnętrzny',
    startAngle: 'Kąt początkowy',
    endAngle: 'Kąt końcowy',
    reverseDirection: 'Odwróć kierunek',
    groupPadding: 'Margines grupy',
    seriesPadding: 'Margines serii',
    tile: 'Kafel',
    whisker: 'Wąs',
    cap: 'Kapturek',
    capLengthRatio: 'Stosunek długości',
    labelPlacement: 'Umiejscowienie etykiety',
    inside: 'Wewnątrz',
    outside: 'Na zewnątrz',
    noDataToChart: 'Brak danych do narysowania.',
    pivotChartRequiresPivotMode: 'Wykres przestawny wymaga włączenia trybu przestawnego.',
    chartSettingsToolbarTooltip: 'Menu',
    chartLinkToolbarTooltip: 'Połączony z siatką',
    chartUnlinkToolbarTooltip: 'Odłączony od siatki',
    chartDownloadToolbarTooltip: 'Pobierz wykres',
    chartMenuToolbarTooltip: 'Menu',
    chartEdit: 'Edytuj wykres',
    chartAdvancedSettings: 'Zaawansowane ustawienia',
    chartLink: 'Połącz z siatką',
    chartUnlink: 'Odłącz od siatki',
    chartDownload: 'Pobierz wykres',
    histogramFrequency: 'Częstotliwość',
    seriesChartType: 'Typ wykresu serii',
    seriesType: 'Typ serii',
    secondaryAxis: 'Druga oś',
    seriesAdd: 'Dodaj serię',
    categoryAdd: 'Dodaj kategorię',
    bar: 'Słupek',
    column: 'Kolumna',
    histogram: 'Histogram',
    advancedSettings: 'Zaawansowane ustawienia',
    direction: 'Kierunek',
    horizontal: 'Poziomy',
    vertical: 'Pionowy',
    seriesGroupType: 'Typ grupy',
    groupedSeriesGroupType: 'Grupowane',
    stackedSeriesGroupType: 'Skumulowane',
    normalizedSeriesGroupType: '100% skumulowane',
    legendEnabled: 'Włączone',
    invalidColor: 'Błędna wartość koloru',
    groupedColumnFull: 'Grupowana kolumna',
    stackedColumnFull: 'Skumulowana kolumna',
    normalizedColumnFull: '100% skumulowana kolumna',
    groupedBarFull: 'Grupowany słupek',
    stackedBarFull: 'Skumulowany słupek',
    normalizedBarFull: '100% skumulowany słupek',
    stackedAreaFull: 'Skumulowana powierzchnia',
    normalizedAreaFull: '100% skumulowana powierzchnia',
    customCombo: 'Własna kombinacja',
    funnel: 'Lejek',
    coneFunnel: 'Lejek Stożkowy',
    pyramid: 'Piramida',
    funnelGroup: 'Lejek',
    funnelTooltip: 'Lejek',
    coneFunnelTooltip: 'Lejek Stożkowy',
    pyramidTooltip: 'Piramida',
    dropOff: 'Spadek',
    stageLabels: 'Etykiety Etapu',
    reverse: 'Odwróć',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. Poziom ${variable}. Naciśnij ENTER, aby edytować',
    ariaAdvancedFilterBuilderItemValidation:
        '${variable}. Poziom ${variable}. ${variable} Naciśnij ENTER, aby edytować.',
    ariaAdvancedFilterBuilderList: 'Zaawansowana Lista Kreatora Filtrów',
    ariaAdvancedFilterBuilderFilterItem: 'Warunek Filtra',
    ariaAdvancedFilterBuilderGroupItem: 'Grupa Filtrów',
    ariaAdvancedFilterBuilderColumn: 'Kolumna',
    ariaAdvancedFilterBuilderOption: 'Opcja',
    ariaAdvancedFilterBuilderValueP: 'Wartość',
    ariaAdvancedFilterBuilderJoinOperator: 'Operator Połączenia',
    ariaAdvancedFilterInput: 'Zaawansowane Wejście Filtra',
    ariaChecked: 'zaznaczone',
    ariaColumn: 'Kolumna',
    ariaColumnGroup: 'Grupa Kolumn',
    ariaColumnFiltered: 'Kolumna Filtrowana',
    ariaColumnSelectAll: 'Przełącz widoczność wszystkich kolumn',
    ariaDateFilterInput: 'Wejście Filtra Daty',
    ariaDefaultListName: 'Lista',
    ariaFilterColumnsInput: 'Wejście Filtrowania Kolumn',
    ariaFilterFromValue: 'Filtr od wartości',
    ariaFilterInput: 'Wejście Filtra',
    ariaFilterList: 'Lista Filtrowania',
    ariaFilterToValue: 'Filtr do wartości',
    ariaFilterValue: 'Wartość Filtra',
    ariaFilterMenuOpen: 'Otwórz Menu Filtrów',
    ariaFilteringOperator: 'Operator Filtrowania',
    ariaHidden: 'ukryte',
    ariaIndeterminate: 'nieokreślone',
    ariaInputEditor: 'Edytor Wejścia',
    ariaMenuColumn: 'Naciśnij ALT w dół, aby otworzyć menu kolumny',
    ariaFilterColumn: 'Naciśnij CTRL ENTER, aby otworzyć filtr',
    ariaRowDeselect: 'Naciśnij SPACJĘ, aby odznaczyć ten wiersz',
    ariaHeaderSelection: 'Kolumna z wyborem nagłówka',
    ariaSelectAllCells: 'Naciśnij Spację, aby zaznaczyć wszystkie komórki',
    ariaRowSelectAll: 'Naciśnij SPACJĘ, aby przełączyć zaznaczenie wszystkich wierszy',
    ariaRowToggleSelection: 'Naciśnij SPACJĘ, aby przełączyć zaznaczenie wiersza',
    ariaRowSelect: 'Naciśnij SPACJĘ, aby zaznaczyć ten wiersz',
    ariaRowSelectionDisabled: 'Zaznaczenie wiersza jest wyłączone dla tego wiersza',
    ariaSearch: 'Szukaj',
    ariaSortableColumn: 'Naciśnij ENTER, aby sortować',
    ariaToggleVisibility: 'Naciśnij SPACJĘ, aby przełączyć widoczność',
    ariaToggleCellValue: 'Naciśnij SPACJĘ, aby przełączyć wartość komórki',
    ariaUnchecked: 'niezaznaczone',
    ariaVisible: 'widoczny',
    ariaSearchFilterValues: 'Szukaj wartości filtrów',
    ariaPageSizeSelectorLabel: 'Rozmiar Strony',
    ariaChartMenuClose: 'Zamknij Menu Edycji Wykresu',
    ariaChartSelected: 'Wybrane',
    ariaSkeletonCellLoadingFailed: 'Wiersz nie udało się załadować',
    ariaSkeletonCellLoading: 'Dane wiersza ładują się',
    ariaDeferSkeletonCellLoading: 'Komórka się ładuje',

    // ARIA for Batch Edit
    ariaPendingChange: 'Zmiana oczekująca',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'Grupy wierszy',
    ariaValuesDropZonePanelLabel: 'Wartości',
    ariaPivotDropZonePanelLabel: 'Etykiety kolumn',
    ariaDropZoneColumnComponentDescription: 'Naciśnij DELETE, aby usunąć',
    ariaDropZoneColumnValueItemDescription: 'Naciśnij ENTER, aby zmienić typ agregacji',
    ariaDropZoneColumnGroupItemDescription: 'Naciśnij ENTER, aby sortować',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' z ',
    ariaDropZoneColumnComponentSortAscending: 'rosnąco',
    ariaDropZoneColumnComponentSortDescending: 'malejąco',
    ariaLabelDialog: 'Dialog',
    ariaLabelColumnMenu: 'Menu kolumny',
    ariaLabelColumnFilter: 'Filtr kolumny',
    ariaLabelSelectField: 'Wybierz pole',

    // Cell Editor
    ariaLabelCellEditor: 'Edytor komórek',
    ariaValidationErrorPrefix: 'Walidacja edytora komórek',
    ariaLabelLoadingContextMenu: 'Ładowanie menu kontekstowego',

    // aria labels for rich select
    ariaLabelRichSelectField: 'Pole z wyborem zaawansowanym',
    ariaLabelRichSelectToggleSelection: 'Naciśnij SPACJĘ, aby przełączyć wybór',
    ariaLabelRichSelectDeselectAllItems: 'Naciśnij DELETE, aby odznaczyć wszystkie elementy',
    ariaLabelRichSelectDeleteSelection: 'Naciśnij DELETE, aby odznaczyć element',
    ariaLabelTooltip: 'Etykieta',
    ariaLabelContextMenu: 'Menu kontekstowe',
    ariaLabelSubMenu: 'Podmenu',
    ariaLabelAggregationFunction: 'Funkcja agregacji',
    ariaLabelAdvancedFilterAutocomplete: 'Zaawansowane Autouzupełnianie Filtrów',
    ariaLabelAdvancedFilterBuilderAddField: 'Zaawansowany Kreator Filtrów Dodaj Pole',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'Zaawansowany Kreator Filtrów Wybór Kolumny',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'Zaawansowany Kreator Filtrów Wybór Opcji',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'Zaawansowany Kreator Filtrów Wybór Operatora Łączenia',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'Lista kolumn',
    ariaFilterPanelList: 'Lista filtrów',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'Dodaj pole filtru',
    ariaLabelFilterCardDelete: 'Usuń filtr',
    ariaLabelFilterCardHasEdits: 'Ma edycje',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'Prawda',
    false: 'Fałsz',
    invalidDate: 'Nieprawidłowa Data',
    invalidNumber: 'Nieprawidłowy Numer',
    january: 'Styczeń',
    february: 'Luty',
    march: 'Marzec',
    april: 'Kwiecień',
    may: 'Maj',
    june: 'Czerwiec',
    july: 'Lipiec',
    august: 'Sierpień',
    september: 'Wrzesień',
    october: 'Październik',
    november: 'Listopad',
    december: 'Grudzień',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/RRRR',
    timeFormatSlashesMMDDYYYY: 'MM/DD/RRRR',
    timeFormatSlashesDDMMYY: 'DD/MM/RR',
    timeFormatSlashesMMDDYY: 'MM/DD/RR',
    timeFormatDotsDDMYY: 'DD.M.RR',
    timeFormatDotsMDDYY: 'M.DD.RR',
    timeFormatDashesYYYYMMDD: 'RRRR-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM RRRR',
    timeFormatHHMMSS: 'GG:MM:SS',
    timeFormatHHMMSSAmPm: 'GG:MM:SS AM/PM',
};
