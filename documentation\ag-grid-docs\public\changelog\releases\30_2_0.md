#### 26th Sept 2023 - Grid v30.2.0 (Charts v8.2.0)

#### Feature Highlights:

-   Column Filtering
    -   AG-8999 - Add Advanced Filter Builder UI for building filter expressions (See [Advanced Filter](https://www.ag-grid.com/javascript-data-grid/filter-advanced/#advanced-filter-builder))
    -   AG-5565 - <PERSON><PERSON> providing custom matching logic to Quick Filter (See [Quick Filter](https://www.ag-grid.com/javascript-data-grid/filter-quick/#quick-filter-parser))
-   Cell Editing
    -   AG-9175 - Add typing, filter, async population of values to Rich Cell Editor (See [Rich Select Editor](https://www.ag-grid.com/javascript-data-grid/provided-cell-editors/))
-   Row Grouping
    -   AG-1962 - Allow showing Group Footer Rows based on custom logic (See [Row Grouping - Group Footers](https://www.ag-grid.com/javascript-data-grid/grouping-footers/#dynamically-display-group-total-rows))
-   Miscellaneous
    -   AG-7953 - Make `groupIncludeTotalFooter` and `groupIncludeFooter` properties reactive
    -   AG-7275 - Make `suppressDragLeaveHidesColumns` property reactive
    -   AG-5989 - Make `treeData` property reactive
    -   AG-684 - Add new `gridPreDestroyed` event

#### Deprecations:

Grid Options

-   `suppressParentsInRowNode` is deprecated without replacement.

ICellRendererParams

-   `rowIndex` is now deprecated, use `ICellRendererParams.node.rowIndex` instead.
