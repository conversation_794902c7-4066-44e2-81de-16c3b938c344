@mixin output {
    .ag-root-wrapper,
    .ag-sticky-top,
    .ag-sticky-bottom,
    .ag-dnd-ghost {
        background-color: var(--ag-background-color);
    }

    .ag-sticky-bottom {
        border-top: var(--ag-row-border-style) var(--ag-row-border-color) var(--ag-row-border-width);
    }

    .ag-root-wrapper,
    .ag-popup {
        --ag-indentation-level: 0;
    }

    [class*='ag-theme-'] {
        -webkit-font-smoothing: antialiased;
        font-family: var(--ag-font-family);
        font-size: var(--ag-font-size);
        line-height: normal;
        color: var(--ag-foreground-color);
    }
}
