name: 'Run Framework Example Tests'
description: 'Run Framework Example Tests'
inputs:
  framework:
    description: 'Framework to use'
    required: true
    default: 'invalid-framework'
runs:
  using: "composite"
  steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 1

    - name: Restore all-examples-cached.json
      uses: actions/cache@v4
      with:
        path: ./testing/ag-grid-docs/e2e/.cache/all-examples-cached.json
        key: all-examples-${{ hashFiles('./testing/ag-grid-docs/e2e/.cache/all-examples-cached.json') }}

    - name: Install Playwright Browsers
      working-directory: testing/ag-grid-docs
      run: npm install && npx playwright install chromium firefox webkit --with-deps --only-shell
      shell: bash

    - name: ${{ inputs.framework }}
      working-directory: testing/ag-grid-docs
      env:
        FRAMEWORK: ${{ inputs.framework }}
      run: npx playwright test framework-examples.spec.ts --shard=${{ matrix.shardIndex }}/${{ matrix.shardTotal }}
      shell: bash

    - name: Upload Test Report
      id: upload-report
      uses: actions/upload-artifact@v4
      if: ${{ !cancelled() }}
      with:
        name: playwright-report-${{ inputs.framework }}-${{ matrix.shardIndex }}-of-${{ matrix.shardTotal }}
        path: reports/
        retention-days: ${{env.DEFAULT_RETENTION_DAYS}}