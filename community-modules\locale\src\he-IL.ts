/**
 * Please note:
 *
 * Translations are provided as an illustration only and are
 * not guaranteed to be accurate or error free.
 *
 * They are designed to show developers where to store their
 * chosen phrase or spelling variant in the target language.
 */

export const AG_GRID_LOCALE_IL = {
    // Set Filter
    selectAll: '(בחר הכל)',
    selectAllSearchResults: '(בחר את כל תוצאות החיפוש)',
    addCurrentSelectionToFilter: 'הוסף את הבחירה הנוכחית לסינון',
    searchOoo: 'חפש...',
    blanks: '(ריקים)',
    noMatches: 'אין התאמות',

    // Number Filter & Text Filter
    filterOoo: 'סינון...',
    equals: 'שווה',
    notEqual: 'לא שווה',
    blank: 'ריק',
    notBlank: 'לא ריק',
    empty: 'בחר אחד',

    // Number Filter
    lessThan: 'פחות מ',
    greaterThan: 'יותר מ',
    lessThanOrEqual: 'פחות או שווה ל',
    greaterThanOrEqual: 'יותר או שווה ל',
    inRange: 'בין',
    inRangeStart: 'מ',
    inRangeEnd: 'עד',

    // Text Filter
    contains: 'מכיל',
    notContains: 'אינו מכיל',
    startsWith: 'מתחיל ב',
    endsWith: 'מסתיים ב',

    // Date Filter
    dateFormatOoo: 'yyyy-mm-dd',
    before: 'לפני',
    after: 'אחרי',

    // Filter Conditions
    andCondition: 'וגם',
    orCondition: 'או',

    // Filter Buttons
    applyFilter: 'החל',
    resetFilter: 'אפס',
    clearFilter: 'נקה',
    cancelFilter: 'בטל',

    // Filter Titles
    textFilter: 'מסנן טקסט',
    numberFilter: 'מסנן מספר',
    dateFilter: 'מסנן תאריך',
    setFilter: 'מסנן סט',

    // Group Column Filter
    groupFilterSelect: 'בחר שדה:',

    // New Filter Tool Panel
    filterSummaryInactive: 'הוא (הכל)',
    filterSummaryContains: 'מכיל',
    filterSummaryNotContains: 'לא מכיל',
    filterSummaryTextEquals: 'שווה',
    filterSummaryTextNotEqual: 'לא שווה',
    filterSummaryStartsWith: 'מתחיל ב',
    filterSummaryEndsWith: 'מסתיים ב',
    filterSummaryBlank: 'ריק',
    filterSummaryNotBlank: 'לא ריק',
    filterSummaryEquals: '=',
    filterSummaryNotEqual: '!=',
    filterSummaryGreaterThan: '>',
    filterSummaryGreaterThanOrEqual: '>=',
    filterSummaryLessThan: '<',
    filterSummaryLessThanOrEqual: '<=',
    filterSummaryInRange: 'בין',
    filterSummaryInRangeValues: '(${variable}, ${variable})',
    filterSummaryTextQuote: '"${variable}"',
    filterSummaryListInactive: 'הוא (הכל)',
    filterSummaryListSeparator: ', ',
    filterSummaryListShort: 'הוא (${variable})',
    filterSummaryListLong: 'הוא (${variable}) ו-${variable} נוספים',
    addFilterCard: 'הוסף מסנן',
    agTextColumnFilterDisplayName: 'מסנן פשוט',
    agNumberColumnFilterDisplayName: 'מסנן פשוט',
    agDateColumnFilterDisplayName: 'מסנן פשוט',
    agSetColumnFilterDisplayName: 'מסנן בחירה',
    agMultiColumnFilterDisplayName: 'מסנן משולב',
    addFilterPlaceholder: 'חפש עמודות...',

    // Advanced Filter
    advancedFilterContains: 'מכיל',
    advancedFilterNotContains: 'לא מכיל',
    advancedFilterTextEquals: 'שווה ל',
    advancedFilterTextNotEqual: 'לא שווה ל',
    advancedFilterStartsWith: 'מתחיל ב',
    advancedFilterEndsWith: 'מסתיים ב',
    advancedFilterBlank: 'ריק',
    advancedFilterNotBlank: 'לא ריק',
    advancedFilterEquals: '=',
    advancedFilterNotEqual: '!=',
    advancedFilterGreaterThan: '>',
    advancedFilterGreaterThanOrEqual: '>=',
    advancedFilterLessThan: '<',
    advancedFilterLessThanOrEqual: '<=',
    advancedFilterTrue: 'אמת',
    advancedFilterFalse: 'שקר',
    advancedFilterAnd: 'וגם',
    advancedFilterOr: 'או',
    advancedFilterApply: 'החל',
    advancedFilterBuilder: 'בונה',
    advancedFilterValidationMissingColumn: 'עמודה חסרה',
    advancedFilterValidationMissingOption: 'אפשרות חסרה',
    advancedFilterValidationMissingValue: 'ערך חסר',
    advancedFilterValidationInvalidColumn: 'עמודה לא נמצאה',
    advancedFilterValidationInvalidOption: 'אפשרות לא נמצאה',
    advancedFilterValidationMissingQuote: 'לערך חסר ציטוט סיום',
    advancedFilterValidationNotANumber: 'הערך אינו מספר',
    advancedFilterValidationInvalidDate: 'הערך אינו תאריך חוקי',
    advancedFilterValidationMissingCondition: 'חסר תנאי',
    advancedFilterValidationJoinOperatorMismatch: 'המפעילים בתנאי חייבים להיות אותו דבר',
    advancedFilterValidationInvalidJoinOperator: 'אופרטור צרוף לא נמצא',
    advancedFilterValidationMissingEndBracket: 'חסר סוגר סיום',
    advancedFilterValidationExtraEndBracket: 'יותר מדי סוגרי סיום',
    advancedFilterValidationMessage: 'יש שגיאה בביטוי. ${variable} - ${variable}.',
    advancedFilterValidationMessageAtEnd: 'יש שגיאה בביטוי. ${variable} בסוף הביטוי.',
    advancedFilterBuilderTitle: 'מסנן מתקדם',
    advancedFilterBuilderApply: 'החל',
    advancedFilterBuilderCancel: 'בטל',
    advancedFilterBuilderAddButtonTooltip: 'הוסף מסנן או קבוצה',
    advancedFilterBuilderRemoveButtonTooltip: 'הסר',
    advancedFilterBuilderMoveUpButtonTooltip: 'העבר מעלה',
    advancedFilterBuilderMoveDownButtonTooltip: 'העבר מטה',
    advancedFilterBuilderAddJoin: 'הוסף קבוצה',
    advancedFilterBuilderAddCondition: 'הוסף מסנן',
    advancedFilterBuilderSelectColumn: 'בחר עמודה',
    advancedFilterBuilderSelectOption: 'בחר אפשרות',
    advancedFilterBuilderEnterValue: 'הכנס ערך...',
    advancedFilterBuilderValidationAlreadyApplied: 'המסנן הנוכחי כבר הוחל.',
    advancedFilterBuilderValidationIncomplete: 'לא כל התנאים הושלמו.',
    advancedFilterBuilderValidationSelectColumn: 'חובה לבחור עמודה.',
    advancedFilterBuilderValidationSelectOption: 'חובה לבחור אפשרות.',
    advancedFilterBuilderValidationEnterValue: 'חובה להזין ערך.',

    // Editor Validation Errors
    minDateValidation: 'התאריך חייב להיות אחרי ${variable}',
    maxDateValidation: 'התאריך חייב להיות לפני ${variable}',
    maxLengthValidation: 'חייב להיות ${variable} תווים או פחות.',
    minValueValidation: 'חייב להיות גדול או שווה ל-${variable}',
    maxValueValidation: 'חייב להיות קטן או שווה ל-${variable}',
    invalidSelectionValidation: 'בחירה לא חוקית.',
    tooltipValidationErrorSeparator: '. ',

    // Side Bar
    columns: 'עמודות',
    filters: 'מסננים',

    // columns tool panel
    pivotMode: 'מצב סיכום',
    groups: 'קבוצות שורות',
    rowGroupColumnsEmptyMessage: 'גרור לכאן כדי להגדיר קבוצות שורות',
    values: 'ערכים',
    valueColumnsEmptyMessage: 'גרור לכאן כדי לבצע חישוב',
    pivots: 'תוויות עמודות',
    pivotColumnsEmptyMessage: 'גרור לכאן כדי להגדיר תוויות עמודות',

    // Header of the Default Group Column
    group: 'קבוצה',

    // Row Drag
    rowDragRow: 'שורה',
    rowDragRows: 'שורות',

    // Other
    loadingOoo: 'טוען...',
    loadingError: 'שגיאה',
    noRowsToShow: 'אין שורות להציג',
    enabled: 'מופעל',

    // Menu
    pinColumn: 'נעל עמודה',
    pinLeft: 'נעל לשמאל',
    pinRight: 'נעל לימין',
    noPin: 'ללא נעילה',
    valueAggregation: 'צבירה ערכית',
    noAggregation: 'ללא',
    autosizeThisColumn: 'כוונון אוטומטי של עמודה זו',
    autosizeAllColumns: 'כוונון אוטומטי של כל העמודות',
    groupBy: 'קבץ לפי',
    ungroupBy: 'בטל קיבוץ לפי',
    ungroupAll: 'בטל קיבוץ של הכול',
    addToValues: 'הוסף את ${variable} לערכים',
    removeFromValues: 'הסר את ${variable} מהערכים',
    addToLabels: 'הוסף את ${variable} לתוויות',
    removeFromLabels: 'הסר את ${variable} מהתוויות',
    resetColumns: 'אפס עמודות',
    expandAll: 'הרחב את כל קבוצות השורות',
    collapseAll: 'סגור את כל קבוצות השורות',
    copy: 'העתק',
    ctrlC: 'Ctrl+C',
    ctrlX: 'Ctrl+X',
    copyWithHeaders: 'העתק עם כותרות',
    copyWithGroupHeaders: 'העתק עם כותרות קבוצה',
    cut: 'גזור',
    paste: 'הדבק',
    ctrlV: 'Ctrl+V',
    export: 'ייצוא',
    csvExport: 'ייצוא CSV',
    excelExport: 'ייצוא Excel',
    columnFilter: 'סינון עמודה',
    columnChooser: 'בחר עמודות',
    chooseColumns: 'בחר עמודות',
    sortAscending: 'מיין בסדר עולה',
    sortDescending: 'מיין בסדר יורד',
    sortUnSort: 'נקה מיון',

    // Enterprise Menu Aggregation and Status Bar
    sum: 'סכום',
    first: 'ראשון',
    last: 'אחרון',
    min: 'מינ',
    max: 'מקס',
    none: 'אין',
    count: 'ספירה',
    avg: 'ממוצע',
    filteredRows: 'מסונן',
    selectedRows: 'נבחר',
    totalRows: 'סה"כ שורות',
    totalAndFilteredRows: 'שורות',
    more: 'עוד',
    to: 'אל',
    of: 'של',
    page: 'דף',
    pageLastRowUnknown: '?',
    nextPage: 'עמוד הבא',
    lastPage: 'עמוד אחרון',
    firstPage: 'עמוד ראשון',
    previousPage: 'עמוד קודם',
    pageSizeSelectorLabel: 'גודל עמוד:',
    footerTotal: 'סה"כ',
    statusBarLastRowUnknown: '?',
    scrollColumnIntoView: 'גלול ${variable} לתוך התצוגה',

    // Pivoting
    pivotColumnGroupTotals: 'סה״כ',

    // Enterprise Menu (Charts)
    pivotChartAndPivotMode: 'תרשים ציר ומצב ציר',
    pivotChart: 'תרשים ציר',
    chartRange: 'טווח תרשים',
    columnChart: 'עמודה',
    groupedColumn: 'מקובץ',
    stackedColumn: 'מוּערָם',
    normalizedColumn: '100% מוערם',
    barChart: 'בר',
    groupedBar: 'מקובץ',
    stackedBar: 'מוּערָם',
    normalizedBar: '100% מוערם',
    pieChart: 'עוגה',
    pie: 'עוגה',
    donut: 'דונאט',
    lineChart: 'קו',
    stackedLine: 'מוערם',
    normalizedLine: 'מוערם 100%',
    xyChart: 'X Y (Scatter)',
    scatter: 'פיזור',
    bubble: 'בועה',
    areaChart: 'אזור',
    area: 'אזור',
    stackedArea: 'מוּערָם',
    normalizedArea: '100% מוערם',
    histogramChart: 'היסטוגרמה',
    polarChart: 'פולארי',
    radarLine: 'קו מכ״ם',
    radarArea: 'אזור מכ״ם',
    nightingale: 'נייטינגייל',
    radialColumn: 'עמודה רדיאלית',
    radialBar: 'בר רדיאלי',
    statisticalChart: 'סטטיסטי',
    boxPlot: 'עלילת קופסה',
    rangeBar: 'טווח בר',
    rangeArea: 'טווח אזור',
    hierarchicalChart: 'היררכי',
    treemap: 'מפת עץ',
    sunburst: 'התפרצות שמש',
    specializedChart: 'מומחה',
    waterfall: 'מפל',
    heatmap: 'מפת חום',
    combinationChart: 'תרשים משולב',
    columnLineCombo: 'עמודה וקו',
    AreaColumnCombo: 'אזור ועמודה',

    // Charts
    pivotChartTitle: 'תרשים ציר',
    rangeChartTitle: 'תרשים טווח',
    settings: 'תרשים',
    data: 'הגדרות',
    format: 'התאמה אישית',
    categories: 'קטגוריות',
    defaultCategory: '(ללא)',
    series: 'סדרות',
    switchCategorySeries: 'החלף קטגוריה / סדרה',
    categoryValues: 'ערכי קטגוריה',
    seriesLabels: 'תוויות סדרה',
    aggregate: 'קיבוץ נתונים',
    xyValues: 'ערכי X Y',
    paired: 'מצב זיווג',
    axis: 'ציר',
    xAxis: 'ציר אופקי',
    yAxis: 'ציר אנכי',
    polarAxis: 'ציר קוטב',
    radiusAxis: 'ציר רדיוס',
    navigator: 'מנווט',
    zoom: 'זום',
    animation: 'אנימציה',
    crosshair: 'קו חוצה',
    color: 'צבע',
    thickness: 'עובי',
    preferredLength: 'אורך מועדף',
    xType: 'סוג X',
    axisType: 'סוג ציר',
    automatic: 'אוטומטי',
    category: 'קטגוריה',
    number: 'מספר',
    time: 'זמן',
    timeFormat: 'פורמט זמן',
    autoRotate: 'סיבוב אוטומטי',
    labelRotation: 'סיבוב תווית',
    circle: 'מעגל',
    polygon: 'מצולע',
    square: 'ריבוע',
    cross: 'צלב',
    diamond: 'יהלום',
    plus: 'פלוס',
    triangle: 'משולש',
    heart: 'לב',
    orientation: 'כיוון',
    fixed: 'קבוע',
    parallel: 'מקביל',
    perpendicular: 'ניצב',
    radiusAxisPosition: 'מיקום',
    ticks: 'סימני קו',
    gridLines: 'קווי רשת',
    width: 'רוחב',
    height: 'גובה',
    length: 'אורך',
    padding: 'ריפוד',
    spacing: 'רווח',
    chartStyle: 'סגנון תרשים',
    title: 'כותרת',
    chartTitles: 'כותרות',
    chartTitle: 'כותרת תרשים',
    chartSubtitle: 'כותרת משנה',
    horizontalAxisTitle: 'כותרת ציר אופקי',
    verticalAxisTitle: 'כותרת ציר אנכי',
    polarAxisTitle: 'כותרת ציר קוטב',
    titlePlaceholder: 'כותרת תרשים',
    background: 'רקע',
    font: 'גופן',
    weight: 'עובי',
    top: 'למעלה',
    right: 'ימין',
    bottom: 'למטה',
    left: 'שמאל',
    labels: 'תוויות',
    calloutLabels: 'תוויות הסבר',
    sectorLabels: 'תוויות מגזר',
    positionRatio: 'יחס מיקום',
    size: 'גודל',
    shape: 'צורה',
    minSize: 'גודל מינימלי',
    maxSize: 'גודל מקסימלי',
    legend: 'מקרא',
    position: 'מיקום',
    markerSize: 'גודל סימן',
    markerStroke: 'קוטר סימן',
    markerPadding: 'ריפוד סימן',
    itemSpacing: 'רווח פריטים',
    itemPaddingX: 'ריפוד פריטים X',
    itemPaddingY: 'ריפוד פריטים Y',
    layoutHorizontalSpacing: 'רווח אופקי',
    layoutVerticalSpacing: 'רווח אנכי',
    strokeWidth: 'עובי קו',
    offset: 'היסט',
    offsets: 'היסטים',
    tooltips: 'הסברים',
    callout: 'הסבר',
    markers: 'סימנים',
    shadow: 'צל',
    blur: 'טשטוש',
    xOffset: 'היסט X',
    yOffset: 'היסט Y',
    lineWidth: 'עובי קו',
    lineDash: 'קו מקווקו',
    lineDashOffset: 'קיזוז קו מקווקו',
    scrollingZoom: 'גלילה',
    scrollingStep: 'שלב גלילה',
    selectingZoom: 'בחירת זום',
    durationMillis: 'משך (מילישניות)',
    crosshairLabel: 'תווית',
    crosshairSnap: 'הצמד לצומת',
    normal: 'רגיל',
    bold: 'מודגש',
    italic: 'נטוי',
    boldItalic: 'מודגש ונטוי',
    predefined: 'מוגדר מראש',
    fillOpacity: 'אטימות מילוי',
    strokeColor: 'צבע קו',
    strokeOpacity: 'אטימות קו',
    miniChart: 'תרשים קטן',
    histogramBinCount: 'מספר אזורים',
    connectorLine: 'קו חיבור',
    seriesItems: 'פריטי סדרה',
    seriesItemType: 'סוג פריט',
    seriesItemPositive: 'חיובי',
    seriesItemNegative: 'שלילי',
    seriesItemLabels: 'תוויות פריט',
    columnGroup: 'עמודה',
    barGroup: 'פס',
    pieGroup: 'עוגה',
    lineGroup: 'קו',
    scatterGroup: 'X Y (פיזור)',
    areaGroup: 'שטח',
    polarGroup: 'קוטב',
    statisticalGroup: 'סטטיסטי',
    hierarchicalGroup: 'היררכי',
    specializedGroup: 'מתמחה',
    combinationGroup: 'שילוב',
    groupedColumnTooltip: 'מקובץ',
    stackedColumnTooltip: 'מוערם',
    normalizedColumnTooltip: 'מוערם 100%',
    groupedBarTooltip: 'מקובץ',
    stackedBarTooltip: 'מוערם',
    normalizedBarTooltip: 'מוערם 100%',
    pieTooltip: 'עוגה',
    donutTooltip: 'סופגנייה',
    lineTooltip: 'קו',
    stackedLineTooltip: 'מוערם',
    normalizedLineTooltip: '100% מוערם',
    groupedAreaTooltip: 'שטח מחולק',
    stackedAreaTooltip: 'שטח מוערם',
    normalizedAreaTooltip: 'שטח מוערם 100%',
    scatterTooltip: 'פיזור',
    bubbleTooltip: 'בועה',
    histogramTooltip: 'היסטוגרמה',
    radialColumnTooltip: 'עמודה רדיאלית',
    radialBarTooltip: 'פס רדיאלי',
    radarLineTooltip: 'קו רדאר',
    radarAreaTooltip: 'שטח רדאר',
    nightingaleTooltip: 'נייטינגייל',
    rangeBarTooltip: 'טווח פס',
    rangeAreaTooltip: 'טווח שטח',
    boxPlotTooltip: 'תרשים קופסה',
    treemapTooltip: 'תרשים עץ',
    sunburstTooltip: 'התפרצות שמש',
    waterfallTooltip: 'מפל',
    heatmapTooltip: 'מפת חום',
    columnLineComboTooltip: 'עמודה & קו',
    areaColumnComboTooltip: 'שטח & עמודה',
    customComboTooltip: 'שילוב מותאם אישית',
    innerRadius: 'רדיוס פנימי',
    startAngle: 'זווית התחלה',
    endAngle: 'זווית סיום',
    reverseDirection: 'כיוון הפוך',
    groupPadding: 'ריפוד קבוצה',
    seriesPadding: 'ריפוד סדרה',
    tile: 'אריח',
    whisker: 'דוּאַזְרַת',
    cap: 'כִּפָּה',
    capLengthRatio: 'יחס אורך',
    labelPlacement: 'מיקום תווית',
    inside: 'בפנים',
    outside: 'בחוץ',
    noDataToChart: 'אין נתונים להצגה.',
    pivotChartRequiresPivotMode: 'תרשים ציר דורש הפעלת מצב ציר.',
    chartSettingsToolbarTooltip: 'תפריט',
    chartLinkToolbarTooltip: 'מקושר לגריד',
    chartUnlinkToolbarTooltip: 'מנותק מהגריד',
    chartDownloadToolbarTooltip: 'הורד תרשים',
    chartMenuToolbarTooltip: 'תפריט',
    chartEdit: 'ערוך תרשים',
    chartAdvancedSettings: 'הגדרות מתקדמות',
    chartLink: 'קישור לגריד',
    chartUnlink: 'נתק מהגריד',
    chartDownload: 'הורד תרשים',
    histogramFrequency: 'תדירות',
    seriesChartType: 'סוג תרשים סדרה',
    seriesType: 'סוג סדרה',
    secondaryAxis: 'ציר משני',
    seriesAdd: 'הוסף סדרה',
    categoryAdd: 'הוסף קטגוריה',
    bar: 'פס',
    column: 'עמודה',
    histogram: 'היסטוגרמה',
    advancedSettings: 'הגדרות מתקדמות',
    direction: 'כיוון',
    horizontal: 'אופקי',
    vertical: 'אנכי',
    seriesGroupType: 'סוג קבוצת סדרה',
    groupedSeriesGroupType: 'מקובץ',
    stackedSeriesGroupType: 'מוערם',
    normalizedSeriesGroupType: 'מוערם 100%',
    legendEnabled: 'מופעל',
    invalidColor: 'ערך צבע לא חוקי',
    groupedColumnFull: 'עמודה מקובצת',
    stackedColumnFull: 'עמודה מוערמת',
    normalizedColumnFull: 'עמודה מוערמת 100%',
    groupedBarFull: 'פס מקובץ',
    stackedBarFull: 'פס מוערם',
    normalizedBarFull: 'פס מוערם 100%',
    stackedAreaFull: 'שטח מוערם',
    normalizedAreaFull: 'שטח מוערם 100%',
    customCombo: 'שילוב מותאם אישית',
    funnel: 'משפך',
    coneFunnel: 'משפך קוני',
    pyramid: 'פירמידה',
    funnelGroup: 'משפך',
    funnelTooltip: 'משפך',
    coneFunnelTooltip: 'משפך קוני',
    pyramidTooltip: 'פירמידה',
    dropOff: 'נשירה',
    stageLabels: 'תוויות שלבים',
    reverse: 'היפוך',

    // ARIA
    ariaAdvancedFilterBuilderItem: '${variable}. רמה ${variable}. לחץ על ENTER כדי לערוך.',
    ariaAdvancedFilterBuilderItemValidation: '${variable}. רמה ${variable}. ${variable} לחץ על ENTER כדי לערוך.',
    ariaAdvancedFilterBuilderList: 'רשימת בונה פילטר מתקדם',
    ariaAdvancedFilterBuilderFilterItem: 'תנאי סינון',
    ariaAdvancedFilterBuilderGroupItem: 'קבוצת סינון',
    ariaAdvancedFilterBuilderColumn: 'עמודה',
    ariaAdvancedFilterBuilderOption: 'אפשרות',
    ariaAdvancedFilterBuilderValueP: 'ערך',
    ariaAdvancedFilterBuilderJoinOperator: 'מפעיל חיבור',
    ariaAdvancedFilterInput: 'קלט סינון מתקדם',
    ariaChecked: 'מסומן',
    ariaColumn: 'עמודה',
    ariaColumnGroup: 'קבוצת עמודות',
    ariaColumnFiltered: 'עמודה מסוננת',
    ariaColumnSelectAll: 'החלפת נראות כל העמודות',
    ariaDateFilterInput: 'קלט סינון תאריך',
    ariaDefaultListName: 'רשימה',
    ariaFilterColumnsInput: 'קלט סינון עמודות',
    ariaFilterFromValue: 'סנן מערך',
    ariaFilterInput: 'קלט סינון',
    ariaFilterList: 'רשימת סינון',
    ariaFilterToValue: 'סנן לערך',
    ariaFilterValue: 'ערך סינון',
    ariaFilterMenuOpen: 'פתח תפריט סינון',
    ariaFilteringOperator: 'מפעיל סינון',
    ariaHidden: 'נסתר',
    ariaIndeterminate: 'בלתי קבוע',
    ariaInputEditor: 'עורך קלט',
    ariaMenuColumn: 'לחץ ALT למטה כדי לפתוח תפריט עמודה',
    ariaFilterColumn: 'לחץ CTRL ENTER כדי לפתוח סינון',
    ariaRowDeselect: 'לחץ על SPACE כדי לבטל את בחירת שורה זו',
    ariaHeaderSelection: 'עמודה עם בחירת כותרת',
    ariaSelectAllCells: 'לחץ על מקש רווח כדי לבחור בכל התאים',
    ariaRowSelectAll: 'לחץ על Space כדי להחליף את בחירת כל השורות',
    ariaRowToggleSelection: 'לחץ על Space כדי להחליף את בחירת השורה',
    ariaRowSelect: 'לחץ על SPACE כדי לבחור שורה זו',
    ariaRowSelectionDisabled: 'בחירת שורות מושבתת עבור שורה זו',
    ariaSearch: 'חיפוש',
    ariaSortableColumn: 'לחץ על ENTER למיון',
    ariaToggleVisibility: 'לחץ על SPACE כדי להחליף נראות',
    ariaToggleCellValue: 'לחץ על SPACE כדי להחליף ערך תא',
    ariaUnchecked: 'לא מסומן',
    ariaVisible: 'גלוי',
    ariaSearchFilterValues: 'ערכי חיפוש סינון',
    ariaPageSizeSelectorLabel: 'גודל עמוד',
    ariaChartMenuClose: 'סגור תפריט עריכת תרשים',
    ariaChartSelected: 'נבחר',
    ariaSkeletonCellLoadingFailed: 'הטעינת השורה נכשלה',
    ariaSkeletonCellLoading: 'נתוני השורה נטענים',
    ariaDeferSkeletonCellLoading: 'התא נטען',

    // ARIA for Batch Edit
    ariaPendingChange: 'שינוי בהמתנה',

    // ARIA Labels for Drop Zones
    ariaRowGroupDropZonePanelLabel: 'קבוצות שורות',
    ariaValuesDropZonePanelLabel: 'ערכים',
    ariaPivotDropZonePanelLabel: 'תוויות עמודות',
    ariaDropZoneColumnComponentDescription: 'לחץ DELETE כדי להסיר',
    ariaDropZoneColumnValueItemDescription: 'לחץ ENTER כדי לשנות את סוג האגרגציה',
    ariaDropZoneColumnGroupItemDescription: 'לחץ ENTER כדי למיין',

    // used for aggregate drop zone, format: {aggregation}{ariaDropZoneColumnComponentAggFuncSeparator}{column name}
    ariaDropZoneColumnComponentAggFuncSeparator: ' של ',
    ariaDropZoneColumnComponentSortAscending: 'עולה',
    ariaDropZoneColumnComponentSortDescending: 'יורד',
    ariaLabelDialog: 'דיאלוג',
    ariaLabelColumnMenu: 'תפריט עמודה',
    ariaLabelColumnFilter: 'מסנן עמודה',
    ariaLabelSelectField: 'שדה בחירה',

    // Cell Editor
    ariaLabelCellEditor: 'עורך תא',
    ariaValidationErrorPrefix: 'אימות עורך תא',
    ariaLabelLoadingContextMenu: 'טוען תפריט קיצור דרך',

    // aria labels for rich select
    ariaLabelRichSelectField: 'שדה בחירה עשיר',
    ariaLabelRichSelectToggleSelection: 'לחץ על מקש הרווח כדי להחליף בחירה',
    ariaLabelRichSelectDeselectAllItems: 'לחץ על מקש מחיקה כדי לבטל את הבחירה בכל הפריטים',
    ariaLabelRichSelectDeleteSelection: 'לחץ על מקש מחיקה כדי לבטל את בחירת הפריט',
    ariaLabelTooltip: 'תווית',
    ariaLabelContextMenu: 'תפריט הקשר',
    ariaLabelSubMenu: 'תפריט משנה',
    ariaLabelAggregationFunction: 'פונקציית צבירה',
    ariaLabelAdvancedFilterAutocomplete: 'השלמה אוטומטית לסינון מתקדם',
    ariaLabelAdvancedFilterBuilderAddField: 'הוסף שדה בבונה הסינון המתקדם',
    ariaLabelAdvancedFilterBuilderColumnSelectField: 'בחר שדה עמודה בבונה הסינון המתקדם',
    ariaLabelAdvancedFilterBuilderOptionSelectField: 'בחר שדה אפשרות בבונה הסינון המתקדם',
    ariaLabelAdvancedFilterBuilderJoinSelectField: 'בחר שדה אופרטור הצטרפות בבונה הסינון המתקדם',

    // ARIA Labels for the Side Bar
    ariaColumnPanelList: 'רשימת עמודות',
    ariaFilterPanelList: 'רשימת מסננים',

    // ARIA labels for new Filters Tool Panel
    ariaLabelAddFilterField: 'הוסף שדה סינון',
    ariaLabelFilterCardDelete: 'מחק סינון',
    ariaLabelFilterCardHasEdits: 'יש עריכות',

    // Number Format (Status Bar, Pagination Panel)
    thousandSeparator: ',',
    decimalSeparator: '.',

    // Data types
    true: 'True',
    false: 'False',
    invalidDate: 'תאריך לא חוקי',
    invalidNumber: 'מספר לא חוקי',
    january: 'ינואר',
    february: 'פברואר',
    march: 'מרץ',
    april: 'אפריל',
    may: 'מאי',
    june: 'יוני',
    july: 'יולי',
    august: 'אוגוסט',
    september: 'ספטמבר',
    october: 'אוקטובר',
    november: 'נובמבר',
    december: 'דצמבר',

    // Time formats
    timeFormatSlashesDDMMYYYY: 'DD/MM/YYYY',
    timeFormatSlashesMMDDYYYY: 'MM/DD/YYYY',
    timeFormatSlashesDDMMYY: 'DD/MM/YY',
    timeFormatSlashesMMDDYY: 'MM/DD/YY',
    timeFormatDotsDDMYY: 'DD.M.YY',
    timeFormatDotsMDDYY: 'M.DD.YY',
    timeFormatDashesYYYYMMDD: 'YYYY-MM-DD',
    timeFormatSpacesDDMMMMYYYY: 'DD MMMM YYYY',
    timeFormatHHMMSS: 'שעה:דקה:שנייה',
    timeFormatHHMMSSAmPm: 'שעה:דקה:שנייה לפנה״צ/אחה״צ',
};
