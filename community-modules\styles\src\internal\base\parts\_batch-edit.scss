@use 'ag';

@mixin output {
    .ag-cell-batch-edit {
        background-color: var(--ag-cell-batch-edit-background-color);
        color: var(--ag-cell-batch-edit-text-color);
    }

    .ag-cell.ag-cell-inline-editing {
        // use gradient image to overlay input background onto background, so that
        // if the user supplies a semi-transparent background the content of the
        // cell below won't be visible
        background-color: var(--ag-background-color);
        background-image: linear-gradient(0deg, var(--ag-input-background-color), var(--ag-input-background-color));
    }

    .ag-row-batch-edit {
        background-color: var(--ag-row-batch-edit-background-color);
        color: var(--ag-row-batch-edit-text-color);
    }
}
