@use 'ag';

@mixin output {
    .ag-chart {
        position: relative;
        display: flex;
        width: 100%;
        height: 100%;
    }

    .ag-chart-components-wrapper {
        position: relative;
        display: flex;
        flex: 1 1 auto;
    }

    .ag-chart-canvas-wrapper {
        position: relative;
        flex: 1 1 auto;
    }

    .ag-chart-menu {
        position: absolute;
        top: 16px;
        display: flex;
        flex-direction: column;

        @include ag.unthemed-rtl(
            (
                right: 20px,
            )
        );
    }

    .ag-chart-docked-container {
        position: relative;
        min-width: var(--ag-chart-menu-panel-width);
    }

    .ag-chart-menu-hidden ~ .ag-chart-docked-container {
        display: none;
    }

    .ag-chart-tabbed-menu {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }

    .ag-chart-tabbed-menu-header {
        flex: none;
        @include ag.selectable(none);
        cursor: default;
    }

    .ag-chart-tabbed-menu-body {
        display: flex;
        flex: 1 1 auto;
        align-items: stretch;
        overflow: hidden;
    }

    .ag-chart-tab {
        width: 100%;
        overflow: hidden;
        overflow-y: auto;
    }

    .ag-chart-settings {
        overflow-x: hidden;
    }

    .ag-chart-settings-wrapper {
        position: relative;
        flex-direction: column;
        width: 100%;
        height: 100%;
        display: flex;
        overflow: hidden;
    }

    .ag-chart-settings-nav-bar {
        display: flex;
        align-items: center;
        width: 100%;
        height: 30px;
        padding: 0 10px;
        @include ag.selectable(none);
    }

    .ag-chart-settings-card-selector {
        display: flex;
        align-items: center;
        justify-content: space-around;
        flex: 1 1 auto;
        height: 100%;
        padding: 0 10px; // increase size of click area for better UX
    }

    .ag-chart-settings-card-item {
        cursor: pointer;
        width: 10px;
        height: 10px;
        background-color: #000; // exception to the rule of no visual styles in functional stylesheet - without a background this element would be invisible
        position: relative;
        &.ag-not-selected {
            opacity: 0.2;
        }

        &::before {
            // make expanded click area
            content: ' ';
            display: block;
            position: absolute;
            background-color: transparent;
            left: 50%;
            top: 50%;
            margin-left: -10px;
            margin-top: -10px;
            width: 20px;
            height: 20px;
        }
    }

    .ag-chart-settings-prev,
    .ag-chart-settings-next {
        position: relative;
        flex: none;

        &:focus-within {
            box-shadow: var(--ag-input-focus-box-shadow);
            border-radius: 1px;
        }
    }

    .ag-chart-settings-prev-button,
    .ag-chart-settings-next-button {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        cursor: pointer;
        opacity: 0;
    }

    .ag-chart-settings-mini-charts-container {
        position: relative;
        flex: 1 1 auto;
        overflow-x: hidden;
        overflow-y: auto;
    }

    .ag-chart-settings-mini-wrapper {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        flex-direction: column;
        width: 100%;
        min-height: 100%;
        overflow: hidden;

        &.ag-animating {
            transition: left 0.3s;
            transition-timing-function: ease-in-out;
        }
    }

    .ag-chart-mini-thumbnail {
        cursor: pointer;
    }

    .ag-chart-mini-thumbnail-canvas {
        display: block;
    }

    .ag-chart-data-wrapper,
    .ag-chart-format-wrapper,
    .ag-chart-advanced-settings-wrapper {
        display: flex;
        flex-direction: column;
        position: relative;
        @include ag.selectable(none);
        padding-bottom: 16px;
    }

    .ag-chart-data-wrapper,
    .ag-chart-advanced-settings-wrapper {
        height: 100%;
        overflow-y: auto;
    }

    .ag-chart-advanced-settings {
        background-color: var(--ag-control-panel-background-color);
    }

    .ag-chart-advanced-settings-wrapper,
    .ag-chart-advanced-settings {
        width: 100%;
    }

    .ag-chart-advanced-settings-wrapper {
        padding-bottom: 0;
    }

    .ag-chart-data-section,
    .ag-chart-format-section,
    .ag-chart-advanced-settings-section {
        display: flex;
        margin: 0;
    }

    .ag-chart-advanced-settings-section {
        padding-top: var(--ag-grid-size);
        padding-bottom: var(--ag-grid-size);
        &:not(:last-child) {
            border-bottom: 1px solid var(--ag-secondary-border-color);
        }
    }

    .ag-chart-empty-text {
        display: flex;
        top: 0;
        width: 100%;
        height: 100%;
        align-items: center;
        justify-content: center;
        background-color: var(--ag-background-color);
    }

    .ag-chart {
        .ag-chart-menu-wrapper {
            .ag-chart-menu {
                display: flex;
                flex-direction: row;
                top: 8px;
                gap: 20px;
                width: auto;

                @include ag.unthemed-rtl(
                    (
                        right: calc(var(--ag-cell-horizontal-padding) + var(--ag-grid-size) - 4px),
                        justify-content: right,
                    )
                );
            }
        }
    }

    .ag-charts-font-size-color {
        display: flex;
        align-self: stretch;
        justify-content: space-between;
    }

    .ag-charts-data-group-item {
        position: relative;
    }

    .ag-charts-data-group-item:not(:last-child) {
        margin-bottom: var(--ag-grid-size);
    }

    .ag-chart-menu {
        border-radius: var(--ag-card-radius);
        background: var(--ag-background-color);
    }

    .ag-chart-menu-icon {
        opacity: 0.5;
        margin: 2px 0;
        cursor: pointer;
        border-radius: var(--ag-card-radius);
        color: var(--ag-secondary-foreground-color);

        &:hover {
            opacity: 1;
        }
    }

    .ag-chart-menu-toolbar-button {
        border: 0;
        background-color: unset;
        padding: 0 2px;
        border-radius: 1px;
    }

    .ag-chart-mini-thumbnail {
        border: 1px solid var(--ag-secondary-border-color);
        border-radius: 5px;
        padding: 1px;

        &.ag-selected {
            border-color: var(--ag-minichart-selected-chart-color);
            border-width: 2px;
            padding: unset;
        }

        &:focus-visible {
            outline: none;
            border-color: var(--ag-minichart-selected-chart-color);
            box-shadow: var(--ag-input-focus-box-shadow);
        }
    }

    .ag-chart-settings-card-item {
        background: var(--ag-foreground-color);
        width: 8px;
        height: 8px;
        border-radius: 4px;

        &.ag-selected {
            background-color: var(--ag-minichart-selected-page-color);
        }
    }

    .ag-chart-data-column-drag-handle {
        margin-left: var(--ag-grid-size);
    }

    .ag-charts-settings-group-title-bar,
    .ag-charts-data-group-title-bar,
    .ag-charts-format-top-level-group-title-bar {
        border-top: var(--ag-borders-secondary) var(--ag-secondary-border-color);
        position: relative;
    }

    .ag-charts-advanced-settings-top-level-group-title-bar {
        position: relative;
        background-color: unset;
    }

    @include ag.keyboard-focus((ag-charts-data-group-title-bar, ag-charts-format-top-level-group-title-bar), 4px);

    .ag-charts-data-group-title-bar .ag-charts-data-group-title,
    .ag-charts-format-top-level-group-title-bar .ag-charts-format-top-level-group-title {
        cursor: pointer;
    }

    .ag-charts-data-group-container {
        padding: calc(var(--ag-widget-container-vertical-padding) * 0.5) var(--ag-widget-container-horizontal-padding);

        .ag-charts-data-group-item:not(.ag-charts-format-sub-level-group):not(.ag-pill-select):not(.ag-select) {
            height: var(--ag-list-item-height);
        }

        .ag-charts-data-group-item.ag-picker-field {
            margin-top: var(--ag-grid-size);
        }

        @include ag.list-item-hovered();
    }

    .ag-charts-format-top-level-group-container,
    .ag-charts-advanced-settings-top-level-group-container {
        @include ag.unthemed-rtl(
            (
                margin-left: calc(var(--ag-grid-size) * 2),
            )
        );
        padding: var(--ag-grid-size);
    }

    .ag-charts-format-top-level-group-item,
    .ag-charts-advanced-settings-top-level-group-item {
        margin: var(--ag-grid-size) 0;
    }

    .ag-charts-format-sub-level-group-container {
        @include ag.vertical-widget-container();
    }

    .ag-charts-format-sub-level-no-header-group-container > * {
        margin-bottom: var(--ag-widget-vertical-spacing);
    }

    .ag-charts-format-sub-level-group-container .ag-charts-format-sub-level-group-item.ag-font-panel-no-header {
        margin: 0;
    }

    .ag-charts-settings-group-container {
        padding: var(--ag-grid-size);
        row-gap: 8px;

        display: grid;
        grid-template-columns: 60px 1fr 60px 1fr 60px;

        .ag-chart-mini-thumbnail:nth-child(3n + 1) {
            grid-column: 1;
        }
        .ag-chart-mini-thumbnail:nth-child(3n + 2) {
            grid-column: 3;
        }
        .ag-chart-mini-thumbnail:nth-child(3n + 3) {
            grid-column: 5;
        }
    }

    .ag-chart-menu-panel {
        @include ag.unthemed-rtl(
            (
                border-left: solid 1px var(--ag-border-color),
            )
        );
        background-color: var(--ag-control-panel-background-color);
    }
}
