name: Benchmark

on:
  issue_comment:
    types: [ created ]
  workflow_dispatch:
    inputs:
      notify:
        description: 'Notify Slack channel'
        type: boolean
        default: false
        required: true
      force_scheduled:
        description: 'Run the workflow as if it was scheduled'
        type: boolean
        default: false
      force_pr_id:
        description: 'PR ID to update (used for debugging this workflow)'
        type: number
        required: false
      force_jira_ticket:
        description: 'Force create a Jira ticket even if the run is not scheduled'
        type: boolean
        default: false
        required: true
  schedule:
    - cron: '0 0 * * *' # Run daily at midnight UTC

env:
  NX_NO_CLOUD: true
  NX_BRANCH: ${{ github.ref }}
  BRANCH_NAME: ${{ github.head_ref || github.ref_name }}
  SHARP_IGNORE_GLOBAL_LIBVIPS: true
  YARN_REGISTRY: "http://************:4873"
  CI: true
  PR_ID: ${{ inputs.force_pr_id || github.event.issue.number }}
  COMMENT_FILE: ./comment.md
  SLACK_FILE: ./slack.json
  SLACK_FILE_SNIPPET: ./slack-snippet.md
  JIRA_DESCRIPTION_FILE: ./jira-description.txt
  JIRA_FINGERPRINT_FILE: ./jira-fingerprint.json
  DEFAULT_RETENTION_DAYS: 30

permissions:
  contents: read

jobs:
  benchmark:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
      issues: write
    if: github.event_name == 'workflow_dispatch' || (github.event_name == 'issue_comment' && github.event.comment.body == '/benchmarks') || github.event_name == 'schedule'
    env:
      JOB_URL: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
    steps:
      - name: Notify start
        id: notify-start
        uses: actions/github-script@v7
        continue-on-error: true
        if: ${{ env.PR_ID }}
        with:
          result-encoding: string
          script: |
            const { data: comment } = await github.rest.issues.createComment({
              issue_number: process.env.PR_ID ? parseInt(process.env.PR_ID, 10) : context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🛎 Started a benchmarking job: ${process.env.JOB_URL}`
            })
            return comment.id;

      - name: Assign correct Branch name
        id: get-branch
        env:
          REPO: ${{ github.repository }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          echo PR_ID=$PR_ID
          echo github.event.issue.number=${{ github.event.issue.number }}
          if [[ !!"${{ steps.notify-start.outputs.result }}" ]] ; then
            # For PR comments, get the branch from the PR
            echo "branch=$(gh pr view $PR_ID --repo $REPO --json headRefName --jq '.headRefName')" >> $GITHUB_OUTPUT
          elif [[ "${{ github.event_name }}" == "schedule" ]] ; then
            # For scheduled workflow dispatch, use the 'latest' branch
            echo "branch=latest" >> $GITHUB_OUTPUT
          else
            # For manual workflow dispatch, use the current branch
            echo "branch=${{ github.ref_name }}" >> $GITHUB_OUTPUT
          fi

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 1
          ref: ${{ steps.get-branch.outputs.branch }}

      - name: Fetch Refs
        run: |
          git fetch origin --depth 1 latest
          git fetch origin --depth 1 tag latest-success

      - name: Notify Progress
        uses: actions/github-script@v7
        continue-on-error: true
        if: ${{ steps.notify-start.outputs.result }}
        with:
          script: |
            github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: ${{ steps.notify-start.outputs.result }},
              body: `🏗 Running setup: ${process.env.JOB_URL}`
            })

      - name: Setup
        id: setup
        uses: ./.github/actions/setup-nx
        with:
          cache_mode: rw

      - name: Notify Progress
        uses: actions/github-script@v7
        continue-on-error: true
        if: ${{ steps.notify-start.outputs.result }}
        with:
          script: |
            github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: ${{ steps.notify-start.outputs.result }},
              body: `🏗 Building umd packages: ${process.env.JOB_URL}`
            })

      - name: Build umd packages
        id: build-umd
        if: ${{ steps.setup.outcome == 'success' || steps.setup.outcome == 'skipped' }}
        run: |
          echo "Building UMD packages..."
          npx nx run-many -t build --projects=ag-grid-{enterprise,community}

      - name: Notify Progress
        uses: actions/github-script@v7
        continue-on-error: true
        if: ${{ steps.notify-start.outputs.result }}
        with:
          script: |
            github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: ${{ steps.notify-start.outputs.result }},
              body: `🏗 Installing Playwright Browsers: ${process.env.JOB_URL}`
            })

      - name: Install Playwright Browsers
        id: install-browsers
        if: ${{ steps.build-umd.outcome == 'success' }}
        run: |
          git status;
          npx playwright install --with-deps

      - name: Notify Progress
        uses: actions/github-script@v7
        continue-on-error: true
        if: ${{ steps.notify-start.outputs.result }}
        with:
          script: |
            github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: ${{ steps.notify-start.outputs.result }},
              body: `🏃 Running benchmarks:️ ${process.env.JOB_URL}`
            })

      - name: playwright test
        id: playwright-test
        continue-on-error: true
        if: ${{ steps.install-browsers.outcome == 'success' }}
        env:
          IS_SCHEDULED: ${{ github.event_name == 'schedule' || inputs.force_scheduled }}
        run: |
          if [[ "$IS_SCHEDULED" == "true" ]] ; then
            npx playwright test -c ./testing/performance/playwright.config.ts -g ".*cron.spec.ts" ./testing/performance/e2e
          else
            npx playwright test -c ./testing/performance/playwright.config.ts -gv ".*cron.spec.ts" ./testing/performance/e2e
          fi

      - name: Upload Benchmark Report
        id: upload-report
        uses: actions/upload-artifact@v4
        if: ${{ !cancelled() }}
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: ${{env.DEFAULT_RETENTION_DAYS}}

      - name: Prepare messages
        id: prepare-messages
        if: ${{ steps.upload-report.outputs.artifact-url }}
        env:
          REPORT_URL: ${{ steps.upload-report.outputs.artifact-url }}
          SLACK_CHANNEL: '#ci-grid-gate'
          SLACK_ICON: 'https://avatars.slack-edge.com/2020-11-25/1527503386626_319578f21381f9641cd8_192.png'
          SLACK_USERNAME: 'ag-grid CI'
          IS_SUCCESS: ${{ steps.playwright-test.outcome == 'success' || '' }}
          SLACK_FILE: ${{ env.SLACK_FILE }}
          SLACK_FILE_SNIPPET: ${{ env.SLACK_FILE_SNIPPET }}
          COMMENT_FILE: ${{ env.COMMENT_FILE }}
          JIRA_DESCRIPTION_FILE: ${{ env.JIRA_DESCRIPTION_FILE }}
          JIRA_FINGERPRINT_FILE: ${{ env.JIRA_FINGERPRINT_FILE }}
          JOB_URL: ${{ env.JOB_URL }}
        run: |
          node ./scripts/test/convert-playwright-output-to-slack-msg.mjs
          
          {
            echo 'JIRA_FINGERPRINT<<EOF'
            cat $JIRA_FINGERPRINT_FILE
            echo 'EOF'
          } >> $GITHUB_ENV
          {
            echo 'JIRA_DESCRIPTION<<EOF'
            cat $JIRA_DESCRIPTION_FILE
            echo 'EOF'
          } >> $GITHUB_ENV

      - name: Upload Snippet
        id: upload-snippet
        uses: actions/upload-artifact@v4
        if: ${{ steps.prepare-messages.outcome == 'success' }}
        with:
          name: slack-snippet
          path: ${{ env.SLACK_FILE_SNIPPET }}
          retention-days: ${{ env.DEFAULT_RETENTION_DAYS }}

      - name: Attach snippet
        id: attach-snippet
        if: ${{ steps.upload-snippet.outputs.artifact-url }}
        env:
          SNIPPET_URL: ${{ steps.upload-snippet.outputs.artifact-url }}
          JIRA_DESCRIPTION_FILE: ${{ env.JIRA_DESCRIPTION_FILE }}
          SLACK_FILE: ${{ env.SLACK_FILE }}
          COMMENT_FILE: ${{ env.COMMENT_FILE }}
        run: |
          node ./scripts/test/attach-snippet.mjs
          echo 'JIRA_DESCRIPTION_FILE:'
          cat $JIRA_DESCRIPTION_FILE
          echo ':JIRA_DESCRIPTION_FILE'
          {
            echo 'JIRA_DESCRIPTION<<EOF'
            cat $JIRA_DESCRIPTION_FILE
            echo 'EOF'
          } >> $GITHUB_ENV
          echo 'SLACK_FILE:'
          cat $SLACK_FILE
          echo ':SLACK_FILE'
          {
            echo 'SLACK_MESSAGE_BLOCKS<<EOF'
            cat $SLACK_FILE
            echo 'EOF'
          } >> $GITHUB_ENV

      - name: Slack Notification
        continue-on-error: true
        if: ${{ steps.upload-snippet.outputs.artifact-url && (github.event_name == 'schedule' || inputs.notify) }}
        uses: ./.github/actions/slack-integration
        with:
          SLACK_WEBHOOK: ${{ secrets.CI_GATE_WEBHOOK }}
          SLACK_MESSAGE_BLOCKS: ${{ env.SLACK_MESSAGE_BLOCKS }}

      - name: Create Jira Ticket
        uses: ./.github/actions/jira-integration
        continue-on-error: true
        if: ${{ steps.attach-snippet.outcome == 'success' && (github.event_name == 'schedule' || inputs.force_jira_ticket) }}
        with:
          JIRA_FINGERPRINT: ${{ env.JIRA_FINGERPRINT }}
          JIRA_API_AUTH: ${{ secrets.JIRA_API_AUTH }}
          WORKFLOW: ${{ github.workflow }}
          JIRA_DESCRIPTION: ${{ env.JIRA_DESCRIPTION }}
          JIRA_SUMMARY: "[NR] CI/CD workflow '${{ github.workflow }}' has detected a slowdown in grid performance"
          IS_SUCCESS: ${{ steps.playwright-test.outcome == 'success' || '' }}

      - name: Notify End
        uses: actions/github-script@v7
        continue-on-error: true
        if: ${{ steps.notify-start.outputs.result && steps.attach-snippet.outcome == 'success' }}
        env:
          COMMENT_ID: ${{ steps.notify-start.outputs.result }}
          IS_SUCCESS: ${{ steps.playwright-test.outcome == 'success' || '' }}
        with:
          script: |
            const owner = context.repo.owner;
            const repo = context.repo.repo;
            const comment_id = process.env.COMMENT_ID;
            const pull_number = process.env.PR_ID;
            const body = require('fs').readFileSync(process.env.COMMENT_FILE, 'utf8');

            if (process.env.IS_SUCCESS) {
              await github.rest.issues.updateComment({ owner, repo, comment_id, body });
            } else {
              await github.rest.issues.deleteComment({ owner, repo, comment_id });
              await github.rest.pulls.createReview({ owner, repo, pull_number, event: "REQUEST_CHANGES", body });
            }
